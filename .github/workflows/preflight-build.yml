name: Preflight Build Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:
    inputs:
      target:
        description: 'Build target'
        required: true
        default: 'prod'
        type: choice
        options:
          - dev
          - prod
          - docker
      run_tests:
        description: 'Run tests after build'
        required: false
        default: true
        type: boolean
      deploy:
        description: 'Deploy after successful build'
        required: false
        default: false
        type: boolean

env:
  NODE_VERSION: '18'
  PNPM_VERSION: '8'

jobs:
  preflight-validation:
    name: Preflight Validation
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install pnpm
        run: npm install -g pnpm@${{ env.PNPM_VERSION }}
        
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
        
      - name: Run quick preflight
        run: pnpm run preflight:quick
        
      - name: Check environment template
        run: |
          if [ ! -f .env.example ]; then
            echo "❌ .env.example not found"
            exit 1
          fi
          echo "✅ Environment template exists"

  build-development:
    name: Development Build
    runs-on: ubuntu-latest
    needs: preflight-validation
    if: github.event.inputs.target == 'dev' || (github.event.inputs.target == '' && github.ref != 'refs/heads/main')
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          
      - name: Install pnpm
        run: npm install -g pnpm@${{ env.PNPM_VERSION }}
        
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
        
      - name: Run development preflight build
        run: pnpm run build:preflight:dev
        
      - name: Validate build output
        run: pnpm run build:validate
        
      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: development-build
          path: |
            dist/
            build-report-*.json
          retention-days: 7

  build-production:
    name: Production Build
    runs-on: ubuntu-latest
    needs: preflight-validation
    if: github.event.inputs.target == 'prod' || github.ref == 'refs/heads/main'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          
      - name: Install pnpm
        run: npm install -g pnpm@${{ env.PNPM_VERSION }}
        
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
        
      - name: Run production preflight build
        run: pnpm run build:preflight:prod
        
      - name: Validate build output (strict)
        run: pnpm run build:validate:strict
        
      - name: Run tests
        if: github.event.inputs.run_tests == 'true' || github.event.inputs.run_tests == ''
        run: pnpm run test --passWithNoTests
        
      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: production-build
          path: |
            dist/
            build-report-*.json
            build-validation-*.json
          retention-days: 30
          
      - name: Upload build reports
        uses: actions/upload-artifact@v4
        with:
          name: build-reports
          path: |
            *.json
          retention-days: 30

  build-docker:
    name: Docker Build
    runs-on: ubuntu-latest
    needs: preflight-validation
    if: github.event.inputs.target == 'docker' || github.ref == 'refs/heads/main'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        
      - name: Setup Node.js (for preflight)
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          
      - name: Install pnpm
        run: npm install -g pnpm@${{ env.PNPM_VERSION }}
        
      - name: Install dependencies (for preflight)
        run: pnpm install --frozen-lockfile
        
      - name: Run Docker preflight
        run: ./scripts/docker-preflight.sh
        
      - name: Build Docker image
        run: ./scripts/docker-build.sh --tag=discord-bot-energex:${{ github.sha }} --test
        
      - name: Test Docker image
        run: |
          # Test that the image can start
          docker run -d --name test-container \
            -e NODE_ENV=production \
            -e PORT=8080 \
            -e DATABASE_URL=postgresql://test:test@localhost:5432/test \
            discord-bot-energex:${{ github.sha }}
          
          # Wait for startup
          sleep 10
          
          # Check if container is running
          if docker ps | grep test-container; then
            echo "✅ Docker container test passed"
            docker stop test-container
            docker rm test-container
          else
            echo "❌ Docker container test failed"
            docker logs test-container
            docker rm test-container
            exit 1
          fi
          
      - name: Upload Docker reports
        uses: actions/upload-artifact@v4
        with:
          name: docker-build-reports
          path: |
            docker-build-report-*.json
          retention-days: 30

  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: [build-production]
    if: github.ref == 'refs/heads/main'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: production-build
          
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          
      - name: Install pnpm
        run: npm install -g pnpm@${{ env.PNPM_VERSION }}
        
      - name: Security audit
        run: |
          pnpm audit --audit-level moderate || true
          
      - name: Check for sensitive data in build
        run: |
          echo "🔍 Scanning for sensitive data in build artifacts..."
          
          # Check for common sensitive patterns
          if grep -r -i "password\|secret\|token\|api_key" dist/ || true; then
            echo "⚠️  Potential sensitive data found in build"
          else
            echo "✅ No sensitive data found in build"
          fi
          
      - name: Dependency license check
        run: |
          echo "🔍 Checking dependency licenses..."
          pnpm licenses list || true

  deployment-ready:
    name: Deployment Ready
    runs-on: ubuntu-latest
    needs: [build-production, security-scan]
    if: github.ref == 'refs/heads/main' && github.event.inputs.deploy == 'true'
    
    steps:
      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: production-build
          
      - name: Verify deployment readiness
        run: |
          echo "🚀 Verifying deployment readiness..."
          
          # Check required files
          if [ ! -f dist/src/main.js ]; then
            echo "❌ Main application file missing"
            exit 1
          fi
          
          if [ ! -f dist/deployment-manifest.json ]; then
            echo "⚠️  Deployment manifest missing"
          fi
          
          echo "✅ Deployment artifacts verified"
          
      - name: Create deployment package
        run: |
          echo "📦 Creating deployment package..."
          
          # Create deployment archive
          tar -czf discord-bot-energex-${{ github.sha }}.tar.gz \
            dist/ \
            package.json \
            pnpm-lock.yaml \
            scripts/production-start.sh
            
          echo "✅ Deployment package created"
          
      - name: Upload deployment package
        uses: actions/upload-artifact@v4
        with:
          name: deployment-package
          path: discord-bot-energex-${{ github.sha }}.tar.gz
          retention-days: 90

  notify-results:
    name: Notify Results
    runs-on: ubuntu-latest
    needs: [build-development, build-production, build-docker, security-scan]
    if: always()
    
    steps:
      - name: Build Status Summary
        run: |
          echo "📊 Build Pipeline Summary"
          echo "========================="
          echo "Development Build: ${{ needs.build-development.result || 'skipped' }}"
          echo "Production Build: ${{ needs.build-production.result || 'skipped' }}"
          echo "Docker Build: ${{ needs.build-docker.result || 'skipped' }}"
          echo "Security Scan: ${{ needs.security-scan.result || 'skipped' }}"
          
          # Determine overall status
          if [[ "${{ needs.build-production.result }}" == "failure" ]] || [[ "${{ needs.build-docker.result }}" == "failure" ]]; then
            echo "❌ Build pipeline failed"
            exit 1
          else
            echo "✅ Build pipeline completed successfully"
          fi
