
> discord-bot-nestjs@1.0.0 build
> nest build

[1m[31mERROR[39m[22m in [1m./src/api/guilds/types/discord.types.ts:40:18[39m[22m
[1mTS2430: Interface 'EnhancedDiscordGuild' incorrectly extends interface 'Omit<APIGuild, "region">'.
  Types of property 'roles' are incompatible.
    Type 'EnhancedDiscordRole[]' is not assignable to type 'APIRole[]'.
      Type 'EnhancedDiscordRole' is not assignable to type 'APIRole'.
        Property 'flags' is optional in type 'EnhancedDiscordRole' but [1m[33mrequired[39m[22m[1m in type 'APIRole'.
    38 |  * Enhanced Discord Guild representation with full type safety
    39 |  */
  > 40 | export interface EnhancedDiscordGuild extends Omit<APIGuild, 'region'> {
       |                  ^^^^^^^^^^^^^^^^^^^^
    41 |   // Core guild properties
    42 |   id: Snowflake;
    43 |   name: string;[39m[22m

[1m[31mERROR[39m[22m in [1m./src/api/guilds/types/index.ts:27:1[39m[22m
[1mTS2308: Module './discord.types' has already exported a member named 'GuildEventType'. Consider explicitly re-exporting to resolve the ambiguity.
    25 |
    26 | // ====== GUILD EVENT TYPES ======
  > 27 | export * from './guild-events.types';
       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    28 |
    29 | // ====== TYPE UTILITIES ======
    30 |[39m[22m

[1m[31mERROR[39m[22m in [1m./src/api/guilds/types/index.ts:27:1[39m[22m
[1mTS2308: Module './member-management.types' has already exported a member named 'MemberLeaveReason'. Consider explicitly re-exporting to resolve the ambiguity.
    25 |
    26 | // ====== GUILD EVENT TYPES ======
  > 27 | export * from './guild-events.types';
       | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    28 |
    29 | // ====== TYPE UTILITIES ======
    30 |[39m[22m

[1m[31mERROR[39m[22m in [1m./src/api/guilds/types/index.ts:433:86[39m[22m
[1mTS2345: Argument of type '"CreateInstantInvite" | "KickMembers" | "BanMembers" | "Administrator" | "ManageChannels" | "ManageGuild" | "AddReactions" | "ViewAuditLog" | "PrioritySpeaker" | "Stream" | ... 39 more ... | "UseExternalApps"' is not assignable to parameter of type '"ManageMessages" | "MuteMembers" | "DeafenMembers" | "MoveMembers" | "ModerateMembers" | "UseSlashCommands"'.
  Type '"CreateInstantInvite"' is not assignable to type '"ManageMessages" | "MuteMembers" | "DeafenMembers" | "MoveMembers" | "ModerateMembers" | "UseSlashCommands"'.
    431 |     if (permissions.includes('Administrator')) return 6;
    432 |     if (permissions.some(p => ['ManageGuild', 'ManageRoles', 'ManageChannels'].includes(p))) return 5;
  > 433 |     if (permissions.some(p => PERMISSION_HIERARCHIES.MODERATION_PERMISSIONS.includes(p))) return 3;
        |                                                                                      ^
    434 |     if (permissions.some(p => PERMISSION_HIERARCHIES.BASIC_PERMISSIONS.includes(p))) return 1;
    435 |     return 0;
    436 |   },[39m[22m

[1m[31mERROR[39m[22m in [1m./src/api/guilds/types/index.ts:434:81[39m[22m
[1mTS2345: Argument of type '"CreateInstantInvite" | "KickMembers" | "BanMembers" | "Administrator" | "ManageChannels" | "ManageGuild" | "AddReactions" | "ViewAuditLog" | "PrioritySpeaker" | "Stream" | ... 39 more ... | "UseExternalApps"' is not assignable to parameter of type '"AddReactions" | "ViewChannel" | "SendMessages" | "ReadMessageHistory" | "UseExternalEmojis" | "Connect" | "Speak"'.
  Type '"CreateInstantInvite"' is not assignable to type '"AddReactions" | "ViewChannel" | "SendMessages" | "ReadMessageHistory" | "UseExternalEmojis" | "Connect" | "Speak"'.
    432 |     if (permissions.some(p => ['ManageGuild', 'ManageRoles', 'ManageChannels'].includes(p))) return 5;
    433 |     if (permissions.some(p => PERMISSION_HIERARCHIES.MODERATION_PERMISSIONS.includes(p))) return 3;
  > 434 |     if (permissions.some(p => PERMISSION_HIERARCHIES.BASIC_PERMISSIONS.includes(p))) return 1;
        |                                                                                 ^
    435 |     return 0;
    436 |   },
    437 |[39m[22m

[1m[31mERROR[39m[22m in [1m./src/api/guilds/types/member-management.types.ts:55:18[39m[22m
[1mTS2430: Interface 'MemberInfo' incorrectly extends interface 'Omit<EnhancedDiscordMember, "permissions">'.
  Types of property 'voice' are incompatible.
    Type 'MemberVoiceInfo' is missing the following properties from type 'DiscordVoiceState': userId, deaf, mute, selfDeaf, and 3 more.
    53 |  * Comprehensive member information with metadata
    54 |  */
  > 55 | export interface MemberInfo extends Omit<EnhancedDiscordMember, 'permissions'> {
       |                  ^^^^^^^^^^
    56 |   // Extended member properties
    57 |   guildId: Snowflake;
    58 |   displayName: string;[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/database/redis-database.service.ts:157:39[39m[22m
[1mTS2344: Type 'T' does not satisfy the constraint 'BaseEntity'.
    155 |   // Generic convenience wrappers expected by some feature services
    156 |   async findById<T = any>(entityType: string, id: string): Promise<T | null> {
  > 157 |     return this.redisService.findById<T>(entityType, id);
        |                                       ^
    158 |   }
    159 |
    160 |   async findMany<T = any>([39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/database/redis-database.service.ts:164:47[39m[22m
[1mTS2344: Type 'T' does not satisfy the constraint 'BaseEntity'.
    162 |     options: { limit?: number; offset?: number; orderBy?: { field: string; direction: 'ASC' | 'DESC' } } = {}
    163 |   ): Promise<{ data: T[] }> {
  > 164 |     const data = await this.redisService.find<T>(entityType, {
        |                                               ^
    165 |       limit: options.limit,
    166 |       offset: options.offset,
    167 |       orderBy: options.orderBy as any,[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/database/redis-database.service.ts:169:14[39m[22m
[1mTS4104: The type 'readonly T[]' is 'readonly' and cannot be assigned to the mutable type 'T[]'.
    167 |       orderBy: options.orderBy as any,
    168 |     } as any);
  > 169 |     return { data };
        |              ^^^^
    170 |   }
    171 |
    172 |   async findByIndex<T = any>([39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/database/redis-database.service.ts:178:50[39m[22m
[1mTS2344: Type 'T' does not satisfy the constraint 'BaseEntity'.
    176 |     options: { limit?: number; sortOrder?: 'asc' | 'desc' } = {}
    177 |   ): Promise<T[]> {
  > 178 |     const results = await this.redisService.find<T>(entityType, {
        |                                                  ^
    179 |       where: { [field]: value } as any,
    180 |       limit: options.limit,
    181 |     } as any);[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/database/redis-database.service.ts:191:5[39m[22m
[1mTS4104: The type 'readonly T[]' is 'readonly' and cannot be assigned to the mutable type 'T[]'.
    189 |       });
    190 |     }
  > 191 |     return results;
        |     ^^^^^^
    192 |   }
    193 |
    194 |   async search<T = any>([39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/database/redis-database.service.ts:200:46[39m[22m
[1mTS2344: Type 'T' does not satisfy the constraint 'BaseEntity'.
    198 |     options: { limit?: number } = {}
    199 |   ): Promise<T[]> {
  > 200 |     const all = await this.redisService.find<T>(entityType, { limit: options.limit } as any);
        |                                              ^
    201 |     if (!query) return all;
    202 |     const q = query.toLowerCase();
    203 |     return all.filter((e: any) => {[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/database/redis-database.service.ts:201:17[39m[22m
[1mTS4104: The type 'readonly T[]' is 'readonly' and cannot be assigned to the mutable type 'T[]'.
    199 |   ): Promise<T[]> {
    200 |     const all = await this.redisService.find<T>(entityType, { limit: options.limit } as any);
  > 201 |     if (!query) return all;
        |                 ^^^^^^
    202 |     const q = query.toLowerCase();
    203 |     return all.filter((e: any) => {
    204 |       const keys = fields.length > 0 ? fields : Object.keys(e ?? {});[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/database/redis-database.service.ts:210:37[39m[22m
[1mTS2344: Type 'T' does not satisfy the constraint 'BaseEntity'.
    208 |
    209 |   async create<T = any>(entityType: string, data: any, _opts?: any): Promise<T> {
  > 210 |     return this.redisService.create<T>(entityType, data);
        |                                     ^
    211 |   }
    212 |
    213 |   async update<T = any>(entityType: string, id: string, data: any, _opts?: any): Promise<T | null> {[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/database/redis-database.service.ts:214:37[39m[22m
[1mTS2344: Type 'T' does not satisfy the constraint 'BaseEntity'.
    212 |
    213 |   async update<T = any>(entityType: string, id: string, data: any, _opts?: any): Promise<T | null> {
  > 214 |     return this.redisService.update<T>(entityType, id, data);
        |                                     ^
    215 |   }
    216 |
    217 |   async increment(entityType: string, id: string, field: string, amount: number): Promise<number> {[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/database/redis/enhanced-redis-client.ts:22:14[39m[22m
[1mTS2420: Class 'EnhancedRedisClient' incorrectly implements interface 'RedisClient'.
  Property 'getHealth' is missing in type 'EnhancedRedisClient' but [1m[33mrequired[39m[22m[1m in type 'RedisClient'.
    20 |
    21 | @Injectable()
  > 22 | export class EnhancedRedisClient implements RedisClient, OnModuleInit, OnModuleDestroy {
       |              ^^^^^^^^^^^^^^^^^^^
    23 |   private readonly logger = new Logger(EnhancedRedisClient.name);
    24 |   private redis!: Redis;
    25 |   private subscriber!: Redis;[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/database/redis/enhanced-redis-client.ts:221:9[39m[22m
[1mTS2353: Object literal may only specify known properties, and 'checks' does not exist in type 'RedisHealthCheck'.
    219 |         uptime,
    220 |         version,
  > 221 |         checks,
        |         ^^^^^^
    222 |         timestamp: new Date()
    223 |       };
    224 |[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/database/redis/enhanced-redis-client.ts:234:9[39m[22m
[1mTS2353: Object literal may only specify known properties, and 'checks' does not exist in type 'RedisHealthCheck'.
    232 |         uptime: 0,
    233 |         version: 'unknown',
  > 234 |         checks: {
        |         ^^^^^^
    235 |           connectivity: false,
    236 |           memory: false,
    237 |           performance: false,[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/database/redis/enhanced-redis-client.ts:319:9[39m[22m
[1mTS2353: Object literal may only specify known properties, and 'keyspace' does not exist in type 'RedisPerformanceMetrics'.
    317 |         memory,
    318 |         connections,
  > 319 |         keyspace,
        |         ^^^^^^^^
    320 |         replication
    321 |       };
    322 |[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/database/redis/enhanced-redis-client.ts:374:96[39m[22m
[1mTS2554: Expected 1-3 arguments, but got 4.
    372 |       return result === 1;
    373 |     } catch ([1m[31merror[39m[22m[1m) {
  > 374 |       throw new RedisError(`[1m[31mFailed[39m[22m[1m to delete cache key ${key}`, 'DELETE_ERROR', 'cacheDelete', key);
        |                                                                                                ^^^
    375 |     }
    376 |   }
    377 |[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/database/redis/enhanced-redis-client.ts:390:5[39m[22m
[1mTS2741: Property 'delete' is missing in type 'RedisSessionStorageImpl' but [1m[33mrequired[39m[22m[1m in type 'RedisSessionStorage'.
    388 |   // Session storage implementation
    389 |   createSessionStorage(keyPrefix = 'session:'): RedisSessionStorage {
  > 390 |     return new RedisSessionStorageImpl(this.redis, keyPrefix, this.logger);
        |     ^^^^^^
    391 |   }
    392 |
    393 |   private handleMessage(channel: string, message: string): void {[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/database/redis/enhanced-redis-client.ts:679:15[39m[22m
[1mTS1345: An expression of type 'void' cannot be tested for truthiness.
    677 |         try {
    678 |           const result = listener(parsedMessage);
  > 679 |           if (result && typeof result === 'object' && 'then' in result) {
        |               ^^^^^^
    680 |             result.catch([1m[31merror[39m[22m[1m => {
    681 |               this.logger.[1m[31merror[39m[22m[1m(`[1m[31mError[39m[22m[1m in async message listener for channel ${channel}:`, [1m[31merror[39m[22m[1m);
    682 |             });[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/database/redis/enhanced-redis-client.ts:679:15[39m[22m
[1mTS1345: An expression of type 'void' cannot be tested for truthiness.
    677 |         try {
    678 |           const result = listener(parsedMessage);
  > 679 |           if (result && typeof result === 'object' && 'then' in result) {
        |               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    680 |             result.catch([1m[31merror[39m[22m[1m => {
    681 |               this.logger.[1m[31merror[39m[22m[1m(`[1m[31mError[39m[22m[1m in async message listener for channel ${channel}:`, [1m[31merror[39m[22m[1m);
    682 |             });[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/database/redis/enhanced-redis-client.ts:679:15[39m[22m
[1mTS1345: An expression of type 'void' cannot be tested for truthiness.
    677 |         try {
    678 |           const result = listener(parsedMessage);
  > 679 |           if (result && typeof result === 'object' && 'then' in result) {
        |               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    680 |             result.catch([1m[31merror[39m[22m[1m => {
    681 |               this.logger.[1m[31merror[39m[22m[1m(`[1m[31mError[39m[22m[1m in async message listener for channel ${channel}:`, [1m[31merror[39m[22m[1m);
    682 |             });[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/database/redis/enhanced-redis-client.ts:680:20[39m[22m
[1mTS2339: Property 'catch' does not exist on type 'never'.
    678 |           const result = listener(parsedMessage);
    679 |           if (result && typeof result === 'object' && 'then' in result) {
  > 680 |             result.catch([1m[31merror[39m[22m[1m => {
        |                    ^^^^^
    681 |               this.logger.[1m[31merror[39m[22m[1m(`[1m[31mError[39m[22m[1m in async message listener for channel ${channel}:`, [1m[31merror[39m[22m[1m);
    682 |             });
    683 |           }[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/database/redis/enhanced-redis-client.ts:704:15[39m[22m
[1mTS1345: An expression of type 'void' cannot be tested for truthiness.
    702 |         try {
    703 |           const result = listener(parsedMessage);
  > 704 |           if (result && typeof result === 'object' && 'then' in result) {
        |               ^^^^^^
    705 |             result.catch([1m[31merror[39m[22m[1m => {
    706 |               this.logger.[1m[31merror[39m[22m[1m(`[1m[31mError[39m[22m[1m in async pattern listener for pattern ${pattern}:`, [1m[31merror[39m[22m[1m);
    707 |             });[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/database/redis/enhanced-redis-client.ts:704:15[39m[22m
[1mTS1345: An expression of type 'void' cannot be tested for truthiness.
    702 |         try {
    703 |           const result = listener(parsedMessage);
  > 704 |           if (result && typeof result === 'object' && 'then' in result) {
        |               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    705 |             result.catch([1m[31merror[39m[22m[1m => {
    706 |               this.logger.[1m[31merror[39m[22m[1m(`[1m[31mError[39m[22m[1m in async pattern listener for pattern ${pattern}:`, [1m[31merror[39m[22m[1m);
    707 |             });[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/database/redis/enhanced-redis-client.ts:704:15[39m[22m
[1mTS1345: An expression of type 'void' cannot be tested for truthiness.
    702 |         try {
    703 |           const result = listener(parsedMessage);
  > 704 |           if (result && typeof result === 'object' && 'then' in result) {
        |               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    705 |             result.catch([1m[31merror[39m[22m[1m => {
    706 |               this.logger.[1m[31merror[39m[22m[1m(`[1m[31mError[39m[22m[1m in async pattern listener for pattern ${pattern}:`, [1m[31merror[39m[22m[1m);
    707 |             });[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/database/redis/enhanced-redis-client.ts:705:20[39m[22m
[1mTS2339: Property 'catch' does not exist on type 'never'.
    703 |           const result = listener(parsedMessage);
    704 |           if (result && typeof result === 'object' && 'then' in result) {
  > 705 |             result.catch([1m[31merror[39m[22m[1m => {
        |                    ^^^^^
    706 |               this.logger.[1m[31merror[39m[22m[1m(`[1m[31mError[39m[22m[1m in async pattern listener for pattern ${pattern}:`, [1m[31merror[39m[22m[1m);
    707 |             });
    708 |           }[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/database/redis/enhanced-redis-client.ts:726:7[39m[22m
[1mTS2420: Class 'RedisSessionStorageImpl' incorrectly implements interface 'RedisSessionStorage'.
  Property 'delete' is missing in type 'RedisSessionStorageImpl' but [1m[33mrequired[39m[22m[1m in type 'RedisSessionStorage'.
    724 |
    725 | // Session Storage Implementation
  > 726 | class RedisSessionStorageImpl implements RedisSessionStorage {
        |       ^^^^^^^^^^^^^^^^^^^^^^^
    727 |   constructor(
    728 |     private readonly redis: Redis,
    729 |     private readonly keyPrefix: string,[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/database/redis/enhanced-redis-client.ts:834:24[39m[22m
[1mTS1107: Jump target cannot cross function boundary.
    832 |         keys.forEach((key, index) => {
    833 |           const result = results[index];
  > 834 |           if (!result) continue;
        |                        ^^^^^^^^^
    835 |           const [err, value] = result;
    836 |           if (!err && value) {
    837 |             try {[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/database/repositories/base-redis.repository.ts:151:39[39m[22m
[1mTS2540: Cannot assign to 'limit' because it is a read-only property.
    149 |     const options: RedisSearchOptions = {};
    150 |     const where = (query as any).where ?? query;
  > 151 |     if ((query as any).limit) options.limit = (query as any).limit;
        |                                       ^^^^^
    152 |     if ((query as any).offset) options.offset = (query as any).offset;
    153 |
    154 |     if (where && Object.keys(where).length === 1) {[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/database/repositories/base-redis.repository.ts:152:40[39m[22m
[1mTS2540: Cannot assign to 'offset' because it is a read-only property.
    150 |     const where = (query as any).where ?? query;
    151 |     if ((query as any).limit) options.limit = (query as any).limit;
  > 152 |     if ((query as any).offset) options.offset = (query as any).offset;
        |                                        ^^^^^^
    153 |
    154 |     if (where && Object.keys(where).length === 1) {
    155 |       const [field, value] = Object.entries(where)[0];[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/database/repositories/index.ts:32:53[39m[22m
[1mTS2694: Namespace '"/home/<USER>/Discordbot-EnergeX/src/core/database/types"' has no exported member 'BaseEntity'.
    30 |    * Create a key generator for an entity
    31 |    */
  > 32 |   createKeyGenerator: <T extends import('../types').BaseEntity>(
       |                                                     ^^^^^^^^^^
    33 |     entityName: string,
    34 |     indexFields?: Array<keyof T>
    35 |   ): EntityKeyGenerator<T> => {[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/database/repositories/index.ts:35:6[39m[22m
[1mTS2304: Cannot find name 'EntityKeyGenerator'.
    33 |     entityName: string,
    34 |     indexFields?: Array<keyof T>
  > 35 |   ): EntityKeyGenerator<T> => {
       |      ^^^^^^^^^^^^^^^^^^
    36 |     const entityKey = entityName.toLowerCase();
    37 |     
    38 |     const generator: EntityKeyGenerator<T> = {[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/database/repositories/index.ts:38:22[39m[22m
[1mTS2304: Cannot find name 'EntityKeyGenerator'.
    36 |     const entityKey = entityName.toLowerCase();
    37 |     
  > 38 |     const generator: EntityKeyGenerator<T> = {
       |                      ^^^^^^^^^^^^^^^^^^
    39 |       primary: (id: string) => `${entityKey}:${id}`,
    40 |       pattern: `${entityKey}:*`,
    41 |       index: {} as Partial<Record<keyof T, string>>,[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/database/repositories/index.ts:59:45[39m[22m
[1mTS2552: Cannot find name 'RepositoryOptions'. [1m[32mDid you mean 'PositionOptions'?[39m[22m[1m
    57 |    * Create default repository options
    58 |    */
  > 59 |   createDefaultOptions: (overrides: Partial<RepositoryOptions> = {}): RepositoryOptions => ({
       |                                             ^^^^^^^^^^^^^^^^^
    60 |     enableCaching: false,
    61 |     defaultTTL: 3600,
    62 |     enableSoftDelete: false,[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/database/repositories/index.ts:59:71[39m[22m
[1mTS2552: Cannot find name 'RepositoryOptions'. [1m[32mDid you mean 'PositionOptions'?[39m[22m[1m
    57 |    * Create default repository options
    58 |    */
  > 59 |   createDefaultOptions: (overrides: Partial<RepositoryOptions> = {}): RepositoryOptions => ({
       |                                                                       ^^^^^^^^^^^^^^^^^
    60 |     enableCaching: false,
    61 |     defaultTTL: 3600,
    62 |     enableSoftDelete: false,[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/database/repositories/index.ts:75:30[39m[22m
[1mTS2552: Cannot find name 'RepositoryOptions'. [1m[32mDid you mean 'PositionOptions'?[39m[22m[1m
    73 |    * Validate repository options
    74 |    */
  > 75 |   validateOptions: (options: RepositoryOptions): boolean => {
       |                              ^^^^^^^^^^^^^^^^^
    76 |     if (options.defaultTTL && options.defaultTTL < 0) {
    77 |       throw new [1m[31mError[39m[22m[1m('Default TTL [1m[33mmust[39m[22m[1m be positive');
    78 |     }[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/database/repositories/index.ts:92:48[39m[22m
[1mTS2304: Cannot find name 'RepositoryFactory'.
    90 |  * Repository factory for creating repository instances
    91 |  */
  > 92 | export class RedisRepositoryFactory implements RepositoryFactory {
       |                                                ^^^^^^^^^^^^^^^^^
    93 |   constructor(private readonly redisClient: import('ioredis').Redis) {}
    94 |
    95 |   /**[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/database/repositories/index.ts:98:39[39m[22m
[1mTS2694: Namespace '"/home/<USER>/Discordbot-EnergeX/src/core/database/types"' has no exported member 'BaseEntity'.
     96 |    * Create a basic repository instance
     97 |    */
  >  98 |   create<T extends import('../types').BaseEntity>(
        |                                       ^^^^^^^^^^
     99 |     entityName: string,
    100 |     keyGenerator: EntityKeyGenerator<T>,
    101 |     options: RepositoryOptions = {}[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/database/repositories/index.ts:100:19[39m[22m
[1mTS2304: Cannot find name 'EntityKeyGenerator'.
     98 |   create<T extends import('../types').BaseEntity>(
     99 |     entityName: string,
  > 100 |     keyGenerator: EntityKeyGenerator<T>,
        |                   ^^^^^^^^^^^^^^^^^^
    101 |     options: RepositoryOptions = {}
    102 |   ): BaseRedisRepository<T> {
    103 |     const { BaseRedisRepositoryImpl } = require('./base-redis.repository');[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/database/repositories/index.ts:101:14[39m[22m
[1mTS2552: Cannot find name 'RepositoryOptions'. [1m[32mDid you mean 'PositionOptions'?[39m[22m[1m
     99 |     entityName: string,
    100 |     keyGenerator: EntityKeyGenerator<T>,
  > 101 |     options: RepositoryOptions = {}
        |              ^^^^^^^^^^^^^^^^^
    102 |   ): BaseRedisRepository<T> {
    103 |     const { BaseRedisRepositoryImpl } = require('./base-redis.repository');
    104 |     const redisClient = this.redisClient;[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/database/repositories/index.ts:102:6[39m[22m
[1mTS2304: Cannot find name 'BaseRedisRepository'.
    100 |     keyGenerator: EntityKeyGenerator<T>,
    101 |     options: RepositoryOptions = {}
  > 102 |   ): BaseRedisRepository<T> {
        |      ^^^^^^^^^^^^^^^^^^^
    103 |     const { BaseRedisRepositoryImpl } = require('./base-redis.repository');
    104 |     const redisClient = this.redisClient;
    105 |     return new (class extends BaseRedisRepositoryImpl<T> {[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/database/repositories/index.ts:115:47[39m[22m
[1mTS2694: Namespace '"/home/<USER>/Discordbot-EnergeX/src/core/database/types"' has no exported member 'BaseEntity'.
    113 |    * Create an extended repository instance with additional features
    114 |    */
  > 115 |   createExtended<T extends import('../types').BaseEntity>(
        |                                               ^^^^^^^^^^
    116 |     entityName: string,
    117 |     keyGenerator: EntityKeyGenerator<T>,
    118 |     options: RepositoryOptions = {}[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/database/repositories/index.ts:117:19[39m[22m
[1mTS2304: Cannot find name 'EntityKeyGenerator'.
    115 |   createExtended<T extends import('../types').BaseEntity>(
    116 |     entityName: string,
  > 117 |     keyGenerator: EntityKeyGenerator<T>,
        |                   ^^^^^^^^^^^^^^^^^^
    118 |     options: RepositoryOptions = {}
    119 |   ): ExtendedRedisRepository<T> {
    120 |     // This would be implemented with additional features like caching, bulk operations, etc.[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/database/repositories/index.ts:118:14[39m[22m
[1mTS2552: Cannot find name 'RepositoryOptions'. [1m[32mDid you mean 'PositionOptions'?[39m[22m[1m
    116 |     entityName: string,
    117 |     keyGenerator: EntityKeyGenerator<T>,
  > 118 |     options: RepositoryOptions = {}
        |              ^^^^^^^^^^^^^^^^^
    119 |   ): ExtendedRedisRepository<T> {
    120 |     // This would be implemented with additional features like caching, bulk operations, etc.
    121 |     throw new [1m[31mError[39m[22m[1m('Extended repository implementation not yet available');[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/database/repositories/index.ts:119:6[39m[22m
[1mTS2304: Cannot find name 'ExtendedRedisRepository'.
    117 |     keyGenerator: EntityKeyGenerator<T>,
    118 |     options: RepositoryOptions = {}
  > 119 |   ): ExtendedRedisRepository<T> {
        |      ^^^^^^^^^^^^^^^^^^^^^^^
    120 |     // This would be implemented with additional features like caching, bulk operations, etc.
    121 |     throw new [1m[31mError[39m[22m[1m('Extended repository implementation not yet available');
    122 |   }[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/database/repositories/user.repository.ts:4:10[39m[22m
[1mTS2305: Module '"../types"' has no exported member 'RepositoryOptions'.
    2 | import { CreateUser, UpdateUser, User, UserKeys } from '../entities/user.entity';
    3 | import { RedisService } from '../redis.service';
  > 4 | import { RepositoryOptions } from '../types';
      |          ^^^^^^^^^^^^^^^^^
    5 | import { BaseRedisRepositoryImpl } from './base-redis.repository';
    6 |
    7 | /**[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/multi-tenancy/organization.service.ts:23:35[39m[22m
[1mTS2339: Property 'settings' does not exist on type 'Organization'.
    21 |   tier?: OrganizationTier;
    22 |   ownerId: string;
  > 23 |   settings?: Partial<Organization['settings']>;
       |                                   ^^^^^^^^^^
    24 | }
    25 |
    26 | export interface UpdateOrganizationDto {[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/multi-tenancy/organization.service.ts:30:35[39m[22m
[1mTS2339: Property 'settings' does not exist on type 'Organization'.
    28 |   tier?: OrganizationTier;
    29 |   status?: OrganizationStatus;
  > 30 |   settings?: Partial<Organization['settings']>;
       |                                   ^^^^^^^^^^
    31 | }
    32 |
    33 | @Injectable()[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/multi-tenancy/organization.service.ts:38:46[39m[22m
[1mTS2304: Cannot find name 'OrganizationRepository'.
    36 |
    37 |   constructor(
  > 38 |     private readonly organizationRepository: OrganizationRepository,
       |                                              ^^^^^^^^^^^^^^^^^^^^^^
    39 |     private readonly cacheService: CacheService,
    40 |     private readonly metricsService: MetricsService,
    41 |     private readonly loggingService: LoggingService,[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/security/types/middleware.types.ts:70:3[39m[22m
[1mTS2687: All declarations of 'value' [1m[33mmust[39m[22m[1m have identical modifiers.
    68 |   message: string;
    69 |   code: string;
  > 70 |   value?: any;
       |   ^^^^^
    71 | }
    72 |
    73 | export interface ValidationWarning {[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/security/types/middleware.types.ts:125:18[39m[22m
[1mTS2320: Interface 'EnhancedRequest' cannot simultaneously extend types 'ApiKeyRequest' and 'RateLimitRequest'.
  Named property 'apiKey' of types 'ApiKeyRequest' and 'RateLimitRequest' are not identical.
    123 | }
    124 |
  > 125 | export interface EnhancedRequest extends AuthenticatedRequest, ApiKeyRequest, RateLimitRequest {
        |                  ^^^^^^^^^^^^^^^
    126 |   deviceFingerprint?: string;
    127 |   csrfToken?: string;
    128 |   validationResult?: ValidationResult;[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/security/types/middleware.types.ts:125:18[39m[22m
[1mTS2320: Interface 'EnhancedRequest' cannot simultaneously extend types 'AuthenticatedRequest' and 'ApiKeyRequest'.
  Named property 'user' of types 'AuthenticatedRequest' and 'ApiKeyRequest' are not identical.
    123 | }
    124 |
  > 125 | export interface EnhancedRequest extends AuthenticatedRequest, ApiKeyRequest, RateLimitRequest {
        |                  ^^^^^^^^^^^^^^^
    126 |   deviceFingerprint?: string;
    127 |   csrfToken?: string;
    128 |   validationResult?: ValidationResult;[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/security/types/middleware.types.ts:125:18[39m[22m
[1mTS2320: Interface 'EnhancedRequest' cannot simultaneously extend types 'AuthenticatedRequest' and 'RateLimitRequest'.
  Named property 'user' of types 'AuthenticatedRequest' and 'RateLimitRequest' are not identical.
    123 | }
    124 |
  > 125 | export interface EnhancedRequest extends AuthenticatedRequest, ApiKeyRequest, RateLimitRequest {
        |                  ^^^^^^^^^^^^^^^
    126 |   deviceFingerprint?: string;
    127 |   csrfToken?: string;
    128 |   validationResult?: ValidationResult;[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/security/types/middleware.types.ts:346:3[39m[22m
[1mTS2687: All declarations of 'value' [1m[33mmust[39m[22m[1m have identical modifiers.
    344 |   type: SecurityEventType.INPUT_VALIDATION_FAILURE;
    345 |   field: string;
  > 346 |   value: any;
        |   ^^^^^
    347 |   rule: ValidationRule;
    348 | }
    349 |[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/services/membership.service.ts:89:11[39m[22m
[1mTS2353: Object literal may only specify known properties, and 'tier' does not exist in type 'UserPreferences'.
    87 |         preferences: {
    88 |           ...user.preferences,
  > 89 |           tier,
       |           ^^^^
    90 |           lastTierUpdate: new Date()
    91 |         }
    92 |       });[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/services/membership.service.ts:181:18[39m[22m
[1mTS2339: Property 'db' does not exist on type 'MembershipService'.
    179 |   private async createBasicUser(userId: string): Promise<void> {
    180 |     try {
  > 181 |       await this.db.insert(users).values({
        |                  ^^
    182 |         discordId: userId,
    183 |         username: 'temp_user',
    184 |         preferences: {[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/services/membership.service.ts:181:28[39m[22m
[1mTS2304: Cannot find name 'users'.
    179 |   private async createBasicUser(userId: string): Promise<void> {
    180 |     try {
  > 181 |       await this.db.insert(users).values({
        |                            ^^^^^
    182 |         discordId: userId,
    183 |         username: 'temp_user',
    184 |         preferences: {[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/types/format.types.ts:507:1[39m[22m
[1mTS2308: Module './utility.types' has already exported a member named 'CustomValidator'. Consider explicitly re-exporting to resolve the ambiguity.
    505 |  */
    506 | export * from './utility.types';
  > 507 | export * from './validation.types';
        | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/types/format.types.ts:507:1[39m[22m
[1mTS2308: Module './utility.types' has already exported a member named 'IValidationService'. Consider explicitly re-exporting to resolve the ambiguity.
    505 |  */
    506 | export * from './utility.types';
  > 507 | export * from './validation.types';
        | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/types/format.types.ts:507:1[39m[22m
[1mTS2308: Module './utility.types' has already exported a member named 'ValidationError'. Consider explicitly re-exporting to resolve the ambiguity.
    505 |  */
    506 | export * from './utility.types';
  > 507 | export * from './validation.types';
        | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/types/format.types.ts:507:1[39m[22m
[1mTS2308: Module './utility.types' has already exported a member named 'ValidationResult'. Consider explicitly re-exporting to resolve the ambiguity.
    505 |  */
    506 | export * from './utility.types';
  > 507 | export * from './validation.types';
        | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/types/format.types.ts:507:1[39m[22m
[1mTS2308: Module './utility.types' has already exported a member named 'ValidationRule'. Consider explicitly re-exporting to resolve the ambiguity.
    505 |  */
    506 | export * from './utility.types';
  > 507 | export * from './validation.types';
        | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/types/format.types.ts:507:1[39m[22m
[1mTS2308: Module './utility.types' has already exported a member named 'ValidationSchema'. Consider explicitly re-exporting to resolve the ambiguity.
    505 |  */
    506 | export * from './utility.types';
  > 507 | export * from './validation.types';
        | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/types/format.types.ts:507:1[39m[22m
[1mTS2308: Module './utility.types' has already exported a member named 'ValidationWarning'. Consider explicitly re-exporting to resolve the ambiguity.
    505 |  */
    506 | export * from './utility.types';
  > 507 | export * from './validation.types';
        | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/types/utility.types.ts:27:3[39m[22m
[1mTS2687: All declarations of 'code' [1m[33mmust[39m[22m[1m have identical modifiers.
    25 |   field?: string;
    26 |   message: string;
  > 27 |   code: string;
       |   ^^^^
    28 |   severity: 'low' | 'medium' | 'high' | 'critical';
    29 | }
    30 |[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/types/utility.types.ts:28:3[39m[22m
[1mTS2687: All declarations of 'severity' [1m[33mmust[39m[22m[1m have identical modifiers.
    26 |   message: string;
    27 |   code: string;
  > 28 |   severity: 'low' | 'medium' | 'high' | 'critical';
       |   ^^^^^^^^
    29 | }
    30 |
    31 | export interface ValidationWarning {[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/types/utility.types.ts:94:17[39m[22m
[1mTS2304: Cannot find name 'PermissionString'.
    92 |   joinedAt?: Date;
    93 |   roles?: string[];
  > 94 |   permissions?: PermissionString[];
       |                 ^^^^^^^^^^^^^^^^
    95 |   isBot: boolean;
    96 |   isSystem: boolean;
    97 | }[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/types/utility.types.ts:121:17[39m[22m
[1mTS2304: Cannot find name 'PermissionString'.
    119 |   nsfw?: boolean;
    120 |   parentId?: string;
  > 121 |   permissions?: PermissionString[];
        |                 ^^^^^^^^^^^^^^^^
    122 | }
    123 |
    124 | export interface DiscordRoleInfo {[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/types/utility.types.ts:129:16[39m[22m
[1mTS2304: Cannot find name 'PermissionString'.
    127 |   color: string;
    128 |   position: number;
  > 129 |   permissions: PermissionString[];
        |                ^^^^^^^^^^^^^^^^
    130 |   mentionable: boolean;
    131 |   hoisted: boolean;
    132 |   managed: boolean;[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/types/utility.types.ts:141:24[39m[22m
[1mTS2304: Cannot find name 'PermissionString'.
    139 |   userId: string;
    140 |   guildId: string;
  > 141 |   requiredPermissions: PermissionString[];
        |                        ^^^^^^^^^^^^^^^^
    142 |   channelId?: string;
    143 | }
    144 |[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/types/utility.types.ts:147:23[39m[22m
[1mTS2304: Cannot find name 'PermissionString'.
    145 | export interface PermissionResult {
    146 |   hasPermission: boolean;
  > 147 |   missingPermissions: PermissionString[];
        |                       ^^^^^^^^^^^^^^^^
    148 |   grantedPermissions: PermissionString[];
    149 |   isOwner: boolean;
    150 |   isAdmin: boolean;[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/types/utility.types.ts:148:23[39m[22m
[1mTS2304: Cannot find name 'PermissionString'.
    146 |   hasPermission: boolean;
    147 |   missingPermissions: PermissionString[];
  > 148 |   grantedPermissions: PermissionString[];
        |                       ^^^^^^^^^^^^^^^^
    149 |   isOwner: boolean;
    150 |   isAdmin: boolean;
    151 | }[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/types/utility.types.ts:229:62[39m[22m
[1mTS2304: Cannot find name 'PermissionString'.
    227 | export interface IPermissionService {
    228 |   checkPermissions(check: PermissionCheck): Promise<PermissionResult>;
  > 229 |   hasPermission(userId: string, guildId: string, permission: PermissionString): Promise<boolean>;
        |                                                              ^^^^^^^^^^^^^^^^
    230 |   hasAnyPermission(userId: string, guildId: string, permissions: PermissionString[]): Promise<boolean>;
    231 |   hasAllPermissions(userId: string, guildId: string, permissions: PermissionString[]): Promise<boolean>;
    232 |   getUserPermissions(userId: string, guildId: string, channelId?: string): Promise<PermissionString[]>;[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/types/utility.types.ts:230:66[39m[22m
[1mTS2304: Cannot find name 'PermissionString'.
    228 |   checkPermissions(check: PermissionCheck): Promise<PermissionResult>;
    229 |   hasPermission(userId: string, guildId: string, permission: PermissionString): Promise<boolean>;
  > 230 |   hasAnyPermission(userId: string, guildId: string, permissions: PermissionString[]): Promise<boolean>;
        |                                                                  ^^^^^^^^^^^^^^^^
    231 |   hasAllPermissions(userId: string, guildId: string, permissions: PermissionString[]): Promise<boolean>;
    232 |   getUserPermissions(userId: string, guildId: string, channelId?: string): Promise<PermissionString[]>;
    233 | }[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/types/utility.types.ts:231:67[39m[22m
[1mTS2304: Cannot find name 'PermissionString'.
    229 |   hasPermission(userId: string, guildId: string, permission: PermissionString): Promise<boolean>;
    230 |   hasAnyPermission(userId: string, guildId: string, permissions: PermissionString[]): Promise<boolean>;
  > 231 |   hasAllPermissions(userId: string, guildId: string, permissions: PermissionString[]): Promise<boolean>;
        |                                                                   ^^^^^^^^^^^^^^^^
    232 |   getUserPermissions(userId: string, guildId: string, channelId?: string): Promise<PermissionString[]>;
    233 | }
    234 |[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/types/utility.types.ts:232:84[39m[22m
[1mTS2304: Cannot find name 'PermissionString'.
    230 |   hasAnyPermission(userId: string, guildId: string, permissions: PermissionString[]): Promise<boolean>;
    231 |   hasAllPermissions(userId: string, guildId: string, permissions: PermissionString[]): Promise<boolean>;
  > 232 |   getUserPermissions(userId: string, guildId: string, channelId?: string): Promise<PermissionString[]>;
        |                                                                                    ^^^^^^^^^^^^^^^^
    233 | }
    234 |
    235 | /**[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/types/utility.types.ts:277:19[39m[22m
[1mTS2687: All declarations of 'code' [1m[33mmust[39m[22m[1m have identical modifiers.
    275 |
    276 | export class ValidationError extends [1m[31mError[39m[22m[1m implements UtilityError {
  > 277 |   public readonly code: string;
        |                   ^^^^
    278 |   public readonly context?: Record<string, any>;
    279 |   public readonly severity: 'low' | 'medium' | 'high' | 'critical';
    280 |   public readonly recoverable: boolean;[39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/types/utility.types.ts:279:19[39m[22m
[1mTS2687: All declarations of 'severity' [1m[33mmust[39m[22m[1m have identical modifiers.
    277 |   public readonly code: string;
    278 |   public readonly context?: Record<string, any>;
  > 279 |   public readonly severity: 'low' | 'medium' | 'high' | 'critical';
        |                   ^^^^^^^^
    280 |   public readonly recoverable: boolean;
    281 |
    282 |   constructor([39m[22m

[1m[31mERROR[39m[22m in [1m./src/core/types/utility.types.ts:398:20[39m[22m
[1mTS2307: Cannot find module './object-utils.types' or its corresponding type declarations.
    396 |  * Export all types
    397 |  */
  > 398 | export type * from './object-utils.types';
        |                    ^^^^^^^^^^^^^^^^^^^^^^
    399 |
    400 |[39m[22m

[1m[31mERROR[39m[22m in [1m./src/discord/commands/discord-commands.service.ts:122:18[39m[22m
[1mTS2769: No overload matches this call.
  Overload 1 of 3, '(options: InteractionReplyOptions & { withResponse: true; }): Promise<InteractionCallbackResponse>', gave the following [1m[31merror[39m[22m[1m.
    Type '{ title: string; color: number; fields: { name: string; value: string; inline: boolean; }[]; timestamp: Date; }' is not assignable to type 'APIEmbed | JSONEncodable<APIEmbed>'.
      Type '{ title: string; color: number; fields: { name: string; value: string; inline: boolean; }[]; timestamp: Date; }' is not assignable to type 'APIEmbed'.
        Types of property 'timestamp' are incompatible.
          Type 'Date' is not assignable to type 'string'.
  Overload 2 of 3, '(options: InteractionReplyOptions & { fetchReply: true; }): Promise<Message<boolean>>', gave the following [1m[31merror[39m[22m[1m.
    Type '{ title: string; color: number; fields: { name: string; value: string; inline: boolean; }[]; timestamp: Date; }' is not assignable to type 'APIEmbed | JSONEncodable<APIEmbed>'.
      Type '{ title: string; color: number; fields: { name: string; value: string; inline: boolean; }[]; timestamp: Date; }' is not assignable to type 'APIEmbed'.
        Types of property 'timestamp' are incompatible.
          Type 'Date' is not assignable to type 'string'.
  Overload 3 of 3, '(options: string | MessagePayload | InteractionReplyOptions): Promise<InteractionResponse<boolean>>', gave the following [1m[31merror[39m[22m[1m.
    Type '{ title: string; color: number; fields: { name: string; value: string; inline: boolean; }[]; timestamp: Date; }' is not assignable to type 'APIEmbed | JSONEncodable<APIEmbed>'.
      Type '{ title: string; color: number; fields: { name: string; value: string; inline: boolean; }[]; timestamp: Date; }' is not assignable to type 'APIEmbed'.
        Types of property 'timestamp' are incompatible.
          Type 'Date' is not assignable to type 'string'.
    120 |       
    121 |       await interaction.reply({
  > 122 |         embeds: [embed],
        |                  ^^^^^
    123 |         ephemeral: true,
    124 |       });
    125 |       [39m[22m

[1m[31mERROR[39m[22m in [1m./src/discord/commands/discord-commands.service.ts:182:18[39m[22m
[1mTS2769: No overload matches this call.
  Overload 1 of 3, '(options: InteractionReplyOptions & { withResponse: true; }): Promise<InteractionCallbackResponse>', gave the following [1m[31merror[39m[22m[1m.
    Type '{ title: string; description: string; color: number; fields: { name: string; value: string; inline: boolean; }[]; footer: { text: string; }; timestamp: Date; }' is not assignable to type 'APIEmbed | JSONEncodable<APIEmbed>'.
      Type '{ title: string; description: string; color: number; fields: { name: string; value: string; inline: boolean; }[]; footer: { text: string; }; timestamp: Date; }' is not assignable to type 'APIEmbed'.
        Types of property 'timestamp' are incompatible.
          Type 'Date' is not assignable to type 'string'.
  Overload 2 of 3, '(options: InteractionReplyOptions & { fetchReply: true; }): Promise<Message<boolean>>', gave the following [1m[31merror[39m[22m[1m.
    Type '{ title: string; description: string; color: number; fields: { name: string; value: string; inline: boolean; }[]; footer: { text: string; }; timestamp: Date; }' is not assignable to type 'APIEmbed | JSONEncodable<APIEmbed>'.
      Type '{ title: string; description: string; color: number; fields: { name: string; value: string; inline: boolean; }[]; footer: { text: string; }; timestamp: Date; }' is not assignable to type 'APIEmbed'.
        Types of property 'timestamp' are incompatible.
          Type 'Date' is not assignable to type 'string'.
  Overload 3 of 3, '(options: string | MessagePayload | InteractionReplyOptions): Promise<InteractionResponse<boolean>>', gave the following [1m[31merror[39m[22m[1m.
    Type '{ title: string; description: string; color: number; fields: { name: string; value: string; inline: boolean; }[]; footer: { text: string; }; timestamp: Date; }' is not assignable to type 'APIEmbed | JSONEncodable<APIEmbed>'.
      Type '{ title: string; description: string; color: number; fields: { name: string; value: string; inline: boolean; }[]; footer: { text: string; }; timestamp: Date; }' is not assignable to type 'APIEmbed'.
        Types of property 'timestamp' are incompatible.
          Type 'Date' is not assignable to type 'string'.
    180 |       
    181 |       await interaction.reply({
  > 182 |         embeds: [embed],
        |                  ^^^^^
    183 |         ephemeral: true,
    184 |       });
    185 |       [39m[22m

[1m[31mERROR[39m[22m in [1m./src/discord/discord.service.ts:70:39[39m[22m
[1mTS2339: Property 'db' does not exist on type 'DiscordService'.
    68 |     try {
    69 |       // Get guild configuration
  > 70 |       const guildResults = await this.db.select().from(guilds).where(eq(guilds.discordId, guildId));
       |                                       ^^
    71 |       const guild = guildResults[0];
    72 |
    73 |       if (!guild) {[39m[22m

[1m[31mERROR[39m[22m in [1m./src/discord/discord.service.ts:70:80[39m[22m
[1mTS2339: Property 'discordId' does not exist on type '{ readonly $inferSelect: Guild; readonly $inferInsert: CreateGuild; }'.
    68 |     try {
    69 |       // Get guild configuration
  > 70 |       const guildResults = await this.db.select().from(guilds).where(eq(guilds.discordId, guildId));
       |                                                                                ^^^^^^^^^
    71 |       const guild = guildResults[0];
    72 |
    73 |       if (!guild) {[39m[22m

[1m[31mERROR[39m[22m in [1m./src/discord/discord.service.ts:78:47[39m[22m
[1mTS2339: Property 'db' does not exist on type 'DiscordService'.
    76 |
    77 |       // Check AI agents configuration
  > 78 |       const aiAgentConfigResults = await this.db.select().from(aiAgentConfigs).where(eq(aiAgentConfigs.guildId, guild.discordId));
       |                                               ^^
    79 |       const enabledAiAgentConfigs = aiAgentConfigResults.filter((config: any) => config.enabled);
    80 |       
    81 |       if (enabledAiAgentConfigs.length > 0) {[39m[22m

[1m[31mERROR[39m[22m in [1m./src/discord/discord.service.ts:78:104[39m[22m
[1mTS2339: Property 'guildId' does not exist on type '{ readonly $inferSelect: AIAgentConfig; readonly $inferInsert: CreateAIAgentConfig; }'.
    76 |
    77 |       // Check AI agents configuration
  > 78 |       const aiAgentConfigResults = await this.db.select().from(aiAgentConfigs).where(eq(aiAgentConfigs.guildId, guild.discordId));
       |                                                                                                        ^^^^^^^
    79 |       const enabledAiAgentConfigs = aiAgentConfigResults.filter((config: any) => config.enabled);
    80 |       
    81 |       if (enabledAiAgentConfigs.length > 0) {[39m[22m

[1m[31mERROR[39m[22m in [1m./src/discord/events/discord-events.service.ts:127:18[39m[22m
[1mTS2339: Property 'db' does not exist on type 'DiscordEventsService'.
    125 |       };
    126 |
  > 127 |       await this.db.insert(guilds).values(guildData as NewGuild)
        |                  ^^
    128 |         .onConflictDoUpdate({
    129 |           target: guilds.discordId,
    130 |           set: {[39m[22m

[1m[31mERROR[39m[22m in [1m./src/discord/events/discord-events.service.ts:129:26[39m[22m
[1mTS2339: Property 'discordId' does not exist on type '{ readonly $inferSelect: Guild; readonly $inferInsert: CreateGuild; }'.
    127 |       await this.db.insert(guilds).values(guildData as NewGuild)
    128 |         .onConflictDoUpdate({
  > 129 |           target: guilds.discordId,
        |                          ^^^^^^^^^
    130 |           set: {
    131 |             name: guild.name,
    132 |             icon: guild.iconURL() || undefined,[39m[22m

[1m[31mERROR[39m[22m in [1m./src/discord/events/discord-events.service.ts:148:18[39m[22m
[1mTS2339: Property 'db' does not exist on type 'DiscordEventsService'.
    146 |   private async updateGuildStatus(guildId: string, isActive: boolean) {
    147 |     try {
  > 148 |       await this.db
        |                  ^^
    149 |         .update(guilds)
    150 |         .set({ isActive, lastActivityAt: new Date() } as Partial<NewGuild>)
    151 |         .where(eq(guilds.discordId, guildId));[39m[22m

[1m[31mERROR[39m[22m in [1m./src/discord/events/discord-events.service.ts:151:26[39m[22m
[1mTS2339: Property 'discordId' does not exist on type '{ readonly $inferSelect: Guild; readonly $inferInsert: CreateGuild; }'.
    149 |         .update(guilds)
    150 |         .set({ isActive, lastActivityAt: new Date() } as Partial<NewGuild>)
  > 151 |         .where(eq(guilds.discordId, guildId));
        |                          ^^^^^^^^^
    152 |     } catch ([1m[31merror[39m[22m[1m) {
    153 |       this.logger.[1m[31merror[39m[22m[1m(`[1m[31mFailed[39m[22m[1m to update guild ${guildId} status:`, [1m[31merror[39m[22m[1m);
    154 |     }[39m[22m

[1m[31mERROR[39m[22m in [1m./src/discord/events/discord-events.service.ts:162:18[39m[22m
[1mTS2339: Property 'db' does not exist on type 'DiscordEventsService'.
    160 |   private async updateGuildInfo(guild: Guild) {
    161 |     try {
  > 162 |       await this.db
        |                  ^^
    163 |         .update(guilds)
    164 |         .set({
    165 |           name: guild.name,[39m[22m

[1m[31mERROR[39m[22m in [1m./src/discord/events/discord-events.service.ts:170:26[39m[22m
[1mTS2339: Property 'discordId' does not exist on type '{ readonly $inferSelect: Guild; readonly $inferInsert: CreateGuild; }'.
    168 |           lastActivityAt: new Date(),
    169 |         } as Partial<NewGuild>)
  > 170 |         .where(eq(guilds.discordId, guild.id));
        |                          ^^^^^^^^^
    171 |     } catch ([1m[31merror[39m[22m[1m) {
    172 |       this.logger.[1m[31merror[39m[22m[1m(`[1m[31mFailed[39m[22m[1m to update guild ${guild.id} info:`, [1m[31merror[39m[22m[1m);
    173 |     }[39m[22m

[1m[31mERROR[39m[22m in [1m./src/discord/events/discord-events.service.ts:188:18[39m[22m
[1mTS2339: Property 'db' does not exist on type 'DiscordEventsService'.
    186 |       };
    187 |
  > 188 |       await this.db.insert(users).values(userData as NewUser)
        |                  ^^
    189 |         .onConflictDoUpdate({
    190 |           target: users.discordId,
    191 |           set: {[39m[22m

[1m[31mERROR[39m[22m in [1m./src/discord/events/discord-events.service.ts:190:25[39m[22m
[1mTS2339: Property 'discordId' does not exist on type '{ readonly $inferSelect: User; readonly $inferInsert: CreateUser; }'.
    188 |       await this.db.insert(users).values(userData as NewUser)
    189 |         .onConflictDoUpdate({
  > 190 |           target: users.discordId,
        |                         ^^^^^^^^^
    191 |           set: {
    192 |             username: userData.username,
    193 |             isActive: true,[39m[22m

[1m[31mERROR[39m[22m in [1m./src/discord/events/discord-events.service.ts:207:18[39m[22m
[1mTS2339: Property 'db' does not exist on type 'DiscordEventsService'.
    205 |   private async updateUserActivity(userId: string) {
    206 |     try {
  > 207 |       await this.db
        |                  ^^
    208 |         .update(users)
    209 |         .set({ lastActivityAt: new Date() } as Partial<NewUser>)
    210 |         .where(eq(users.discordId, userId));[39m[22m

[1m[31mERROR[39m[22m in [1m./src/discord/events/discord-events.service.ts:210:25[39m[22m
[1mTS2339: Property 'discordId' does not exist on type '{ readonly $inferSelect: User; readonly $inferInsert: CreateUser; }'.
    208 |         .update(users)
    209 |         .set({ lastActivityAt: new Date() } as Partial<NewUser>)
  > 210 |         .where(eq(users.discordId, userId));
        |                         ^^^^^^^^^
    211 |     } catch ([1m[31merror[39m[22m[1m) {
    212 |       this.logger.[1m[31merror[39m[22m[1m(`[1m[31mFailed[39m[22m[1m to update user activity for ${userId}:`, [1m[31merror[39m[22m[1m);
    213 |     }[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-automation/ai-automation.service.ts:107:39[39m[22m
[1mTS2339: Property 'db' does not exist on type 'AIAutomationService'.
    105 |   async handleSmartGreeting(@Context() [member]: [GuildMember]) {
    106 |     try {
  > 107 |       const guildResults = await this.db.select().from(guilds).where(eq(guilds.discordId, member.guild.id));
        |                                       ^^
    108 |       const guild = guildResults[0];
    109 |
    110 |       if (!guild?.settings?.aiAutomation?.smart_greetings) return;[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-automation/ai-automation.service.ts:107:56[39m[22m
[1mTS2304: Cannot find name 'guilds'.
    105 |   async handleSmartGreeting(@Context() [member]: [GuildMember]) {
    106 |     try {
  > 107 |       const guildResults = await this.db.select().from(guilds).where(eq(guilds.discordId, member.guild.id));
        |                                                        ^^^^^^
    108 |       const guild = guildResults[0];
    109 |
    110 |       if (!guild?.settings?.aiAutomation?.smart_greetings) return;[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-automation/ai-automation.service.ts:107:73[39m[22m
[1mTS2304: Cannot find name 'guilds'.
    105 |   async handleSmartGreeting(@Context() [member]: [GuildMember]) {
    106 |     try {
  > 107 |       const guildResults = await this.db.select().from(guilds).where(eq(guilds.discordId, member.guild.id));
        |                                                                         ^^^^^^
    108 |       const guild = guildResults[0];
    109 |
    110 |       if (!guild?.settings?.aiAutomation?.smart_greetings) return;[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-automation/ai-automation.service.ts:135:39[39m[22m
[1mTS2339: Property 'db' does not exist on type 'AIAutomationService'.
    133 |
    134 |     try {
  > 135 |       const guildResults = await this.db.select().from(guilds).where(eq(guilds.discordId, message.guild.id));
        |                                       ^^
    136 |       const guild = guildResults[0];
    137 |
    138 |       if (!guild?.settings?.aiAutomation?.auto_engagement) return;[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-automation/ai-automation.service.ts:135:56[39m[22m
[1mTS2304: Cannot find name 'guilds'.
    133 |
    134 |     try {
  > 135 |       const guildResults = await this.db.select().from(guilds).where(eq(guilds.discordId, message.guild.id));
        |                                                        ^^^^^^
    136 |       const guild = guildResults[0];
    137 |
    138 |       if (!guild?.settings?.aiAutomation?.auto_engagement) return;[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-automation/ai-automation.service.ts:135:73[39m[22m
[1mTS2304: Cannot find name 'guilds'.
    133 |
    134 |     try {
  > 135 |       const guildResults = await this.db.select().from(guilds).where(eq(guilds.discordId, message.guild.id));
        |                                                                         ^^^^^^
    136 |       const guild = guildResults[0];
    137 |
    138 |       if (!guild?.settings?.aiAutomation?.auto_engagement) return;[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-automation/ai-automation.service.ts:161:39[39m[22m
[1mTS2339: Property 'db' does not exist on type 'AIAutomationService'.
    159 |   async generateContentSuggestions() {
    160 |     try {
  > 161 |       const activeGuilds = await this.db.select().from(guilds).where(eq(guilds.isActive, true));
        |                                       ^^
    162 |
    163 |       for (const guild of activeGuilds) {
    164 |         if (!guild.settings?.aiAutomation?.content_suggestions) continue;[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-automation/ai-automation.service.ts:161:56[39m[22m
[1mTS2304: Cannot find name 'guilds'.
    159 |   async generateContentSuggestions() {
    160 |     try {
  > 161 |       const activeGuilds = await this.db.select().from(guilds).where(eq(guilds.isActive, true));
        |                                                        ^^^^^^
    162 |
    163 |       for (const guild of activeGuilds) {
    164 |         if (!guild.settings?.aiAutomation?.content_suggestions) continue;[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-automation/ai-automation.service.ts:161:73[39m[22m
[1mTS2304: Cannot find name 'guilds'.
    159 |   async generateContentSuggestions() {
    160 |     try {
  > 161 |       const activeGuilds = await this.db.select().from(guilds).where(eq(guilds.isActive, true));
        |                                                                         ^^^^^^
    162 |
    163 |       for (const guild of activeGuilds) {
    164 |         if (!guild.settings?.aiAutomation?.content_suggestions) continue;[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-automation/ai-automation.service.ts:182:39[39m[22m
[1mTS2339: Property 'db' does not exist on type 'AIAutomationService'.
    180 |   async generateMemberInsights() {
    181 |     try {
  > 182 |       const activeGuilds = await this.db.select().from(guilds).where(eq(guilds.isActive, true));
        |                                       ^^
    183 |
    184 |       for (const guild of activeGuilds) {
    185 |         if (!guild.settings?.aiAutomation?.member_insights) continue;[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-automation/ai-automation.service.ts:182:56[39m[22m
[1mTS2304: Cannot find name 'guilds'.
    180 |   async generateMemberInsights() {
    181 |     try {
  > 182 |       const activeGuilds = await this.db.select().from(guilds).where(eq(guilds.isActive, true));
        |                                                        ^^^^^^
    183 |
    184 |       for (const guild of activeGuilds) {
    185 |         if (!guild.settings?.aiAutomation?.member_insights) continue;[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-automation/ai-automation.service.ts:182:73[39m[22m
[1mTS2304: Cannot find name 'guilds'.
    180 |   async generateMemberInsights() {
    181 |     try {
  > 182 |       const activeGuilds = await this.db.select().from(guilds).where(eq(guilds.isActive, true));
        |                                                                         ^^^^^^
    183 |
    184 |       for (const guild of activeGuilds) {
    185 |         if (!guild.settings?.aiAutomation?.member_insights) continue;[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-automation/auto-moderation.service.ts:35:39[39m[22m
[1mTS2339: Property 'db' does not exist on type 'AutoModerationService'.
    33 |
    34 |     try {
  > 35 |       const guildResults = await this.db.select().from(guilds).where(eq(guilds.discordId, message.guild.id));
       |                                       ^^
    36 |       const guild = guildResults[0];
    37 |
    38 |       if (!guild?.settings?.aiAutomation?.auto_moderation) return;[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-automation/auto-moderation.service.ts:35:70[39m[22m
[1mTS2304: Cannot find name 'eq'.
    33 |
    34 |     try {
  > 35 |       const guildResults = await this.db.select().from(guilds).where(eq(guilds.discordId, message.guild.id));
       |                                                                      ^^
    36 |       const guild = guildResults[0];
    37 |
    38 |       if (!guild?.settings?.aiAutomation?.auto_moderation) return;[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-automation/auto-moderation.service.ts:35:80[39m[22m
[1mTS2339: Property 'discordId' does not exist on type '{ readonly $inferSelect: Guild; readonly $inferInsert: CreateGuild; }'.
    33 |
    34 |     try {
  > 35 |       const guildResults = await this.db.select().from(guilds).where(eq(guilds.discordId, message.guild.id));
       |                                                                                ^^^^^^^^^
    36 |       const guild = guildResults[0];
    37 |
    38 |       if (!guild?.settings?.aiAutomation?.auto_moderation) return;[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-automation/auto-moderation.service.ts:109:36[39m[22m
[1mTS2339: Property 'db' does not exist on type 'AutoModerationService'.
    107 |     
    108 |     // Check if user is very new (potential raid account)
  > 109 |     const userResults = await this.db.select().from(users).where(eq(users.discordId, userId));
        |                                    ^^
    110 |     const user = userResults[0];
    111 |     
    112 |     if (user && user.createdAt) {[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-automation/auto-moderation.service.ts:109:66[39m[22m
[1mTS2304: Cannot find name 'eq'.
    107 |     
    108 |     // Check if user is very new (potential raid account)
  > 109 |     const userResults = await this.db.select().from(users).where(eq(users.discordId, userId));
        |                                                                  ^^
    110 |     const user = userResults[0];
    111 |     
    112 |     if (user && user.createdAt) {[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-automation/auto-moderation.service.ts:109:75[39m[22m
[1mTS2339: Property 'discordId' does not exist on type '{ readonly $inferSelect: User; readonly $inferInsert: CreateUser; }'.
    107 |     
    108 |     // Check if user is very new (potential raid account)
  > 109 |     const userResults = await this.db.select().from(users).where(eq(users.discordId, userId));
        |                                                                           ^^^^^^^^^
    110 |     const user = userResults[0];
    111 |     
    112 |     if (user && user.createdAt) {[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-automation/auto-moderation.service.ts:240:39[39m[22m
[1mTS2339: Property 'db' does not exist on type 'AutoModerationService'.
    238 |     try {
    239 |       // Send to moderation log channel
  > 240 |       const guildResults = await this.db.select().from(guilds).where(eq(guilds.discordId, message.guild!.id));
        |                                       ^^
    241 |       const guild = guildResults[0];
    242 |
    243 |       const logChannelId = guild?.settings?.moderation?.logChannel;[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-automation/auto-moderation.service.ts:240:70[39m[22m
[1mTS2304: Cannot find name 'eq'.
    238 |     try {
    239 |       // Send to moderation log channel
  > 240 |       const guildResults = await this.db.select().from(guilds).where(eq(guilds.discordId, message.guild!.id));
        |                                                                      ^^
    241 |       const guild = guildResults[0];
    242 |
    243 |       const logChannelId = guild?.settings?.moderation?.logChannel;[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-automation/auto-moderation.service.ts:240:80[39m[22m
[1mTS2339: Property 'discordId' does not exist on type '{ readonly $inferSelect: Guild; readonly $inferInsert: CreateGuild; }'.
    238 |     try {
    239 |       // Send to moderation log channel
  > 240 |       const guildResults = await this.db.select().from(guilds).where(eq(guilds.discordId, message.guild!.id));
        |                                                                                ^^^^^^^^^
    241 |       const guild = guildResults[0];
    242 |
    243 |       const logChannelId = guild?.settings?.moderation?.logChannel;[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-automation/smart-notification.service.ts:20:39[39m[22m
[1mTS2339: Property 'db' does not exist on type 'SmartNotificationService'.
    18 |   async checkInactiveUsers() {
    19 |     try {
  > 20 |       const activeGuilds = await this.db.select().from(guilds).where(eq(guilds.isActive, true));
       |                                       ^^
    21 |
    22 |       for (const guild of activeGuilds) {
    23 |         if (!guild.settings?.aiAutomation?.smart_notifications) continue;[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-automation/smart-notification.service.ts:20:70[39m[22m
[1mTS2304: Cannot find name 'eq'.
    18 |   async checkInactiveUsers() {
    19 |     try {
  > 20 |       const activeGuilds = await this.db.select().from(guilds).where(eq(guilds.isActive, true));
       |                                                                      ^^
    21 |
    22 |       for (const guild of activeGuilds) {
    23 |         if (!guild.settings?.aiAutomation?.smart_notifications) continue;[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-automation/smart-notification.service.ts:20:80[39m[22m
[1mTS2339: Property 'isActive' does not exist on type '{ readonly $inferSelect: Guild; readonly $inferInsert: CreateGuild; }'.
    18 |   async checkInactiveUsers() {
    19 |     try {
  > 20 |       const activeGuilds = await this.db.select().from(guilds).where(eq(guilds.isActive, true));
       |                                                                                ^^^^^^^^
    21 |
    22 |       for (const guild of activeGuilds) {
    23 |         if (!guild.settings?.aiAutomation?.smart_notifications) continue;[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-automation/smart-notification.service.ts:37:39[39m[22m
[1mTS2339: Property 'db' does not exist on type 'SmartNotificationService'.
    35 |   async sendEngagementDigest() {
    36 |     try {
  > 37 |       const activeGuilds = await this.db.select().from(guilds).where(eq(guilds.isActive, true));
       |                                       ^^
    38 |
    39 |       for (const guild of activeGuilds) {
    40 |         if (!guild.settings?.aiAutomation?.engagement_digest) continue;[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-automation/smart-notification.service.ts:37:70[39m[22m
[1mTS2304: Cannot find name 'eq'.
    35 |   async sendEngagementDigest() {
    36 |     try {
  > 37 |       const activeGuilds = await this.db.select().from(guilds).where(eq(guilds.isActive, true));
       |                                                                      ^^
    38 |
    39 |       for (const guild of activeGuilds) {
    40 |         if (!guild.settings?.aiAutomation?.engagement_digest) continue;[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-automation/smart-notification.service.ts:37:80[39m[22m
[1mTS2339: Property 'isActive' does not exist on type '{ readonly $inferSelect: Guild; readonly $inferInsert: CreateGuild; }'.
    35 |   async sendEngagementDigest() {
    36 |     try {
  > 37 |       const activeGuilds = await this.db.select().from(guilds).where(eq(guilds.isActive, true));
       |                                                                                ^^^^^^^^
    38 |
    39 |       for (const guild of activeGuilds) {
    40 |         if (!guild.settings?.aiAutomation?.engagement_digest) continue;[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-automation/smart-notification.service.ts:54:39[39m[22m
[1mTS2339: Property 'db' does not exist on type 'SmartNotificationService'.
    52 |   async generateWeeklyInsightsCron() {
    53 |     try {
  > 54 |       const activeGuilds = await this.db.select().from(guilds).where(eq(guilds.isActive, true));
       |                                       ^^
    55 |
    56 |       for (const guild of activeGuilds) {
    57 |         if (!guild.settings?.aiAutomation?.weekly_insights) continue;[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-automation/smart-notification.service.ts:54:70[39m[22m
[1mTS2304: Cannot find name 'eq'.
    52 |   async generateWeeklyInsightsCron() {
    53 |     try {
  > 54 |       const activeGuilds = await this.db.select().from(guilds).where(eq(guilds.isActive, true));
       |                                                                      ^^
    55 |
    56 |       for (const guild of activeGuilds) {
    57 |         if (!guild.settings?.aiAutomation?.weekly_insights) continue;[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-automation/smart-notification.service.ts:54:80[39m[22m
[1mTS2339: Property 'isActive' does not exist on type '{ readonly $inferSelect: Guild; readonly $inferInsert: CreateGuild; }'.
    52 |   async generateWeeklyInsightsCron() {
    53 |     try {
  > 54 |       const activeGuilds = await this.db.select().from(guilds).where(eq(guilds.isActive, true));
       |                                                                                ^^^^^^^^
    55 |
    56 |       for (const guild of activeGuilds) {
    57 |         if (!guild.settings?.aiAutomation?.weekly_insights) continue;[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-automation/smart-notification.service.ts:73:40[39m[22m
[1mTS2339: Property 'db' does not exist on type 'SmartNotificationService'.
    71 |       const threeDaysAgo = new Date(Date.now() - 3 * 24 * 60 * 60 * 1000);
    72 |       
  > 73 |       const inactiveUsers = await this.db.select().from(users)
       |                                        ^^
    74 |         .where(and(
    75 |           eq(users.isActive, true),
    76 |           lt(users.lastActivityAt, threeDaysAgo)[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-automation/smart-notification.service.ts:74:16[39m[22m
[1mTS2304: Cannot find name 'and'.
    72 |       
    73 |       const inactiveUsers = await this.db.select().from(users)
  > 74 |         .where(and(
       |                ^^^
    75 |           eq(users.isActive, true),
    76 |           lt(users.lastActivityAt, threeDaysAgo)
    77 |         ))[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-automation/smart-notification.service.ts:75:11[39m[22m
[1mTS2304: Cannot find name 'eq'.
    73 |       const inactiveUsers = await this.db.select().from(users)
    74 |         .where(and(
  > 75 |           eq(users.isActive, true),
       |           ^^
    76 |           lt(users.lastActivityAt, threeDaysAgo)
    77 |         ))
    78 |         .limit(10); // Limit to avoid spam[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-automation/smart-notification.service.ts:75:20[39m[22m
[1mTS2339: Property 'isActive' does not exist on type '{ readonly $inferSelect: User; readonly $inferInsert: CreateUser; }'.
    73 |       const inactiveUsers = await this.db.select().from(users)
    74 |         .where(and(
  > 75 |           eq(users.isActive, true),
       |                    ^^^^^^^^
    76 |           lt(users.lastActivityAt, threeDaysAgo)
    77 |         ))
    78 |         .limit(10); // Limit to avoid spam[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-automation/smart-notification.service.ts:76:11[39m[22m
[1mTS2304: Cannot find name 'lt'.
    74 |         .where(and(
    75 |           eq(users.isActive, true),
  > 76 |           lt(users.lastActivityAt, threeDaysAgo)
       |           ^^
    77 |         ))
    78 |         .limit(10); // Limit to avoid spam
    79 |[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-automation/smart-notification.service.ts:76:20[39m[22m
[1mTS2339: Property 'lastActivityAt' does not exist on type '{ readonly $inferSelect: User; readonly $inferInsert: CreateUser; }'.
    74 |         .where(and(
    75 |           eq(users.isActive, true),
  > 76 |           lt(users.lastActivityAt, threeDaysAgo)
       |                    ^^^^^^^^^^^^^^
    77 |         ))
    78 |         .limit(10); // Limit to avoid spam
    79 |[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-automation/smart-notification.service.ts:105:14[39m[22m
[1mTS2339: Property 'db' does not exist on type 'SmartNotificationService'.
    103 |         topChannels
    104 |       ] = await Promise.all([
  > 105 |         this.db.select().from(users).where(gte(users.createdAt, yesterday)),
        |              ^^
    106 |         this.db.select().from(users).where(and(
    107 |           eq(users.isActive, true),
    108 |           gte(users.lastActivityAt, yesterday)[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-automation/smart-notification.service.ts:105:44[39m[22m
[1mTS2304: Cannot find name 'gte'.
    103 |         topChannels
    104 |       ] = await Promise.all([
  > 105 |         this.db.select().from(users).where(gte(users.createdAt, yesterday)),
        |                                            ^^^
    106 |         this.db.select().from(users).where(and(
    107 |           eq(users.isActive, true),
    108 |           gte(users.lastActivityAt, yesterday)[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-automation/smart-notification.service.ts:105:54[39m[22m
[1mTS2339: Property 'createdAt' does not exist on type '{ readonly $inferSelect: User; readonly $inferInsert: CreateUser; }'.
    103 |         topChannels
    104 |       ] = await Promise.all([
  > 105 |         this.db.select().from(users).where(gte(users.createdAt, yesterday)),
        |                                                      ^^^^^^^^^
    106 |         this.db.select().from(users).where(and(
    107 |           eq(users.isActive, true),
    108 |           gte(users.lastActivityAt, yesterday)[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-automation/smart-notification.service.ts:106:14[39m[22m
[1mTS2339: Property 'db' does not exist on type 'SmartNotificationService'.
    104 |       ] = await Promise.all([
    105 |         this.db.select().from(users).where(gte(users.createdAt, yesterday)),
  > 106 |         this.db.select().from(users).where(and(
        |              ^^
    107 |           eq(users.isActive, true),
    108 |           gte(users.lastActivityAt, yesterday)
    109 |         )),[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-automation/smart-notification.service.ts:106:44[39m[22m
[1mTS2304: Cannot find name 'and'.
    104 |       ] = await Promise.all([
    105 |         this.db.select().from(users).where(gte(users.createdAt, yesterday)),
  > 106 |         this.db.select().from(users).where(and(
        |                                            ^^^
    107 |           eq(users.isActive, true),
    108 |           gte(users.lastActivityAt, yesterday)
    109 |         )),[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-automation/smart-notification.service.ts:107:11[39m[22m
[1mTS2304: Cannot find name 'eq'.
    105 |         this.db.select().from(users).where(gte(users.createdAt, yesterday)),
    106 |         this.db.select().from(users).where(and(
  > 107 |           eq(users.isActive, true),
        |           ^^
    108 |           gte(users.lastActivityAt, yesterday)
    109 |         )),
    110 |         this.db.select().from(agentInteractions).where(gte(agentInteractions.createdAt, yesterday)),[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-automation/smart-notification.service.ts:107:20[39m[22m
[1mTS2339: Property 'isActive' does not exist on type '{ readonly $inferSelect: User; readonly $inferInsert: CreateUser; }'.
    105 |         this.db.select().from(users).where(gte(users.createdAt, yesterday)),
    106 |         this.db.select().from(users).where(and(
  > 107 |           eq(users.isActive, true),
        |                    ^^^^^^^^
    108 |           gte(users.lastActivityAt, yesterday)
    109 |         )),
    110 |         this.db.select().from(agentInteractions).where(gte(agentInteractions.createdAt, yesterday)),[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-automation/smart-notification.service.ts:108:11[39m[22m
[1mTS2304: Cannot find name 'gte'.
    106 |         this.db.select().from(users).where(and(
    107 |           eq(users.isActive, true),
  > 108 |           gte(users.lastActivityAt, yesterday)
        |           ^^^
    109 |         )),
    110 |         this.db.select().from(agentInteractions).where(gte(agentInteractions.createdAt, yesterday)),
    111 |         this.getTopChannelsByActivity(guildId, yesterday),[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-automation/smart-notification.service.ts:108:21[39m[22m
[1mTS2339: Property 'lastActivityAt' does not exist on type '{ readonly $inferSelect: User; readonly $inferInsert: CreateUser; }'.
    106 |         this.db.select().from(users).where(and(
    107 |           eq(users.isActive, true),
  > 108 |           gte(users.lastActivityAt, yesterday)
        |                     ^^^^^^^^^^^^^^
    109 |         )),
    110 |         this.db.select().from(agentInteractions).where(gte(agentInteractions.createdAt, yesterday)),
    111 |         this.getTopChannelsByActivity(guildId, yesterday),[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-automation/smart-notification.service.ts:110:14[39m[22m
[1mTS2339: Property 'db' does not exist on type 'SmartNotificationService'.
    108 |           gte(users.lastActivityAt, yesterday)
    109 |         )),
  > 110 |         this.db.select().from(agentInteractions).where(gte(agentInteractions.createdAt, yesterday)),
        |              ^^
    111 |         this.getTopChannelsByActivity(guildId, yesterday),
    112 |       ]);
    113 |[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-automation/smart-notification.service.ts:110:56[39m[22m
[1mTS2304: Cannot find name 'gte'.
    108 |           gte(users.lastActivityAt, yesterday)
    109 |         )),
  > 110 |         this.db.select().from(agentInteractions).where(gte(agentInteractions.createdAt, yesterday)),
        |                                                        ^^^
    111 |         this.getTopChannelsByActivity(guildId, yesterday),
    112 |       ]);
    113 |[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-automation/smart-notification.service.ts:110:78[39m[22m
[1mTS2339: Property 'createdAt' does not exist on type '{ readonly $inferSelect: AgentInteraction; readonly $inferInsert: CreateAgentInteraction; }'.
    108 |           gte(users.lastActivityAt, yesterday)
    109 |         )),
  > 110 |         this.db.select().from(agentInteractions).where(gte(agentInteractions.createdAt, yesterday)),
        |                                                                              ^^^^^^^^^
    111 |         this.getTopChannelsByActivity(guildId, yesterday),
    112 |       ]);
    113 |[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-automation/smart-notification.service.ts:178:41[39m[22m
[1mTS2339: Property 'db' does not exist on type 'SmartNotificationService'.
    176 |
    177 |     // Personalize based on user's previous activity
  > 178 |     const userInteractions = await this.db.select().from(agentInteractions)
        |                                         ^^
    179 |       .where(eq(agentInteractions.userId, user.discordId))
    180 |       .orderBy(desc(agentInteractions.createdAt))
    181 |       .limit(5);[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-automation/smart-notification.service.ts:179:14[39m[22m
[1mTS2304: Cannot find name 'eq'.
    177 |     // Personalize based on user's previous activity
    178 |     const userInteractions = await this.db.select().from(agentInteractions)
  > 179 |       .where(eq(agentInteractions.userId, user.discordId))
        |              ^^
    180 |       .orderBy(desc(agentInteractions.createdAt))
    181 |       .limit(5);
    182 |[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-automation/smart-notification.service.ts:179:35[39m[22m
[1mTS2339: Property 'userId' does not exist on type '{ readonly $inferSelect: AgentInteraction; readonly $inferInsert: CreateAgentInteraction; }'.
    177 |     // Personalize based on user's previous activity
    178 |     const userInteractions = await this.db.select().from(agentInteractions)
  > 179 |       .where(eq(agentInteractions.userId, user.discordId))
        |                                   ^^^^^^
    180 |       .orderBy(desc(agentInteractions.createdAt))
    181 |       .limit(5);
    182 |[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-automation/smart-notification.service.ts:180:16[39m[22m
[1mTS2304: Cannot find name 'desc'.
    178 |     const userInteractions = await this.db.select().from(agentInteractions)
    179 |       .where(eq(agentInteractions.userId, user.discordId))
  > 180 |       .orderBy(desc(agentInteractions.createdAt))
        |                ^^^^
    181 |       .limit(5);
    182 |
    183 |     if (userInteractions.length > 0) {[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-automation/smart-notification.service.ts:180:39[39m[22m
[1mTS2339: Property 'createdAt' does not exist on type '{ readonly $inferSelect: AgentInteraction; readonly $inferInsert: CreateAgentInteraction; }'.
    178 |     const userInteractions = await this.db.select().from(agentInteractions)
    179 |       .where(eq(agentInteractions.userId, user.discordId))
  > 180 |       .orderBy(desc(agentInteractions.createdAt))
        |                                       ^^^^^^^^^
    181 |       .limit(5);
    182 |
    183 |     if (userInteractions.length > 0) {[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/ai-channel-auto-setup.service.ts:303:33[39m[22m
[1mTS2339: Property 'db' does not exist on type 'AIChannelAutoSetupService'.
    301 |   private async hasAIChannelSetup(guildId: string): Promise<boolean> {
    302 |     try {
  > 303 |       const config = await this.db.select().from(aiChannelConfigs)
        |                                 ^^
    304 |         .where(eq(aiChannelConfigs.guildId, guildId))
    305 |         .limit(1);
    306 |[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/ai-channel-auto-setup.service.ts:304:16[39m[22m
[1mTS2304: Cannot find name 'eq'.
    302 |     try {
    303 |       const config = await this.db.select().from(aiChannelConfigs)
  > 304 |         .where(eq(aiChannelConfigs.guildId, guildId))
        |                ^^
    305 |         .limit(1);
    306 |
    307 |       if (config.length === 0) {[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/ai-channel-auto-setup.service.ts:439:38[39m[22m
[1mTS2339: Property 'db' does not exist on type 'AIChannelAutoSetupService'.
    437 |       const totalGuilds = this.client?.guilds?.cache.size || 0;
    438 |       
  > 439 |       const setupGuilds = await this.db.select().from(aiChannelConfigs);
        |                                      ^^
    440 |       const setupCount = setupGuilds.length;
    441 |
    442 |       return {[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/ai-channel-panel.service.ts:1312:18[39m[22m
[1mTS2339: Property 'db' does not exist on type 'AIChannelPanelService'.
    1310 |       } as NewAIChannelConfig;
    1311 |
  > 1312 |       await this.db.insert(aiChannelConfigs)
         |                  ^^
    1313 |         .values(config)
    1314 |         .onConflictDoUpdate({
    1315 |           target: aiChannelConfigs.guildId,[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/ai-channel-panel.service.ts:1361:18[39m[22m
[1mTS2339: Property 'db' does not exist on type 'AIChannelPanelService'.
    1359 |       });
    1360 |
  > 1361 |       await this.db.update(aiChannelConfigs)
         |                  ^^
    1362 |         .set({ lastPanelUpdate: new Date() } as any)
    1363 |         .where(eq(aiChannelConfigs.guildId, guildId));
    1364 |[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/ai-channel-panel.service.ts:1363:16[39m[22m
[1mTS2304: Cannot find name 'eq'.
    1361 |       await this.db.update(aiChannelConfigs)
    1362 |         .set({ lastPanelUpdate: new Date() } as any)
  > 1363 |         .where(eq(aiChannelConfigs.guildId, guildId));
         |                ^^
    1364 |
    1365 |       this.logger.log(`Updated panel for guild ${guildId}`);
    1366 |       return true;[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/ai-channel-panel.service.ts:1379:33[39m[22m
[1mTS2339: Property 'db' does not exist on type 'AIChannelPanelService'.
    1377 |   async getPanelConfig(guildId: string): Promise<AIChannelConfig | null> {
    1378 |     try {
  > 1379 |       const result = await this.db.select().from(aiChannelConfigs)
         |                                 ^^
    1380 |         .where(eq(aiChannelConfigs.guildId, guildId))
    1381 |         .limit(1);
    1382 |[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/ai-channel-panel.service.ts:1380:16[39m[22m
[1mTS2304: Cannot find name 'eq'.
    1378 |     try {
    1379 |       const result = await this.db.select().from(aiChannelConfigs)
  > 1380 |         .where(eq(aiChannelConfigs.guildId, guildId))
         |                ^^
    1381 |         .limit(1);
    1382 |
    1383 |       return result[0] || null;[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/ai-channel-panel.service.ts:1412:18[39m[22m
[1mTS2339: Property 'db' does not exist on type 'AIChannelPanelService'.
    1410 |       }
    1411 |
  > 1412 |       await this.db.update(aiChannelConfigs)
         |                  ^^
    1413 |         .set({ 
    1414 |           enabled: false,
    1415 |           panelMessageId: null,[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/ai-channel-panel.service.ts:1418:16[39m[22m
[1mTS2304: Cannot find name 'eq'.
    1416 |           updatedAt: new Date()
    1417 |         } as any)
  > 1418 |         .where(eq(aiChannelConfigs.guildId, guildId));
         |                ^^
    1419 |
    1420 |       this.logger.log(`Removed panel for guild ${guildId}`);
    1421 |       return true;[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/api-key-manager.service.ts:179:18[39m[22m
[1mTS2339: Property 'db' does not exist on type 'ApiKeyManagerService'.
    177 |       } as NewUserApiKey;
    178 |
  > 179 |       await this.db.insert(userApiKeys).values(newApiKey);
        |                  ^^
    180 |
    181 |       // Fetch and return the created key
    182 |       const createdKey = await this.getUserApiKey(userId, guildId, provider, keyName);[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/api-key-manager.service.ts:203:33[39m[22m
[1mTS2339: Property 'db' does not exist on type 'ApiKeyManagerService'.
    201 |   ): Promise<UserApiKey | null> {
    202 |     try {
  > 203 |       const result = await this.db.select().from(userApiKeys)
        |                                 ^^
    204 |         .where(and(
    205 |           eq(userApiKeys.userId, userId),
    206 |           eq(userApiKeys.guildId, guildId),[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/api-key-manager.service.ts:225:25[39m[22m
[1mTS2339: Property 'db' does not exist on type 'ApiKeyManagerService'.
    223 |   async getUserApiKeys(userId: string, guildId: string): Promise<UserApiKey[]> {
    224 |     try {
  > 225 |       return await this.db.select().from(userApiKeys)
        |                         ^^
    226 |         .where(and(
    227 |           eq(userApiKeys.userId, userId),
    228 |           eq(userApiKeys.guildId, guildId),[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/api-key-manager.service.ts:242:33[39m[22m
[1mTS2339: Property 'db' does not exist on type 'ApiKeyManagerService'.
    240 |   async getDefaultApiKey(userId: string, guildId: string, provider: AIProvider): Promise<UserApiKey | null> {
    241 |     try {
  > 242 |       const result = await this.db.select().from(userApiKeys)
        |                                 ^^
    243 |         .where(and(
    244 |           eq(userApiKeys.userId, userId),
    245 |           eq(userApiKeys.guildId, guildId),[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/api-key-manager.service.ts:264:33[39m[22m
[1mTS2339: Property 'db' does not exist on type 'ApiKeyManagerService'.
    262 |   async getDecryptedApiKey(keyId: string): Promise<string | null> {
    263 |     try {
  > 264 |       const result = await this.db.select().from(userApiKeys)
        |                                 ^^
    265 |         .where(and(
    266 |           eq(userApiKeys.id, keyId),
    267 |           eq(userApiKeys.isActive, true)[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/api-key-manager.service.ts:285:30[39m[22m
[1mTS2339: Property 'db' does not exist on type 'ApiKeyManagerService'.
    283 |   async updateKeyUsage(keyId: string): Promise<void> {
    284 |     try {
  > 285 |       const key = await this.db.select().from(userApiKeys)
        |                              ^^
    286 |         .where(eq(userApiKeys.id, keyId))
    287 |         .limit(1);
    288 |[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/api-key-manager.service.ts:295:20[39m[22m
[1mTS2339: Property 'db' does not exist on type 'ApiKeyManagerService'.
    293 |         };
    294 |
  > 295 |         await this.db.update(userApiKeys)
        |                    ^^
    296 |           .set({
    297 |             config: updatedConfig,
    298 |             lastUsedAt: new Date(),[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/api-key-manager.service.ts:313:33[39m[22m
[1mTS2339: Property 'db' does not exist on type 'ApiKeyManagerService'.
    311 |   async deleteApiKey(userId: string, guildId: string, keyId: string): Promise<boolean> {
    312 |     try {
  > 313 |       const result = await this.db.update(userApiKeys)
        |                                 ^^
    314 |         .set({ 
    315 |           isActive: false, 
    316 |           updatedAt: new Date() [39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/api-key-manager.service.ts:339:7[39m[22m
[1mTS2322: Type 'Date' is not assignable to type 'string'.
    337 |     const validation: APIKeyValidation = {
    338 |       isValid: true,
  > 339 |       lastChecked: new Date(),
        |       ^^^^^^^^^^^
    340 |     };
    341 |
    342 |     try {[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/api-key-manager.service.ts:388:18[39m[22m
[1mTS2339: Property 'db' does not exist on type 'ApiKeyManagerService'.
    386 |   private async clearDefaultKeys(userId: string, guildId: string, provider: AIProvider): Promise<void> {
    387 |     try {
  > 388 |       await this.db.update(userApiKeys)
        |                  ^^
    389 |         .set({ 
    390 |           isDefault: false, 
    391 |           updatedAt: new Date() [39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/api-key-manager.service.ts:410:30[39m[22m
[1mTS2339: Property 'db' does not exist on type 'ApiKeyManagerService'.
    408 |     try {
    409 |       // Get the key to find its provider
  > 410 |       const key = await this.db.select().from(userApiKeys)
        |                              ^^
    411 |         .where(eq(userApiKeys.id, keyId))
    412 |         .limit(1);
    413 |[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/api-key-manager.service.ts:420:18[39m[22m
[1mTS2339: Property 'db' does not exist on type 'ApiKeyManagerService'.
    418 |
    419 |       // Set this key as default
  > 420 |       await this.db.update(userApiKeys)
        |                  ^^
    421 |         .set({ 
    422 |           isDefault: true, 
    423 |           updatedAt: new Date() [39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/api-key-manager.service.ts:440:18[39m[22m
[1mTS2339: Property 'db' does not exist on type 'ApiKeyManagerService'.
    438 |   async updateApiKeyConfig(userId: string, guildId: string, keyId: string, config: APIKeyConfig): Promise<boolean> {
    439 |     try {
  > 440 |       await this.db.update(userApiKeys)
        |                  ^^
    441 |         .set({ 
    442 |           config: config,
    443 |           updatedAt: new Date() [39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/api-key.commands.ts:91:24[39m[22m
[1mTS2339: Property 'guild' does not exist on type 'SlashCommandContext'.
    89 |   ) {
    90 |     try {
  > 91 |       if (!interaction.guild || !interaction.member) {
       |                        ^^^^^
    92 |         await interaction.reply({
    93 |           content: '❌ This command can only be used in a server.',
    94 |           ephemeral: true,[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/api-key.commands.ts:91:46[39m[22m
[1mTS2339: Property 'member' does not exist on type 'SlashCommandContext'.
    89 |   ) {
    90 |     try {
  > 91 |       if (!interaction.guild || !interaction.member) {
       |                                              ^^^^^^
    92 |         await interaction.reply({
    93 |           content: '❌ This command can only be used in a server.',
    94 |           ephemeral: true,[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/api-key.commands.ts:92:27[39m[22m
[1mTS2339: Property 'reply' does not exist on type 'SlashCommandContext'.
    90 |     try {
    91 |       if (!interaction.guild || !interaction.member) {
  > 92 |         await interaction.reply({
       |                           ^^^^^
    93 |           content: '❌ This command can only be used in a server.',
    94 |           ephemeral: true,
    95 |         });[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/api-key.commands.ts:99:25[39m[22m
[1mTS2339: Property 'deferReply' does not exist on type 'SlashCommandContext'.
     97 |       }
     98 |
  >  99 |       await interaction.deferReply({ ephemeral: true });
        |                         ^^^^^^^^^^
    100 |
    101 |       switch (action) {
    102 |         case 'list':[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/api-key.commands.ts:107:31[39m[22m
[1mTS2339: Property 'editReply' does not exist on type 'SlashCommandContext'.
    105 |         case 'add':
    106 |           if (!provider || !keyName || !apiKey) {
  > 107 |             await interaction.editReply({
        |                               ^^^^^^^^^
    108 |               content: '❌ For adding keys, please provide: provider, key_name, and api_key parameters.',
    109 |             });
    110 |             return;[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/api-key.commands.ts:116:31[39m[22m
[1mTS2339: Property 'editReply' does not exist on type 'SlashCommandContext'.
    114 |         case 'delete':
    115 |           if (!keyName) {
  > 116 |             await interaction.editReply({
        |                               ^^^^^^^^^
    117 |               content: '❌ Please provide the key_name to delete.',
    118 |             });
    119 |             return;[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/api-key.commands.ts:125:31[39m[22m
[1mTS2339: Property 'editReply' does not exist on type 'SlashCommandContext'.
    123 |         case 'default':
    124 |           if (!keyName) {
  > 125 |             await interaction.editReply({
        |                               ^^^^^^^^^
    126 |               content: '❌ Please provide the key_name to set as default.',
    127 |             });
    128 |             return;[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/api-key.commands.ts:133:29[39m[22m
[1mTS2339: Property 'editReply' does not exist on type 'SlashCommandContext'.
    131 |           break;
    132 |         default:
  > 133 |           await interaction.editReply({
        |                             ^^^^^^^^^
    134 |             content: '❌ Unknown action. Use list, add, delete, or default.',
    135 |           });
    136 |       }[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/api-key.commands.ts:141:23[39m[22m
[1mTS2339: Property 'deferred' does not exist on type 'SlashCommandContext'.
    139 |       this.logger.[1m[31merror[39m[22m[1m('API keys command [1m[31mfailed[39m[22m[1m:', [1m[31merror[39m[22m[1m);
    140 |       
  > 141 |       if (interaction.deferred) {
        |                       ^^^^^^^^
    142 |         await interaction.editReply({
    143 |           content: '❌ Command [1m[31mfailed[39m[22m[1m. Please try again.',
    144 |         });[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/api-key.commands.ts:142:27[39m[22m
[1mTS2339: Property 'editReply' does not exist on type 'SlashCommandContext'.
    140 |       
    141 |       if (interaction.deferred) {
  > 142 |         await interaction.editReply({
        |                           ^^^^^^^^^
    143 |           content: '❌ Command [1m[31mfailed[39m[22m[1m. Please try again.',
    144 |         });
    145 |       } else {[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/api-key.commands.ts:146:27[39m[22m
[1mTS2339: Property 'reply' does not exist on type 'SlashCommandContext'.
    144 |         });
    145 |       } else {
  > 146 |         await interaction.reply({
        |                           ^^^^^
    147 |           content: '❌ Command [1m[31mfailed[39m[22m[1m. Please try again.',
    148 |           ephemeral: true,
    149 |         });[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/private-chat-manager.service.ts:109:18[39m[22m
[1mTS2339: Property 'db' does not exist on type 'PrivateChatManagerService'.
    107 |       } as NewAIChatSession;
    108 |
  > 109 |       await this.db.insert(aiChatSessions).values(newSession);
        |                  ^^
    110 |       
    111 |       const session = await this.getSessionById(sessionId);
    112 |       if (!session) {[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/private-chat-manager.service.ts:238:33[39m[22m
[1mTS2339: Property 'db' does not exist on type 'PrivateChatManagerService'.
    236 |   async getActiveSession(userId: string, guildId: string, agentType: string): Promise<AIChatSession | null> {
    237 |     try {
  > 238 |       const result = await this.db.select().from(aiChatSessions)
        |                                 ^^
    239 |         .where(and(
    240 |           eq(aiChatSessions.userId, userId),
    241 |           eq(aiChatSessions.guildId, guildId),[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/private-chat-manager.service.ts:239:16[39m[22m
[1mTS2304: Cannot find name 'and'.
    237 |     try {
    238 |       const result = await this.db.select().from(aiChatSessions)
  > 239 |         .where(and(
        |                ^^^
    240 |           eq(aiChatSessions.userId, userId),
    241 |           eq(aiChatSessions.guildId, guildId),
    242 |           eq(aiChatSessions.agentType, agentType),[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/private-chat-manager.service.ts:240:11[39m[22m
[1mTS2304: Cannot find name 'eq'.
    238 |       const result = await this.db.select().from(aiChatSessions)
    239 |         .where(and(
  > 240 |           eq(aiChatSessions.userId, userId),
        |           ^^
    241 |           eq(aiChatSessions.guildId, guildId),
    242 |           eq(aiChatSessions.agentType, agentType),
    243 |           eq(aiChatSessions.status, 'active')[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/private-chat-manager.service.ts:241:11[39m[22m
[1mTS2304: Cannot find name 'eq'.
    239 |         .where(and(
    240 |           eq(aiChatSessions.userId, userId),
  > 241 |           eq(aiChatSessions.guildId, guildId),
        |           ^^
    242 |           eq(aiChatSessions.agentType, agentType),
    243 |           eq(aiChatSessions.status, 'active')
    244 |         ))[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/private-chat-manager.service.ts:242:11[39m[22m
[1mTS2304: Cannot find name 'eq'.
    240 |           eq(aiChatSessions.userId, userId),
    241 |           eq(aiChatSessions.guildId, guildId),
  > 242 |           eq(aiChatSessions.agentType, agentType),
        |           ^^
    243 |           eq(aiChatSessions.status, 'active')
    244 |         ))
    245 |         .limit(1);[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/private-chat-manager.service.ts:243:11[39m[22m
[1mTS2304: Cannot find name 'eq'.
    241 |           eq(aiChatSessions.guildId, guildId),
    242 |           eq(aiChatSessions.agentType, agentType),
  > 243 |           eq(aiChatSessions.status, 'active')
        |           ^^
    244 |         ))
    245 |         .limit(1);
    246 |[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/private-chat-manager.service.ts:259:33[39m[22m
[1mTS2339: Property 'db' does not exist on type 'PrivateChatManagerService'.
    257 |   async getSessionById(sessionId: string): Promise<AIChatSession | null> {
    258 |     try {
  > 259 |       const result = await this.db.select().from(aiChatSessions)
        |                                 ^^
    260 |         .where(eq(aiChatSessions.id, sessionId))
    261 |         .limit(1);
    262 |[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/private-chat-manager.service.ts:260:16[39m[22m
[1mTS2304: Cannot find name 'eq'.
    258 |     try {
    259 |       const result = await this.db.select().from(aiChatSessions)
  > 260 |         .where(eq(aiChatSessions.id, sessionId))
        |                ^^
    261 |         .limit(1);
    262 |
    263 |       return result[0] || null;[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/private-chat-manager.service.ts:300:18[39m[22m
[1mTS2339: Property 'db' does not exist on type 'PrivateChatManagerService'.
    298 |   async updateSessionStatus(sessionId: string, status: AIChatSessionStatus): Promise<void> {
    299 |     try {
  > 300 |       await this.db.update(aiChatSessions)
        |                  ^^
    301 |         .set({ 
    302 |           status, 
    303 |           updatedAt: new Date(),[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/private-chat-manager.service.ts:306:16[39m[22m
[1mTS2304: Cannot find name 'eq'.
    304 |           ...(status === 'archived' && { archivedAt: new Date() })
    305 |         } as any)
  > 306 |         .where(eq(aiChatSessions.id, sessionId));
        |                ^^
    307 |
    308 |       // Remove from cache if no longer active
    309 |       if (status !== 'active') {[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/private-chat-manager.service.ts:326:43[39m[22m
[1mTS2339: Property 'db' does not exist on type 'PrivateChatManagerService'.
    324 |       const cutoffTime = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 hours ago
    325 |
  > 326 |       const inactiveSessions = await this.db.select().from(aiChatSessions)
        |                                           ^^
    327 |         .where(and(
    328 |           eq(aiChatSessions.status, 'active'),
    329 |           // lastMessageAt is less than cutoff time or null[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/private-chat-manager.service.ts:327:16[39m[22m
[1mTS2304: Cannot find name 'and'.
    325 |
    326 |       const inactiveSessions = await this.db.select().from(aiChatSessions)
  > 327 |         .where(and(
        |                ^^^
    328 |           eq(aiChatSessions.status, 'active'),
    329 |           // lastMessageAt is less than cutoff time or null
    330 |         ));[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/private-chat-manager.service.ts:328:11[39m[22m
[1mTS2304: Cannot find name 'eq'.
    326 |       const inactiveSessions = await this.db.select().from(aiChatSessions)
    327 |         .where(and(
  > 328 |           eq(aiChatSessions.status, 'active'),
        |           ^^
    329 |           // lastMessageAt is less than cutoff time or null
    330 |         ));
    331 |[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/private-chat-manager.service.ts:354:18[39m[22m
[1mTS2339: Property 'db' does not exist on type 'PrivateChatManagerService'.
    352 |       if (!session) return;
    353 |
  > 354 |       await this.db.update(aiChatSessions)
        |                  ^^
    355 |         .set({ 
    356 |           messageCount: (session.messageCount || 0) + 1,
    357 |           lastMessageAt: new Date(),[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/private-chat-manager.service.ts:360:16[39m[22m
[1mTS2304: Cannot find name 'eq'.
    358 |           updatedAt: new Date()
    359 |         } as any)
  > 360 |         .where(eq(aiChatSessions.id, sessionId));
        |                ^^
    361 |
    362 |     } catch ([1m[31merror[39m[22m[1m) {
    363 |       this.logger.[1m[31merror[39m[22m[1m(`[1m[31mFailed[39m[22m[1m to update session activity for ${sessionId}:`, [1m[31merror[39m[22m[1m);[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/private-chat-manager.service.ts:372:33[39m[22m
[1mTS2339: Property 'db' does not exist on type 'PrivateChatManagerService'.
    370 |   async getSessionByThreadId(threadId: string): Promise<AIChatSession | null> {
    371 |     try {
  > 372 |       const result = await this.db.select().from(aiChatSessions)
        |                                 ^^
    373 |         .where(eq(aiChatSessions.threadId, threadId))
    374 |         .limit(1);
    375 |[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/private-chat-manager.service.ts:373:16[39m[22m
[1mTS2304: Cannot find name 'eq'.
    371 |     try {
    372 |       const result = await this.db.select().from(aiChatSessions)
  > 373 |         .where(eq(aiChatSessions.threadId, threadId))
        |                ^^
    374 |         .limit(1);
    375 |
    376 |       return result[0] || null;[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/private-chat-manager.service.ts:411:25[39m[22m
[1mTS2339: Property 'db' does not exist on type 'PrivateChatManagerService'.
    409 |   async getUserActiveSessions(userId: string, guildId: string): Promise<AIChatSession[]> {
    410 |     try {
  > 411 |       return await this.db.select().from(aiChatSessions)
        |                         ^^
    412 |         .where(and(
    413 |           eq(aiChatSessions.userId, userId),
    414 |           eq(aiChatSessions.guildId, guildId),[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/private-chat-manager.service.ts:412:16[39m[22m
[1mTS2304: Cannot find name 'and'.
    410 |     try {
    411 |       return await this.db.select().from(aiChatSessions)
  > 412 |         .where(and(
        |                ^^^
    413 |           eq(aiChatSessions.userId, userId),
    414 |           eq(aiChatSessions.guildId, guildId),
    415 |           eq(aiChatSessions.status, 'active')[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/private-chat-manager.service.ts:413:11[39m[22m
[1mTS2304: Cannot find name 'eq'.
    411 |       return await this.db.select().from(aiChatSessions)
    412 |         .where(and(
  > 413 |           eq(aiChatSessions.userId, userId),
        |           ^^
    414 |           eq(aiChatSessions.guildId, guildId),
    415 |           eq(aiChatSessions.status, 'active')
    416 |         ));[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/private-chat-manager.service.ts:414:11[39m[22m
[1mTS2304: Cannot find name 'eq'.
    412 |         .where(and(
    413 |           eq(aiChatSessions.userId, userId),
  > 414 |           eq(aiChatSessions.guildId, guildId),
        |           ^^
    415 |           eq(aiChatSessions.status, 'active')
    416 |         ));
    417 |     } catch ([1m[31merror[39m[22m[1m) {[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/ai-channel/private-chat-manager.service.ts:415:11[39m[22m
[1mTS2304: Cannot find name 'eq'.
    413 |           eq(aiChatSessions.userId, userId),
    414 |           eq(aiChatSessions.guildId, guildId),
  > 415 |           eq(aiChatSessions.status, 'active')
        |           ^^
    416 |         ));
    417 |     } catch ([1m[31merror[39m[22m[1m) {
    418 |       this.logger.[1m[31merror[39m[22m[1m(`[1m[31mFailed[39m[22m[1m to get active sessions for user ${userId}:`, [1m[31merror[39m[22m[1m);[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/automation/comprehensive-automation.service.ts:395:24[39m[22m
[1mTS2339: Property 'settings' does not exist on type 'Guild'.
    393 |       if (guildData) {
    394 |         const updatedSettings = {
  > 395 |           ...guildData.settings,
        |                        ^^^^^^^^
    396 |           roleAccess: {
    397 |             enabled: true,
    398 |             tiers: [[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/automation/comprehensive-automation.service.ts:543:24[39m[22m
[1mTS2339: Property 'settings' does not exist on type 'Guild'.
    541 |       if (guildData) {
    542 |         const updatedSettings = {
  > 543 |           ...guildData.settings,
        |                        ^^^^^^^^
    544 |           aiAutomation: {
    545 |             smart_greetings: true,
    546 |             auto_engagement: true,[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/automation/comprehensive-automation.service.ts:734:36[39m[22m
[1mTS2339: Property 'settings' does not exist on type 'Guild'.
    732 |
    733 |       if (guild) {
  > 734 |         const setupHistory = guild.settings?.setupHistory || [];
        |                                    ^^^^^^^^
    735 |         setupHistory.push({
    736 |           trigger,
    737 |           timestamp: status.startTime.toISOString(),[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/automation/comprehensive-automation.service.ts:750:20[39m[22m
[1mTS2339: Property 'settings' does not exist on type 'Guild'.
    748 |
    749 |         const updatedSettings = {
  > 750 |           ...guild.settings,
        |                    ^^^^^^^^
    751 |           setupHistory,
    752 |           lastSetup: {
    753 |             timestamp: status.startTime.toISOString(),[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/automation/comprehensive-automation.service.ts:771:31[39m[22m
[1mTS2339: Property 'settings' does not exist on type 'Guild'.
    769 |       
    770 |       // Merge database config with default config to ensure all properties exist
  > 771 |       const dbConfig = guild?.settings?.automationConfig;
        |                               ^^^^^^^^
    772 |       if (!dbConfig) {
    773 |         return this.defaultConfig;
    774 |       }[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/core/actions/community-hub-actions-database.handler.ts:4:10[39m[22m
[1mTS2724: '"../../../../core/database/entities/community-events.entity"' has no exported member named 'CommunityFeedback'. [1m[32mDid you mean 'communityFeedback'?[39m[22m[1m
    2 | import { ButtonInteraction, StringSelectMenuInteraction, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle, StringSelectMenuBuilder, StringSelectMenuOptionBuilder } from 'discord.js';
    3 | import { CommunityDatabaseService, CommunityEventWithParticipants, LeaderboardEntryWithUser } from '../../services/community-database.service';
  > 4 | import { CommunityFeedback } from '../../../../core/database/entities/community-events.entity';
      |          ^^^^^^^^^^^^^^^^^
    5 |
    6 | @Injectable()
    7 | export class CommunityHubActionsDatabaseHandler {[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/core/actions/community-hub-actions-database.handler.ts:112:52[39m[22m
[1mTS2339: Property 'toLocaleDateString' does not exist on type 'string'.
    110 |             name: `${eventEmoji} ${event.title}`,
    111 |             value: `${event.description}\n` +
  > 112 |                    `📅 **Date:** ${event.startDate.toLocaleDateString()} at ${event.startDate.toLocaleTimeString()}\n` +
        |                                                    ^^^^^^^^^^^^^^^^^^
    113 |                    `👥 **Participants:** ${event.currentParticipants}${event.maxParticipants ? `/${event.maxParticipants}` : ''}\n` +
    114 |                    `${spotsLeft !== null ? `🎯 **Spots Left:** ${spotsLeft}\n` : ''}` +
    115 |                    `🏷️ **Tags:** ${event.tags?.join(', ') || 'No tags'}`,[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/core/actions/community-hub-actions-database.handler.ts:112:95[39m[22m
[1mTS2339: Property 'toLocaleTimeString' does not exist on type 'string'.
    110 |             name: `${eventEmoji} ${event.title}`,
    111 |             value: `${event.description}\n` +
  > 112 |                    `📅 **Date:** ${event.startDate.toLocaleDateString()} at ${event.startDate.toLocaleTimeString()}\n` +
        |                                                                                               ^^^^^^^^^^^^^^^^^^
    113 |                    `👥 **Participants:** ${event.currentParticipants}${event.maxParticipants ? `/${event.maxParticipants}` : ''}\n` +
    114 |                    `${spotsLeft !== null ? `🎯 **Spots Left:** ${spotsLeft}\n` : ''}` +
    115 |                    `🏷️ **Tags:** ${event.tags?.join(', ') || 'No tags'}`,[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/core/actions/community-hub-actions-database.handler.ts:390:65[39m[22m
[1mTS2339: Property 'toLocaleDateString' does not exist on type 'string'.
    388 |         .setDescription(`You have been registered for **${eventToJoin.title}**`)
    389 |         .addFields([
  > 390 |           { name: '📅 Event Date', value: eventToJoin.startDate.toLocaleDateString(), inline: true },
        |                                                                 ^^^^^^^^^^^^^^^^^^
    391 |           { name: '⏰ Event Time', value: eventToJoin.startDate.toLocaleTimeString(), inline: true },
    392 |           { name: '👥 Participants', value: `${eventToJoin.currentParticipants + 1}${eventToJoin.maxParticipants ? `/${eventToJoin.maxParticipants}` : ''}`, inline: true }
    393 |         ])[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/core/actions/community-hub-actions-database.handler.ts:391:64[39m[22m
[1mTS2339: Property 'toLocaleTimeString' does not exist on type 'string'.
    389 |         .addFields([
    390 |           { name: '📅 Event Date', value: eventToJoin.startDate.toLocaleDateString(), inline: true },
  > 391 |           { name: '⏰ Event Time', value: eventToJoin.startDate.toLocaleTimeString(), inline: true },
        |                                                                ^^^^^^^^^^^^^^^^^^
    392 |           { name: '👥 Participants', value: `${eventToJoin.currentParticipants + 1}${eventToJoin.maxParticipants ? `/${eventToJoin.maxParticipants}` : ''}`, inline: true }
    393 |         ])
    394 |         .setFooter({ text: 'You will receive a reminder before the event starts' })[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/core/actions/community-hub-actions-database.handler.ts:448:9[39m[22m
[1mTS2353: Object literal may only specify known properties, and 'type' does not exist in type 'Omit<CommunityFeedback, "id" | "createdAt" | "updatedAt">'.
    446 |         userId: interaction.user.id,
    447 |         guildId: interaction.guildId!,
  > 448 |         type: feedbackType as any,
        |         ^^^^
    449 |         title: sampleTitles[feedbackType as keyof typeof sampleTitles] || 'Feedback',
    450 |         description: 'Feedback submitted via community panel (detailed feedback would be collected via modal)'
    451 |       });[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/core/actions/technical-support-actions-database.handler.ts:4:10[39m[22m
[1mTS2724: '"../../../../core/database/entities/support-tickets.entity"' has no exported member named 'KnowledgeBaseArticle'. [1m[32mDid you mean 'knowledgeBaseArticles'?[39m[22m[1m
    2 | import { ButtonInteraction, StringSelectMenuInteraction, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle, StringSelectMenuBuilder, StringSelectMenuOptionBuilder } from 'discord.js';
    3 | import { SupportDatabaseService, SupportTicketWithResponses, TicketStats } from '../../services/support-database.service';
  > 4 | import { KnowledgeBaseArticle, TroubleshootingGuide, SystemStatus } from '../../../../core/database/entities/support-tickets.entity';
      |          ^^^^^^^^^^^^^^^^^^^^
    5 |
    6 | @Injectable()
    7 | export class TechnicalSupportActionsDatabaseHandler {[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/core/actions/technical-support-actions-database.handler.ts:4:32[39m[22m
[1mTS2724: '"../../../../core/database/entities/support-tickets.entity"' has no exported member named 'TroubleshootingGuide'. [1m[32mDid you mean 'troubleshootingGuides'?[39m[22m[1m
    2 | import { ButtonInteraction, StringSelectMenuInteraction, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle, StringSelectMenuBuilder, StringSelectMenuOptionBuilder } from 'discord.js';
    3 | import { SupportDatabaseService, SupportTicketWithResponses, TicketStats } from '../../services/support-database.service';
  > 4 | import { KnowledgeBaseArticle, TroubleshootingGuide, SystemStatus } from '../../../../core/database/entities/support-tickets.entity';
      |                                ^^^^^^^^^^^^^^^^^^^^
    5 |
    6 | @Injectable()
    7 | export class TechnicalSupportActionsDatabaseHandler {[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/core/actions/technical-support-actions-database.handler.ts:4:54[39m[22m
[1mTS2724: '"../../../../core/database/entities/support-tickets.entity"' has no exported member named 'SystemStatus'. [1m[32mDid you mean 'systemStatus'?[39m[22m[1m
    2 | import { ButtonInteraction, StringSelectMenuInteraction, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle, StringSelectMenuBuilder, StringSelectMenuOptionBuilder } from 'discord.js';
    3 | import { SupportDatabaseService, SupportTicketWithResponses, TicketStats } from '../../services/support-database.service';
  > 4 | import { KnowledgeBaseArticle, TroubleshootingGuide, SystemStatus } from '../../../../core/database/entities/support-tickets.entity';
      |                                                      ^^^^^^^^^^^^
    5 |
    6 | @Injectable()
    7 | export class TechnicalSupportActionsDatabaseHandler {[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/core/actions/technical-support-actions-database.handler.ts:167:67[39m[22m
[1mTS2339: Property 'difficulty' does not exist on type 'KnowledgeBaseArticle'.
    165 |
    166 |         for (const article of popularArticles.slice(0, 4)) {
  > 167 |           const difficultyEmoji = this.getDifficultyEmoji(article.difficulty);
        |                                                                   ^^^^^^^^^^
    168 |           const helpfulPercentage = article.helpful + article.notHelpful > 0 
    169 |             ? Math.round((article.helpful / (article.helpful + article.notHelpful)) * 100)
    170 |             : 0;[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/core/actions/technical-support-actions-database.handler.ts:174:67[39m[22m
[1mTS2551: Property 'subcategory' does not exist on type 'KnowledgeBaseArticle'. [1m[32mDid you mean 'category'?[39m[22m[1m
    172 |           embed.addFields([{
    173 |             name: `${difficultyEmoji} ${article.title}`,
  > 174 |             value: `**Category:** ${article.category} > ${article.subcategory}\n` +
        |                                                                   ^^^^^^^^^^^
    175 |                    `👀 **Views:** ${article.views} | 👍 **Helpful:** ${helpfulPercentage}%\n` +
    176 |                    `📅 **Updated:** ${article.updatedAt.toLocaleDateString()}\n` +
    177 |                    `🏷️ **Tags:** ${article.tags?.slice(0, 3).join(', ') || 'No tags'}`,[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/core/actions/technical-support-actions-database.handler.ts:176:57[39m[22m
[1mTS2339: Property 'toLocaleDateString' does not exist on type 'string'.
    174 |             value: `**Category:** ${article.category} > ${article.subcategory}\n` +
    175 |                    `👀 **Views:** ${article.views} | 👍 **Helpful:** ${helpfulPercentage}%\n` +
  > 176 |                    `📅 **Updated:** ${article.updatedAt.toLocaleDateString()}\n` +
        |                                                         ^^^^^^^^^^^^^^^^^^
    177 |                    `🏷️ **Tags:** ${article.tags?.slice(0, 3).join(', ') || 'No tags'}`,
    178 |             inline: false
    179 |           }]);[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/core/actions/technical-support-actions-database.handler.ts:317:84[39m[22m
[1mTS2339: Property 'solutions' does not exist on type 'TroubleshootingGuide'.
    315 |             value: `${guide.description.substring(0, 100)}${guide.description.length > 100 ? '...' : ''}\n` +
    316 |                    `⏱️ **Time:** ${timeEstimate} | 📈 **Success Rate:** ${guide.successRate}%\n` +
  > 317 |                    `🔧 **Difficulty:** ${guide.difficulty} | 📊 **Steps:** ${guide.solutions?.length || 0}\n` +
        |                                                                                    ^^^^^^^^^
    318 |                    `🏷️ **Category:** ${guide.category}`,
    319 |             inline: false
    320 |           }]);[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/core/actions/technical-support-actions-database.handler.ts:333:74[39m[22m
[1mTS2339: Property 'solutions' does not exist on type 'TroubleshootingGuide'.
    331 |         // Add troubleshooting statistics
    332 |         const avgSuccessRate = topGuides.reduce((sum, guide) => sum + guide.successRate, 0) / topGuides.length;
  > 333 |         const totalSteps = topGuides.reduce((sum, guide) => sum + (guide.solutions?.length || 0), 0);
        |                                                                          ^^^^^^^^^
    334 |         const avgTime = topGuides.reduce((sum, guide) => sum + guide.estimatedTime, 0) / topGuides.length;
    335 |
    336 |         embed.addFields([[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/core/actions/technical-support-actions-database.handler.ts:478:70[39m[22m
[1mTS2339: Property 'incidents' does not exist on type 'SystemStatus'.
    476 |       const avgUptime = systemStatus.reduce((sum, s) => sum + s.uptime, 0) / systemStatus.length;
    477 |       const avgResponseTime = systemStatus.reduce((sum, s) => sum + s.responseTime, 0) / systemStatus.length;
  > 478 |       const totalIncidents = systemStatus.reduce((sum, s) => sum + s.incidents, 0);
        |                                                                      ^^^^^^^^^
    479 |
    480 |       embed.addFields([
    481 |         {[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/core/actions/technical-support-actions-database.handler.ts:534:56[39m[22m
[1mTS2345: Argument of type '{ userId: string; guildId: string; title: string; description: string; category: string; priority: string; }' is not assignable to parameter of type 'Omit<SupportTicket, "id" | "createdAt" | "updatedAt" | "ticketNumber">'.
  Property 'status' is missing in type '{ userId: string; guildId: string; title: string; description: string; category: string; priority: string; }' but [1m[33mrequired[39m[22m[1m in type 'Omit<SupportTicket, "id" | "createdAt" | "updatedAt" | "ticketNumber">'.
    532 |       
    533 |       // Create a quick ticket with default values
  > 534 |       const ticket = await this.supportDb.createTicket({
        |                                                        ^
  > 535 |         userId: interaction.user.id,
        | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  > 536 |         guildId: interaction.guildId!,
        | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  > 537 |         title: 'Quick Support Request',
        | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  > 538 |         description: 'User submitted a quick support request via panel. Details to be provided in follow-up.',
        | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  > 539 |         category: 'general',
        | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  > 540 |         priority: 'medium'
        | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  > 541 |       });
        | ^^^^^^^^
    542 |
    543 |       const embed = new EmbedBuilder()
    544 |         .setColor(0x2ecc71)[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/core/actions/technical-support-actions-database.handler.ts:549:57[39m[22m
[1mTS2339: Property 'toLocaleDateString' does not exist on type 'string'.
    547 |         .addFields([
    548 |           { name: '🎫 Ticket Number', value: ticket.ticketNumber, inline: true },
  > 549 |           { name: '📅 Created', value: ticket.createdAt.toLocaleDateString(), inline: true },
        |                                                         ^^^^^^^^^^^^^^^^^^
    550 |           { name: '⏰ Priority', value: ticket.priority.toUpperCase(), inline: true },
    551 |           { name: '📝 Next Steps', value: 'Our support team will review your ticket and respond within 24 hours. You can add more details by replying to this ticket.', inline: false }
    552 |         ])[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/services/ai-mastery-database.service.ts:514:9[39m[22m
[1mTS2322: Type 'Date' is not assignable to type 'string'.
    512 |         timeSpent,
    513 |         isCompleted,
  > 514 |         lastAccessedAt: new Date()
        |         ^^^^^^^^^^^^^^
    515 |       };
    516 |       
    517 |       if (isCompleted && !existingProgress.isCompleted) {[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/services/ai-mastery-database.service.ts:518:9[39m[22m
[1mTS2322: Type 'Date' is not assignable to type 'string'.
    516 |       
    517 |       if (isCompleted && !existingProgress.isCompleted) {
  > 518 |         updateData.completedAt = new Date();
        |         ^^^^^^^^^^^^^^^^^^^^^^
    519 |       }
    520 |       
    521 |       await this.redisDb.update<TutorialProgress>([39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/services/community-database.service.ts:219:11[39m[22m
[1mTS2353: Object literal may only specify known properties, and 'createdAt' does not exist in type 'EventParticipant'.
    217 |           joinedAt: new Date().toISOString(),
    218 |           status: 'registered',
  > 219 |           createdAt: new Date().toISOString(),
        |           ^^^^^^^^^
    220 |           updatedAt: new Date().toISOString()
    221 |         };
    222 |[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/services/community-database.service.ts:225:40[39m[22m
[1mTS2345: Argument of type 'Record<string, string>' is not assignable to parameter of type 'string | number | Buffer<ArrayBufferLike>'.
  Type 'Record<string, string>' is missing the following properties from type 'Buffer<ArrayBufferLike>': slice, subarray, write, toJSON, and 97 more.
    223 |         // Add to Redis
    224 |         const participantKey = `entity:${this.EVENT_PARTICIPANT}:${participant.id}`;
  > 225 |         pipeline.hmset(participantKey, this.serializeEntity(participant));
        |                                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    226 |
    227 |         // Update indexes
    228 |         pipeline.sadd(`index:${this.EVENT_PARTICIPANT}:eventId:${eventId}`, participant.id);[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/services/dynamic-content.service.ts:14:3[39m[22m
[1mTS2724: '"@/core/database"' has no exported member named 'DynamicContentSource'. [1m[32mDid you mean 'dynamicContentSources'?[39m[22m[1m
    12 |   panelContentMappings,
    13 |   contentFreshnessTracking,
  > 14 |   DynamicContentSource,
       |   ^^^^^^^^^^^^^^^^^^^^
    15 |   NewDynamicContentSource,
    16 |   DynamicContentCache,
    17 |   NewDynamicContentCache,[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/services/dynamic-content.service.ts:15:3[39m[22m
[1mTS2724: '"@/core/database"' has no exported member named 'NewDynamicContentSource'. [1m[32mDid you mean 'dynamicContentSources'?[39m[22m[1m
    13 |   contentFreshnessTracking,
    14 |   DynamicContentSource,
  > 15 |   NewDynamicContentSource,
       |   ^^^^^^^^^^^^^^^^^^^^^^^
    16 |   DynamicContentCache,
    17 |   NewDynamicContentCache,
    18 |   PanelContentMapping,[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/services/dynamic-content.service.ts:16:3[39m[22m
[1mTS2724: '"@/core/database"' has no exported member named 'DynamicContentCache'. [1m[32mDid you mean 'dynamicContentCache'?[39m[22m[1m
    14 |   DynamicContentSource,
    15 |   NewDynamicContentSource,
  > 16 |   DynamicContentCache,
       |   ^^^^^^^^^^^^^^^^^^^
    17 |   NewDynamicContentCache,
    18 |   PanelContentMapping,
    19 |   ContentFreshnessTracking,[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/services/dynamic-content.service.ts:17:3[39m[22m
[1mTS2724: '"@/core/database"' has no exported member named 'NewDynamicContentCache'. [1m[32mDid you mean 'dynamicContentCache'?[39m[22m[1m
    15 |   NewDynamicContentSource,
    16 |   DynamicContentCache,
  > 17 |   NewDynamicContentCache,
       |   ^^^^^^^^^^^^^^^^^^^^^^
    18 |   PanelContentMapping,
    19 |   ContentFreshnessTracking,
    20 | } from '@/core/database';[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/services/dynamic-content.service.ts:18:3[39m[22m
[1mTS2724: '"@/core/database"' has no exported member named 'PanelContentMapping'. [1m[32mDid you mean 'panelContentMappings'?[39m[22m[1m
    16 |   DynamicContentCache,
    17 |   NewDynamicContentCache,
  > 18 |   PanelContentMapping,
       |   ^^^^^^^^^^^^^^^^^^^
    19 |   ContentFreshnessTracking,
    20 | } from '@/core/database';
    21 |[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/services/dynamic-content.service.ts:19:3[39m[22m
[1mTS2724: '"@/core/database"' has no exported member named 'ContentFreshnessTracking'. [1m[32mDid you mean 'contentFreshnessTracking'?[39m[22m[1m
    17 |   NewDynamicContentCache,
    18 |   PanelContentMapping,
  > 19 |   ContentFreshnessTracking,
       |   ^^^^^^^^^^^^^^^^^^^^^^^^
    20 | } from '@/core/database';
    21 |
    22 | interface ContentRefreshResult {[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/services/dynamic-content.service.ts:55:39[39m[22m
[1mTS2551: Property 'getDb' does not exist on type 'DatabaseService'. [1m[32mDid you mean 'get'?[39m[22m[1m
    53 |   async registerContentSource(source: NewDynamicContentSource): Promise<string | null> {
    54 |     try {
  > 55 |       const db = this.databaseService.getDb();
       |                                       ^^^^^
    56 |       
    57 |       const created = await db
    58 |         .insert(dynamicContentSources)[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/services/dynamic-content.service.ts:77:39[39m[22m
[1mTS2551: Property 'getDb' does not exist on type 'DatabaseService'. [1m[32mDid you mean 'get'?[39m[22m[1m
    75 |   async getContentForPanel(panelId: string): Promise<any[]> {
    76 |     try {
  > 77 |       const db = this.databaseService.getDb();
       |                                       ^^^^^
    78 |       
    79 |       // Get all content mappings for this panel
    80 |       const mappings = await db[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/services/dynamic-content.service.ts:123:39[39m[22m
[1mTS2551: Property 'getDb' does not exist on type 'DatabaseService'. [1m[32mDid you mean 'get'?[39m[22m[1m
    121 |       }
    122 |
  > 123 |       const db = this.databaseService.getDb();
        |                                       ^^^^^
    124 |       
    125 |       // Get source configuration
    126 |       const sources = await db[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/services/dynamic-content.service.ts:371:39[39m[22m
[1mTS2551: Property 'getDb' does not exist on type 'DatabaseService'. [1m[32mDid you mean 'get'?[39m[22m[1m
    369 |   ): Promise<void> {
    370 |     try {
  > 371 |       const db = this.databaseService.getDb();
        |                                       ^^^^^
    372 |       const serialized = JSON.stringify(data);
    373 |       const checksum = this.generateChecksum(serialized);
    374 |       const cacheConfig = source.config.caching;[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/services/dynamic-content.service.ts:433:39[39m[22m
[1mTS2551: Property 'getDb' does not exist on type 'DatabaseService'. [1m[32mDid you mean 'get'?[39m[22m[1m
    431 |   private async getCachedContent(sourceId: string): Promise<DynamicContentCache | null> {
    432 |     try {
  > 433 |       const db = this.databaseService.getDb();
        |                                       ^^^^^
    434 |       
    435 |       const cached = await db
    436 |         .select()[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/services/dynamic-content.service.ts:474:39[39m[22m
[1mTS2551: Property 'getDb' does not exist on type 'DatabaseService'. [1m[32mDid you mean 'get'?[39m[22m[1m
    472 |   async checkContentFreshness(sourceId: string): Promise<boolean> {
    473 |     try {
  > 474 |       const db = this.databaseService.getDb();
        |                                       ^^^^^
    475 |       
    476 |       // Get freshness tracking info
    477 |       const tracking = await db[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/services/dynamic-content.service.ts:519:39[39m[22m
[1mTS2551: Property 'getDb' does not exist on type 'DatabaseService'. [1m[32mDid you mean 'get'?[39m[22m[1m
    517 |   async refreshStaleContent() {
    518 |     try {
  > 519 |       const db = this.databaseService.getDb();
        |                                       ^^^^^
    520 |       
    521 |       // Get sources that need refresh
    522 |       const now = new Date();[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/services/dynamic-content.service.ts:529:13[39m[22m
[1mTS2304: Cannot find name 'lt'.
    527 |           and(
    528 |             eq(dynamicContentSources.isActive, true),
  > 529 |             lt(dynamicContentSources.lastFetched, new Date(now.getTime() - 5 * 60 * 1000))
        |             ^^
    530 |           )
    531 |         );
    532 |[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/services/dynamic-content.service.ts:686:39[39m[22m
[1mTS2551: Property 'getDb' does not exist on type 'DatabaseService'. [1m[32mDid you mean 'get'?[39m[22m[1m
    684 |   private async updateErrorCount(sourceId: string, errorMessage: string): Promise<void> {
    685 |     try {
  > 686 |       const db = this.databaseService.getDb();
        |                                       ^^^^^
    687 |       
    688 |       await db
    689 |         .update(dynamicContentSources)[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/services/networking-database.service.ts:12:10[39m[22m
[1mTS2724: '"@/core/database"' has no exported member named 'BusinessOpportunity'. [1m[32mDid you mean 'businessOpportunities'?[39m[22m[1m
    10 |     professionalProfiles,
    11 |     skillEndorsements,
  > 12 |     type BusinessOpportunity,
       |          ^^^^^^^^^^^^^^^^^^^
    13 |     type JobApplication,
    14 |     type JobPosting,
    15 |     type NewBusinessOpportunity,[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/services/networking-database.service.ts:13:10[39m[22m
[1mTS2724: '"@/core/database"' has no exported member named 'JobApplication'. [1m[32mDid you mean 'jobApplications'?[39m[22m[1m
    11 |     skillEndorsements,
    12 |     type BusinessOpportunity,
  > 13 |     type JobApplication,
       |          ^^^^^^^^^^^^^^
    14 |     type JobPosting,
    15 |     type NewBusinessOpportunity,
    16 |     type NewJobPosting,[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/services/networking-database.service.ts:14:10[39m[22m
[1mTS2724: '"@/core/database"' has no exported member named 'JobPosting'. [1m[32mDid you mean 'jobPostings'?[39m[22m[1m
    12 |     type BusinessOpportunity,
    13 |     type JobApplication,
  > 14 |     type JobPosting,
       |          ^^^^^^^^^^
    15 |     type NewBusinessOpportunity,
    16 |     type NewJobPosting,
    17 |     type NewProfessionalProfile,[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/services/networking-database.service.ts:15:10[39m[22m
[1mTS2724: '"@/core/database"' has no exported member named 'NewBusinessOpportunity'. [1m[32mDid you mean 'businessOpportunities'?[39m[22m[1m
    13 |     type JobApplication,
    14 |     type JobPosting,
  > 15 |     type NewBusinessOpportunity,
       |          ^^^^^^^^^^^^^^^^^^^^^^
    16 |     type NewJobPosting,
    17 |     type NewProfessionalProfile,
    18 |     type ProfessionalConnection,[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/services/networking-database.service.ts:16:10[39m[22m
[1mTS2724: '"@/core/database"' has no exported member named 'NewJobPosting'. [1m[32mDid you mean 'jobPostings'?[39m[22m[1m
    14 |     type JobPosting,
    15 |     type NewBusinessOpportunity,
  > 16 |     type NewJobPosting,
       |          ^^^^^^^^^^^^^
    17 |     type NewProfessionalProfile,
    18 |     type ProfessionalConnection,
    19 |     type ProfessionalProfile,[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/services/networking-database.service.ts:17:10[39m[22m
[1mTS2724: '"@/core/database"' has no exported member named 'NewProfessionalProfile'. [1m[32mDid you mean 'professionalProfiles'?[39m[22m[1m
    15 |     type NewBusinessOpportunity,
    16 |     type NewJobPosting,
  > 17 |     type NewProfessionalProfile,
       |          ^^^^^^^^^^^^^^^^^^^^^^
    18 |     type ProfessionalConnection,
    19 |     type ProfessionalProfile,
    20 |     type SkillEndorsement[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/services/networking-database.service.ts:18:10[39m[22m
[1mTS2724: '"@/core/database"' has no exported member named 'ProfessionalConnection'. [1m[32mDid you mean 'professionalConnections'?[39m[22m[1m
    16 |     type NewJobPosting,
    17 |     type NewProfessionalProfile,
  > 18 |     type ProfessionalConnection,
       |          ^^^^^^^^^^^^^^^^^^^^^^
    19 |     type ProfessionalProfile,
    20 |     type SkillEndorsement
    21 | } from '@/core/database';[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/services/networking-database.service.ts:19:10[39m[22m
[1mTS2724: '"@/core/database"' has no exported member named 'ProfessionalProfile'. [1m[32mDid you mean 'professionalProfiles'?[39m[22m[1m
    17 |     type NewProfessionalProfile,
    18 |     type ProfessionalConnection,
  > 19 |     type ProfessionalProfile,
       |          ^^^^^^^^^^^^^^^^^^^
    20 |     type SkillEndorsement
    21 | } from '@/core/database';
    22 | import { users } from '@/core/database';[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/services/networking-database.service.ts:20:10[39m[22m
[1mTS2724: '"@/core/database"' has no exported member named 'SkillEndorsement'. [1m[32mDid you mean 'skillEndorsements'?[39m[22m[1m
    18 |     type ProfessionalConnection,
    19 |     type ProfessionalProfile,
  > 20 |     type SkillEndorsement
       |          ^^^^^^^^^^^^^^^^
    21 | } from '@/core/database';
    22 | import { users } from '@/core/database';
    23 | import { eq, and, or, desc, asc, count, sql } from 'drizzle-orm';[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/services/networking-database.service.ts:55:33[39m[22m
[1mTS2551: Property 'getDb' does not exist on type 'DatabaseService'. [1m[32mDid you mean 'get'?[39m[22m[1m
    53 |
    54 |   private get db() {
  > 55 |     return this.databaseService.getDb();
       |                                 ^^^^^
    56 |   }
    57 |
    58 |   // Professional Profile Management[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/services/networking-database.service.ts:154:30[39m[22m
[1mTS2304: Cannot find name 'ilike'.
    152 |       }
    153 |       if (filters?.location) {
  > 154 |         whereConditions.push(ilike(professionalProfiles.location, `%${filters.location}%`));
        |                              ^^^^^
    155 |       }
    156 |       if (filters?.availableForHire !== undefined) {
    157 |         whereConditions.push(eq(professionalProfiles.isAvailableForHire, filters.availableForHire));[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/services/networking-database.service.ts:234:16[39m[22m
[1mTS2769: No overload matches this call.
  Overload 1 of 3, '(left: PgColumn<{ name: "id"; tableName: "professional_connections"; dataType: "string"; columnType: "PgText"; data: string; driverParam: string; notNull: true; hasDefault: false; isPrimaryKey: true; isAutoincrement: false; hasRuntimeDefault: false; enumValues: [...]; baseColumn: never; generated: GeneratedColumnConfig<...>; }, {}, {}>, right: string | SQLWrapper): SQL<...>', gave the following [1m[31merror[39m[22m[1m.
    Argument of type 'number' is not assignable to parameter of type 'string | SQLWrapper'.
  Overload 2 of 3, '(left: Aliased<number>, right: number | SQLWrapper): SQL<unknown>', gave the following [1m[31merror[39m[22m[1m.
    Argument of type 'PgColumn<{ name: "id"; tableName: "professional_connections"; dataType: "string"; columnType: "PgText"; data: string; driverParam: string; notNull: true; hasDefault: false; isPrimaryKey: true; isAutoincrement: false; hasRuntimeDefault: false; enumValues: [...]; baseColumn: never; generated: GeneratedColumnConfig<......' is not assignable to parameter of type 'Aliased<number>'.
      Type 'PgColumn<{ name: "id"; tableName: "professional_connections"; dataType: "string"; columnType: "PgText"; data: string; driverParam: string; notNull: true; hasDefault: false; isPrimaryKey: true; isAutoincrement: false; hasRuntimeDefault: false; enumValues: [...]; baseColumn: never; generated: GeneratedColumnConfig<......' is missing the following properties from type 'Aliased<number>': sql, fieldAlias
  Overload 3 of 3, '(left: never, right: unknown): SQL<unknown>', gave the following [1m[31merror[39m[22m[1m.
    Argument of type 'PgColumn<{ name: "id"; tableName: "professional_connections"; dataType: "string"; columnType: "PgText"; data: string; driverParam: string; notNull: true; hasDefault: false; isPrimaryKey: true; isAutoincrement: false; hasRuntimeDefault: false; enumValues: [...]; baseColumn: never; generated: GeneratedColumnConfig<......' is not assignable to parameter of type 'never'.
    232 |           acceptedAt: response === 'accepted' ? new Date() : undefined
    233 |         } as any)
  > 234 |         .where(eq(professionalConnections.id, connectionId));
        |                ^^
    235 |
    236 |       if (response === 'accepted') {
    237 |         // Update connection counts[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/services/networking-database.service.ts:241:18[39m[22m
[1mTS2769: No overload matches this call.
  Overload 1 of 3, '(left: PgColumn<{ name: "id"; tableName: "professional_connections"; dataType: "string"; columnType: "PgText"; data: string; driverParam: string; notNull: true; hasDefault: false; isPrimaryKey: true; isAutoincrement: false; hasRuntimeDefault: false; enumValues: [...]; baseColumn: never; generated: GeneratedColumnConfig<...>; }, {}, {}>, right: string | SQLWrapper): SQL<...>', gave the following [1m[31merror[39m[22m[1m.
    Argument of type 'number' is not assignable to parameter of type 'string | SQLWrapper'.
  Overload 2 of 3, '(left: Aliased<number>, right: number | SQLWrapper): SQL<unknown>', gave the following [1m[31merror[39m[22m[1m.
    Argument of type 'PgColumn<{ name: "id"; tableName: "professional_connections"; dataType: "string"; columnType: "PgText"; data: string; driverParam: string; notNull: true; hasDefault: false; isPrimaryKey: true; isAutoincrement: false; hasRuntimeDefault: false; enumValues: [...]; baseColumn: never; generated: GeneratedColumnConfig<......' is not assignable to parameter of type 'Aliased<number>'.
      Type 'PgColumn<{ name: "id"; tableName: "professional_connections"; dataType: "string"; columnType: "PgText"; data: string; driverParam: string; notNull: true; hasDefault: false; isPrimaryKey: true; isAutoincrement: false; hasRuntimeDefault: false; enumValues: [...]; baseColumn: never; generated: GeneratedColumnConfig<......' is missing the following properties from type 'Aliased<number>': sql, fieldAlias
  Overload 3 of 3, '(left: never, right: unknown): SQL<unknown>', gave the following [1m[31merror[39m[22m[1m.
    Argument of type 'PgColumn<{ name: "id"; tableName: "professional_connections"; dataType: "string"; columnType: "PgText"; data: string; driverParam: string; notNull: true; hasDefault: false; isPrimaryKey: true; isAutoincrement: false; hasRuntimeDefault: false; enumValues: [...]; baseColumn: never; generated: GeneratedColumnConfig<......' is not assignable to parameter of type 'never'.
    239 |           .select()
    240 |           .from(professionalConnections)
  > 241 |           .where(eq(professionalConnections.id, connectionId))
        |                  ^^
    242 |           .limit(1);
    243 |
    244 |         if (connection) {[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/services/networking-database.service.ts:250:20[39m[22m
[1mTS2304: Cannot find name 'inArray'.
    248 |               connectionCount: sql`${professionalProfiles.connectionCount} + 1`
    249 |             } as any)
  > 250 |             .where(inArray(professionalProfiles.userId, [connection.requesterId, connection.receiverId]));
        |                    ^^^^^^^
    251 |         }
    252 |       }
    253 |[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/services/networking-database.service.ts:362:9[39m[22m
[1mTS2304: Cannot find name 'gte'.
    360 |         eq(jobPostings.guildId, guildId),
    361 |         eq(jobPostings.isActive, true),
  > 362 |         gte(jobPostings.expiresAt, new Date())
        |         ^^^
    363 |       ];
    364 |       
    365 |       if (filters?.jobType) {[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/services/networking-database.service.ts:378:30[39m[22m
[1mTS2304: Cannot find name 'ilike'.
    376 |       }
    377 |       if (filters?.location) {
  > 378 |         whereConditions.push(ilike(jobPostings.location, `%${filters.location}%`));
        |                              ^^^^^
    379 |       }
    380 |
    381 |       if (searchTerm) {[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/services/networking-database.service.ts:481:16[39m[22m
[1mTS2769: No overload matches this call.
  Overload 1 of 3, '(left: PgColumn<{ name: "id"; tableName: "job_postings"; dataType: "string"; columnType: "PgText"; data: string; driverParam: string; notNull: true; hasDefault: false; isPrimaryKey: true; isAutoincrement: false; hasRuntimeDefault: false; enumValues: [...]; baseColumn: never; generated: GeneratedColumnConfig<...>; }, {}, {}>, right: string | SQLWrapper): SQL<...>', gave the following [1m[31merror[39m[22m[1m.
    Argument of type 'number' is not assignable to parameter of type 'string | SQLWrapper'.
  Overload 2 of 3, '(left: Aliased<number>, right: number | SQLWrapper): SQL<unknown>', gave the following [1m[31merror[39m[22m[1m.
    Argument of type 'PgColumn<{ name: "id"; tableName: "job_postings"; dataType: "string"; columnType: "PgText"; data: string; driverParam: string; notNull: true; hasDefault: false; isPrimaryKey: true; isAutoincrement: false; hasRuntimeDefault: false; enumValues: [...]; baseColumn: never; generated: GeneratedColumnConfig<...>; }, {}, {}>' is not assignable to parameter of type 'Aliased<number>'.
      Type 'PgColumn<{ name: "id"; tableName: "job_postings"; dataType: "string"; columnType: "PgText"; data: string; driverParam: string; notNull: true; hasDefault: false; isPrimaryKey: true; isAutoincrement: false; hasRuntimeDefault: false; enumValues: [...]; baseColumn: never; generated: GeneratedColumnConfig<...>; }, {}, {}>' is missing the following properties from type 'Aliased<number>': sql, fieldAlias
  Overload 3 of 3, '(left: never, right: unknown): SQL<unknown>', gave the following [1m[31merror[39m[22m[1m.
    Argument of type 'PgColumn<{ name: "id"; tableName: "job_postings"; dataType: "string"; columnType: "PgText"; data: string; driverParam: string; notNull: true; hasDefault: false; isPrimaryKey: true; isAutoincrement: false; hasRuntimeDefault: false; enumValues: [...]; baseColumn: never; generated: GeneratedColumnConfig<...>; }, {}, {}>' is not assignable to parameter of type 'never'.
    479 |           applicationCount: sql`${jobPostings.applicationCount} + 1`
    480 |         } as any)
  > 481 |         .where(eq(jobPostings.id, jobId));
        |                ^^
    482 |
    483 |       this.logger.log(`User ${applicantId} applied to job ${jobId}`);
    484 |       return { success: true, message: 'Application submitted successfully!' };[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/services/networking-database.service.ts:534:9[39m[22m
[1mTS2304: Cannot find name 'gte'.
    532 |         eq(businessOpportunities.guildId, guildId),
    533 |         eq(businessOpportunities.isActive, true),
  > 534 |         gte(businessOpportunities.expiresAt, new Date())
        |         ^^^
    535 |       ];
    536 |       
    537 |       if (filters?.type) {[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/services/networking-database.service.ts:544:30[39m[22m
[1mTS2304: Cannot find name 'ilike'.
    542 |       }
    543 |       if (filters?.location) {
  > 544 |         whereConditions.push(ilike(businessOpportunities.location, `%${filters.location}%`));
        |                              ^^^^^
    545 |       }
    546 |       if (filters?.isRemote !== undefined) {
    547 |         whereConditions.push(eq(businessOpportunities.isRemote, filters.isRemote));[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/services/networking-database.service.ts:601:16[39m[22m
[1mTS2769: No overload matches this call.
  Overload 1 of 3, '(left: PgColumn<{ name: "id"; tableName: "business_opportunities"; dataType: "string"; columnType: "PgText"; data: string; driverParam: string; notNull: true; hasDefault: false; isPrimaryKey: true; isAutoincrement: false; hasRuntimeDefault: false; enumValues: [...]; baseColumn: never; generated: GeneratedColumnConfig<...>; }, {}, {}>, right: string | SQLWrapper): SQL<...>', gave the following [1m[31merror[39m[22m[1m.
    Argument of type 'number' is not assignable to parameter of type 'string | SQLWrapper'.
  Overload 2 of 3, '(left: Aliased<number>, right: number | SQLWrapper): SQL<unknown>', gave the following [1m[31merror[39m[22m[1m.
    Argument of type 'PgColumn<{ name: "id"; tableName: "business_opportunities"; dataType: "string"; columnType: "PgText"; data: string; driverParam: string; notNull: true; hasDefault: false; isPrimaryKey: true; isAutoincrement: false; hasRuntimeDefault: false; enumValues: [...]; baseColumn: never; generated: GeneratedColumnConfig<...>;...' is not assignable to parameter of type 'Aliased<number>'.
      Type 'PgColumn<{ name: "id"; tableName: "business_opportunities"; dataType: "string"; columnType: "PgText"; data: string; driverParam: string; notNull: true; hasDefault: false; isPrimaryKey: true; isAutoincrement: false; hasRuntimeDefault: false; enumValues: [...]; baseColumn: never; generated: GeneratedColumnConfig<...>;...' is missing the following properties from type 'Aliased<number>': sql, fieldAlias
  Overload 3 of 3, '(left: never, right: unknown): SQL<unknown>', gave the following [1m[31merror[39m[22m[1m.
    Argument of type 'PgColumn<{ name: "id"; tableName: "business_opportunities"; dataType: "string"; columnType: "PgText"; data: string; driverParam: string; notNull: true; hasDefault: false; isPrimaryKey: true; isAutoincrement: false; hasRuntimeDefault: false; enumValues: [...]; baseColumn: never; generated: GeneratedColumnConfig<...>;...' is not assignable to parameter of type 'never'.
    599 |           interestCount: sql`${businessOpportunities.interestCount} + 1`
    600 |         } as any)
  > 601 |         .where(eq(businessOpportunities.id, opportunityId));
        |                ^^
    602 |
    603 |       this.logger.log(`User ${userId} expressed interest in opportunity ${opportunityId}`);
    604 |       return { success: true, message: 'Interest expressed successfully!' };[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/services/networking-database.service.ts:689:25[39m[22m
[1mTS2339: Property 'discordId' does not exist on type '{ readonly $inferSelect: User; readonly $inferInsert: CreateUser; }'.
    687 |         .select()
    688 |         .from(users)
  > 689 |         .where(eq(users.discordId, discordId))
        |                         ^^^^^^^^^
    690 |         .limit(1);
    691 |
    692 |       if (!existingUser) {[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/services/networking-database.service.ts:710:27[39m[22m
[1mTS2339: Property 'discordId' does not exist on type '{ readonly $inferSelect: User; readonly $inferInsert: CreateUser; }'.
    708 |             lastActivityAt: new Date()
    709 |           } as any)
  > 710 |           .where(eq(users.discordId, discordId));
        |                           ^^^^^^^^^
    711 |       }
    712 |     } catch ([1m[31merror[39m[22m[1m) {
    713 |       this.logger.[1m[31merror[39m[22m[1m('[1m[31mFailed[39m[22m[1m to ensure user:', [1m[31merror[39m[22m[1m);[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/services/ticket-system.service.ts:84:11[39m[22m
[1mTS2322: Type 'Date' is not assignable to type 'string'.
    82 |           description: `**Category:** ${category.charAt(0).toUpperCase() + category.slice(1)}\n**Priority:** ${priority.toUpperCase()}\n**Created by:** ${user.tag}`,
    83 |           color: priority === 'critical' ? 0xFF0000 : priority === 'high' ? 0xFF9800 : priority === 'medium' ? 0x2196F3 : 0x4CAF50,
  > 84 |           timestamp: new Date(),
       |           ^^^^^^^^^
    85 |           footer: { text: 'React with ✅ when resolved or 🔒 to close ticket' }
    86 |         }]
    87 |       });[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/channel-panels/services/ticket-system.service.ts:121:11[39m[22m
[1mTS2322: Type 'Date' is not assignable to type 'string'.
    119 |           description: `This ticket has been closed by ${closedBy}.`,
    120 |           color: 0x607D8B,
  > 121 |           timestamp: new Date()
        |           ^^^^^^^^^
    122 |         }]
    123 |       });
    124 |[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/dev-on-demand/dev-on-demand.service.ts:171:21[39m[22m
[1mTS2353: Object literal may only specify known properties, and 'devOnDemand' does not exist in type 'GuildSettings'.
    169 |           discordId: interaction.guild.id,
    170 |           name: interaction.guild.name,
  > 171 |           config: { devOnDemand: { enabled: false } },
        |                     ^^^^^^^^^^^
    172 |           isActive: true,
    173 |           ownerDiscordId: interaction.guild.ownerId || interaction.user.id,
    174 |           welcomeEnabled: false,[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/dev-on-demand/dev-on-demand.service.ts:214:26[39m[22m
[1mTS2339: Property 'devOnDemand' does not exist on type 'GuildSettings'.
    212 |
    213 |       // Initialize settings
  > 214 |       if (!guild.config?.devOnDemand) {
        |                          ^^^^^^^^^^^
    215 |         guild.config = {
    216 |           ...guild.config,
    217 |           devOnDemand: {[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/dev-on-demand/dev-on-demand.service.ts:217:11[39m[22m
[1mTS2353: Object literal may only specify known properties, and 'devOnDemand' does not exist in type 'GuildSettings'.
    215 |         guild.config = {
    216 |           ...guild.config,
  > 217 |           devOnDemand: {
        |           ^^^^^^^^^^^
    218 |             enabled: false,
    219 |             requestChannel: null,
    220 |             notificationChannel: null,[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/dev-on-demand/dev-on-demand.service.ts:258:42[39m[22m
[1mTS2339: Property 'devOnDemand' does not exist on type 'GuildSettings'.
    256 |       const guild = await this.redisDatabaseService.guilds.findByDiscordId(interaction.guild.id);
    257 |
  > 258 |       const devSettings = guild?.config?.devOnDemand;
        |                                          ^^^^^^^^^^^
    259 |       if (!devSettings?.enabled) {
    260 |         await interaction.reply({
    261 |           content: '❌ Developer-on-demand system is not enabled in this server.',[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/dev-on-demand/dev-on-demand.service.ts:380:42[39m[22m
[1mTS2339: Property 'devOnDemand' does not exist on type 'GuildSettings'.
    378 |       const guild = await this.redisDatabaseService.guilds.findByDiscordId(interaction.guild.id);
    379 |
  > 380 |       const devSettings = guild?.config?.devOnDemand;
        |                                          ^^^^^^^^^^^
    381 |       if (!devSettings?.enabled) {
    382 |         await interaction.reply({
    383 |           content: '❌ Developer-on-demand system is not enabled in this server.',[39m[22m

[1m[31mERROR[39m[22m in [1m./src/features/dev-on-demand/dev-on-demand.service.ts:940:41[39m[22m
[1mTS2339: Property 'devOnDemand' does not exist on type 'GuildSettings'.
    938 |       }
    939 |
  > 940 |       const devSettings = guild.config?.devOnDemand || {};
        |                                         ^^^^^^^^^^^
    941 |
    942 |       switch (setting.toLowerCase()) {
    943 |         case 'enable':[39m[22m

webpack 5.97.1 compiled with [1m[31m279 errors[39m[22m in 15848 ms
