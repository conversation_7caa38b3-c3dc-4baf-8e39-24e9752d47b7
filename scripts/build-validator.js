#!/usr/bin/env node

/**
 * Build Validator for Discord Bot EnergeX
 * Validates build artifacts and ensures deployment readiness
 * 
 * Usage: node scripts/build-validator.js [--strict] [--report]
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const config = {
  strict: false,
  generateReport: false,
  verbose: false
};

// Parse arguments
process.argv.slice(2).forEach(arg => {
  if (arg === '--strict') config.strict = true;
  else if (arg === '--report') config.generateReport = true;
  else if (arg === '--verbose') config.verbose = true;
});

// Colors
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m'
};

// Logging
const log = {
  info: (msg) => console.log(`${colors.blue}ℹ️  ${msg}${colors.reset}`),
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`),
  error: (msg) => console.error(`${colors.red}❌ ${msg}${colors.reset}`)
};

// Validation results
const results = {
  passed: [],
  failed: [],
  warnings: [],
  metrics: {}
};

// Build artifact validation
function validateBuildArtifacts() {
  log.info('Validating build artifacts...');
  
  const requiredFiles = [
    'dist/src/main.js',
    'dist/src/app.module.js'
  ];
  
  const requiredDirs = [
    'dist/src',
    'dist/src/core',
    'dist/src/discord',
    'dist/src/api'
  ];
  
  // Check required files
  for (const file of requiredFiles) {
    if (!fs.existsSync(file)) {
      results.failed.push(`Missing build artifact: ${file}`);
      return false;
    }
  }
  
  // Check required directories
  for (const dir of requiredDirs) {
    if (!fs.existsSync(dir)) {
      results.failed.push(`Missing build directory: ${dir}`);
      return false;
    }
  }
  
  results.passed.push('Build artifacts validation');
  return true;
}

// Syntax validation
function validateSyntax() {
  log.info('Validating JavaScript syntax...');
  
  try {
    const jsFiles = execSync('find dist -name "*.js"', { encoding: 'utf8' })
      .trim()
      .split('\n')
      .filter(file => file.length > 0);
    
    let syntaxErrors = 0;
    
    for (const file of jsFiles.slice(0, 10)) { // Check first 10 files
      try {
        execSync(`node -c "${file}"`, { stdio: 'pipe' });
      } catch (error) {
        syntaxErrors++;
        results.failed.push(`Syntax error in ${file}`);
        
        if (syntaxErrors >= 3) break; // Stop after 3 errors
      }
    }
    
    if (syntaxErrors === 0) {
      results.passed.push('JavaScript syntax validation');
      return true;
    } else {
      return false;
    }
  } catch (error) {
    results.failed.push(`Syntax validation failed: ${error.message}`);
    return false;
  }
}

// Module resolution validation
function validateModuleResolution() {
  log.info('Validating module resolution...');
  
  try {
    // Test if main module can be required
    const mainPath = path.resolve('dist/src/main.js');
    
    // Create a test script to validate module loading
    const testScript = `
      try {
        // Test basic require
        const mainModule = require('${mainPath}');
        console.log('Module resolution: OK');
        process.exit(0);
      } catch (error) {
        console.error('Module resolution failed:', error.message);
        process.exit(1);
      }
    `;
    
    execSync(`node -e "${testScript}"`, { stdio: 'pipe' });
    results.passed.push('Module resolution validation');
    return true;
  } catch (error) {
    results.failed.push(`Module resolution failed: ${error.message}`);
    return false;
  }
}

// Performance analysis
function analyzePerformance() {
  log.info('Analyzing build performance...');
  
  try {
    // Calculate build size metrics
    const totalSize = parseInt(execSync('du -sb dist', { encoding: 'utf8' }).split('\t')[0]);
    const jsSize = parseInt(execSync('find dist -name "*.js" -exec du -cb {} + | tail -1', { encoding: 'utf8' }).split('\t')[0]);
    const mapSize = parseInt(execSync('find dist -name "*.map" -exec du -cb {} + 2>/dev/null | tail -1 || echo "0"', { encoding: 'utf8' }).split('\t')[0]);
    
    results.metrics = {
      totalSize: totalSize,
      jsSize: jsSize,
      mapSize: mapSize,
      totalSizeMB: (totalSize / 1024 / 1024).toFixed(2),
      jsSizeMB: (jsSize / 1024 / 1024).toFixed(2),
      mapSizeMB: (mapSize / 1024 / 1024).toFixed(2)
    };
    
    // Performance warnings
    if (totalSize > 50 * 1024 * 1024) { // 50MB
      results.warnings.push(`Large build size: ${results.metrics.totalSizeMB}MB`);
    }
    
    if (mapSize > 10 * 1024 * 1024) { // 10MB
      results.warnings.push(`Large source maps: ${results.metrics.mapSizeMB}MB`);
    }
    
    results.passed.push('Performance analysis');
    return true;
  } catch (error) {
    results.warnings.push(`Performance analysis failed: ${error.message}`);
    return true; // Non-critical
  }
}

// Security validation
function validateSecurity() {
  log.info('Validating build security...');
  
  try {
    // Check for sensitive data in build
    const sensitivePatterns = [
      'password',
      'secret',
      'token',
      'api_key',
      'private_key'
    ];
    
    let foundSensitive = false;
    
    for (const pattern of sensitivePatterns) {
      try {
        const matches = execSync(`grep -r -i "${pattern}" dist/ || true`, { encoding: 'utf8' }).trim();
        if (matches && !matches.includes('No such file')) {
          results.warnings.push(`Potential sensitive data found: ${pattern}`);
          foundSensitive = true;
        }
      } catch (error) {
        // Ignore grep errors
      }
    }
    
    if (!foundSensitive) {
      results.passed.push('Security validation');
    }
    
    return true;
  } catch (error) {
    results.warnings.push(`Security validation failed: ${error.message}`);
    return true; // Non-critical
  }
}

// Generate validation report
function generateReport() {
  if (!config.generateReport) {
    return;
  }
  
  const report = {
    timestamp: new Date().toISOString(),
    validation: {
      passed: results.passed,
      failed: results.failed,
      warnings: results.warnings
    },
    metrics: results.metrics,
    summary: {
      totalChecks: results.passed.length + results.failed.length + results.warnings.length,
      passedChecks: results.passed.length,
      failedChecks: results.failed.length,
      warningChecks: results.warnings.length,
      success: results.failed.length === 0
    }
  };
  
  const reportFile = `build-validation-${Date.now()}.json`;
  fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));
  log.info(`Validation report saved: ${reportFile}`);
}

// Main validation function
async function runValidation() {
  console.log(`${colors.blue}🔍 Build Validator - Discord Bot EnergeX${colors.reset}`);
  console.log(`Strict mode: ${config.strict ? 'ON' : 'OFF'}`);
  console.log('==========================================\n');
  
  // Check if build exists
  if (!fs.existsSync('dist')) {
    log.error('Build directory "dist" not found - run build first');
    process.exit(1);
  }
  
  // Run validations
  const validations = [
    validateBuildArtifacts,
    validateSyntax,
    validateModuleResolution,
    analyzePerformance,
    validateSecurity
  ];
  
  for (const validation of validations) {
    try {
      await validation();
    } catch (error) {
      results.failed.push(`Validation error: ${error.message}`);
    }
  }
  
  // Generate report
  generateReport();
  
  // Show summary
  console.log('\n==========================================');
  console.log(`${colors.blue}📊 Validation Summary${colors.reset}`);
  console.log(`Passed: ${colors.green}${results.passed.length}${colors.reset}`);
  console.log(`Failed: ${colors.red}${results.failed.length}${colors.reset}`);
  console.log(`Warnings: ${colors.yellow}${results.warnings.length}${colors.reset}`);
  
  if (results.metrics.totalSizeMB) {
    console.log(`Build size: ${colors.cyan}${results.metrics.totalSizeMB}MB${colors.reset}`);
  }
  
  // Show detailed results
  if (results.failed.length > 0) {
    console.log(`\n${colors.red}❌ Failed validations:${colors.reset}`);
    results.failed.forEach(failure => console.log(`  • ${failure}`));
  }
  
  if (results.warnings.length > 0) {
    console.log(`\n${colors.yellow}⚠️  Warnings:${colors.reset}`);
    results.warnings.forEach(warning => console.log(`  • ${warning}`));
  }
  
  // Exit with appropriate code
  const hasFailures = results.failed.length > 0;
  const hasWarningsInStrict = config.strict && results.warnings.length > 0;
  
  if (hasFailures || hasWarningsInStrict) {
    console.log(`\n${colors.red}❌ Build validation failed${colors.reset}`);
    process.exit(1);
  } else {
    console.log(`\n${colors.green}✅ Build validation passed${colors.reset}`);
    process.exit(0);
  }
}

// Change to project root
process.chdir(path.join(__dirname, '..'));

// Run validation
runValidation().catch(error => {
  log.error(`Build validation failed: ${error.message}`);
  process.exit(1);
});
