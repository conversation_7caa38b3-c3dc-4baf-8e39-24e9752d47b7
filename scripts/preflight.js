#!/usr/bin/env node

/**
 * Discord Bot EnergeX - Preflight Check Script
 * Comprehensive pre-deployment validation in Node.js
 * 
 * Usage: node scripts/preflight.js [options]
 * Options:
 *   --env=dev|prod     Environment mode (default: dev)
 *   --skip-build       Skip build validation
 *   --skip-db          Skip database checks
 *   --verbose          Enable verbose output
 *   --json             Output results in JSON format
 */

const fs = require('fs');
const path = require('path');
const { execSync, spawn } = require('child_process');
const { Client } = require('pg');

// Configuration
const config = {
  environment: 'dev',
  skipBuild: false,
  skipDb: false,
  verbose: false,
  jsonOutput: false
};

// Parse command line arguments
process.argv.slice(2).forEach(arg => {
  if (arg.startsWith('--env=')) {
    config.environment = arg.split('=')[1];
  } else if (arg === '--skip-build') {
    config.skipBuild = true;
  } else if (arg === '--skip-db') {
    config.skipDb = true;
  } else if (arg === '--verbose') {
    config.verbose = true;
  } else if (arg === '--json') {
    config.jsonOutput = true;
  } else if (arg === '--help') {
    console.log(`
Usage: node scripts/preflight.js [options]

Options:
  --env=dev|prod     Environment mode (default: dev)
  --skip-build       Skip build validation
  --skip-db          Skip database checks
  --verbose          Enable verbose output
  --json             Output results in JSON format
  --help             Show this help message
    `);
    process.exit(0);
  }
});

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  bold: '\x1b[1m'
};

// Logging functions
const log = {
  info: (msg) => !config.jsonOutput && console.log(`${colors.blue}ℹ️  ${msg}${colors.reset}`),
  success: (msg) => !config.jsonOutput && console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  warning: (msg) => !config.jsonOutput && console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`),
  error: (msg) => !config.jsonOutput && console.error(`${colors.red}❌ ${msg}${colors.reset}`),
  verbose: (msg) => config.verbose && !config.jsonOutput && console.log(`${colors.cyan}🔍 ${msg}${colors.reset}`)
};

// Results tracking
const results = {
  checks: [],
  summary: {
    total: 0,
    passed: 0,
    failed: 0,
    warnings: 0
  }
};

// Check runner
async function runCheck(name, checkFunction, critical = true) {
  results.summary.total++;
  
  log.info(`Running check: ${name}`);
  
  try {
    const result = await checkFunction();
    
    if (result.success) {
      log.success(`${name} passed`);
      results.summary.passed++;
      results.checks.push({ name, status: 'passed', critical, ...result });
      return true;
    } else {
      if (critical) {
        log.error(`${name} failed (CRITICAL): ${result.message || 'Unknown error'}`);
        results.summary.failed++;
        results.checks.push({ name, status: 'failed', critical, ...result });
        return false;
      } else {
        log.warning(`${name} failed (NON-CRITICAL): ${result.message || 'Unknown error'}`);
        results.summary.warnings++;
        results.checks.push({ name, status: 'warning', critical, ...result });
        return true;
      }
    }
  } catch (error) {
    const message = error.message || 'Unknown error';
    
    if (critical) {
      log.error(`${name} failed (CRITICAL): ${message}`);
      results.summary.failed++;
      results.checks.push({ name, status: 'failed', critical, message, error: error.stack });
      return false;
    } else {
      log.warning(`${name} failed (NON-CRITICAL): ${message}`);
      results.summary.warnings++;
      results.checks.push({ name, status: 'warning', critical, message, error: error.stack });
      return true;
    }
  }
}

// Check functions
async function checkSystemRequirements() {
  const nodeVersion = process.version;
  const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
  
  if (majorVersion < 18) {
    return {
      success: false,
      message: `Node.js version must be >= 18.17.0, found: ${nodeVersion}`,
      nodeVersion
    };
  }
  
  // Check pnpm
  try {
    const pnpmVersion = execSync('pnpm --version', { encoding: 'utf8' }).trim();
    log.verbose(`Node.js: ${nodeVersion}, pnpm: v${pnpmVersion}`);
    
    return {
      success: true,
      nodeVersion,
      pnpmVersion
    };
  } catch (error) {
    return {
      success: false,
      message: 'pnpm is not installed or not available',
      nodeVersion
    };
  }
}

async function checkFileStructure() {
  const requiredFiles = [
    'package.json',
    'tsconfig.json',
    'nest-cli.json',
    'src/main.ts',
    'src/app.module.ts',
    'drizzle.config.ts'
  ];
  
  const requiredDirs = [
    'src',
    'src/core',
    'src/discord',
    'src/api',
    'src/features',
    'migrations',
    'scripts'
  ];
  
  const missing = [];
  
  // Check files
  for (const file of requiredFiles) {
    if (!fs.existsSync(file)) {
      missing.push(`file: ${file}`);
    }
  }
  
  // Check directories
  for (const dir of requiredDirs) {
    if (!fs.existsSync(dir) || !fs.statSync(dir).isDirectory()) {
      missing.push(`directory: ${dir}`);
    }
  }
  
  if (missing.length > 0) {
    return {
      success: false,
      message: `Missing required items: ${missing.join(', ')}`,
      missing
    };
  }
  
  return { success: true, message: 'All required files and directories found' };
}

async function checkDependencies() {
  if (!fs.existsSync('node_modules')) {
    return {
      success: false,
      message: 'node_modules directory not found - run "pnpm install"'
    };
  }
  
  const criticalDeps = [
    '@nestjs/core',
    '@nestjs/common',
    'discord.js',
    'necord',
    'drizzle-orm',
    'pg',
    'class-validator',
    'class-transformer'
  ];
  
  const missing = [];
  
  for (const dep of criticalDeps) {
    if (!fs.existsSync(path.join('node_modules', dep))) {
      missing.push(dep);
    }
  }
  
  if (missing.length > 0) {
    return {
      success: false,
      message: `Missing critical dependencies: ${missing.join(', ')}`,
      missing
    };
  }
  
  return { success: true, message: 'All critical dependencies found' };
}

async function checkEnvironmentVariables() {
  const envConfig = {
    dev: {
      critical: ['DATABASE_URL', 'BOT_CLIENT_ID', 'BOT_CLIENT_SECRET', 'DISCORD_CLIENT_ID', 'DISCORD_CLIENT_SECRET'],
      optional: ['DISCORD_TOKEN', 'ANTHROPIC_API_KEY', 'NODE_ENV', 'PORT', 'GUILD_ID']
    },
    prod: {
      critical: ['DATABASE_URL', 'NODE_ENV', 'PORT', 'BOT_CLIENT_ID', 'BOT_CLIENT_SECRET', 'DISCORD_CLIENT_ID', 'DISCORD_CLIENT_SECRET', 'USER_ENCRYPTION_KEY', 'SESSION_ENCRYPTION_KEY', 'CSRF_ENCRYPTION_KEY', 'WEB_URL'],
      optional: ['DISCORD_TOKEN', 'ANTHROPIC_API_KEY', 'APP_URL', 'NEXT_PUBLIC_API_ENDPOINT', 'INTERNAL_API_ENDPOINT']
    }
  };
  
  const env = config.environment === 'production' ? 'prod' : 'dev';
  const { critical, optional } = envConfig[env];
  
  const missingCritical = critical.filter(varName => !process.env[varName]);
  const missingOptional = optional.filter(varName => !process.env[varName]);
  
  if (missingCritical.length > 0) {
    return {
      success: false,
      message: `Missing critical environment variables: ${missingCritical.join(', ')}`,
      missingCritical,
      missingOptional
    };
  }
  
  return {
    success: true,
    message: 'All critical environment variables are set',
    missingOptional: missingOptional.length > 0 ? missingOptional : undefined
  };
}

async function checkDiscordConfiguration() {
  // Check OAuth configuration (critical)
  if (!process.env.BOT_CLIENT_ID || !process.env.BOT_CLIENT_SECRET) {
    return {
      success: false,
      message: 'Discord OAuth configuration incomplete'
    };
  }
  
  // Check bot token (optional)
  if (!process.env.DISCORD_TOKEN) {
    return {
      success: true,
      message: 'Discord OAuth configured, bot token not set (OAuth-only mode)',
      mode: 'oauth-only'
    };
  }
  
  // Basic token format validation
  const tokenPattern = /^[A-Za-z0-9._-]+$/;
  if (!tokenPattern.test(process.env.DISCORD_TOKEN)) {
    return {
      success: false,
      message: 'DISCORD_TOKEN appears to have invalid format'
    };
  }
  
  return {
    success: true,
    message: 'Discord configuration complete (bot mode enabled)',
    mode: 'bot'
  };
}

async function checkDatabaseConnectivity() {
  if (config.skipDb) {
    return { success: true, message: 'Database check skipped' };
  }
  
  if (!process.env.DATABASE_URL) {
    return {
      success: false,
      message: 'DATABASE_URL not set'
    };
  }
  
  try {
    const client = new Client({ connectionString: process.env.DATABASE_URL });
    await client.connect();
    await client.query('SELECT 1 as test');
    await client.end();
    
    return {
      success: true,
      message: 'Database connectivity test passed'
    };
  } catch (error) {
    return {
      success: false,
      message: `Database connectivity failed: ${error.message}`
    };
  }
}

async function checkBuild() {
  if (config.skipBuild) {
    return { success: true, message: 'Build check skipped' };
  }
  
  // Check if dist directory exists
  if (!fs.existsSync('dist')) {
    return {
      success: false,
      message: 'Build directory "dist" not found - run "pnpm run build"'
    };
  }
  
  // Check main entry point
  if (!fs.existsSync('dist/src/main.js')) {
    return {
      success: false,
      message: 'Main application file "dist/src/main.js" not found'
    };
  }
  
  // Check if build is recent
  const srcStat = fs.statSync('src');
  const distStat = fs.statSync('dist');
  
  if (srcStat.mtime > distStat.mtime) {
    return {
      success: true,
      message: 'Build exists but source files are newer - consider rebuilding',
      warning: 'outdated'
    };
  }
  
  return { success: true, message: 'Build validation passed' };
}

async function checkTypeScript() {
  if (config.skipBuild) {
    return { success: true, message: 'TypeScript check skipped' };
  }
  
  try {
    execSync('npx tsc --noEmit --skipLibCheck', { stdio: 'pipe' });
    return { success: true, message: 'TypeScript compilation check passed' };
  } catch (error) {
    return {
      success: false,
      message: 'TypeScript compilation errors found - run "npx tsc --noEmit" for details'
    };
  }
}

// Main preflight function
async function runPreflight() {
  if (!config.jsonOutput) {
    console.log(`${colors.bold}${colors.blue}🚀 Discord Bot EnergeX - Preflight Check${colors.reset}`);
    console.log(`${colors.blue}Environment: ${config.environment}${colors.reset}`);
    console.log(`${colors.blue}Skip Build: ${config.skipBuild}${colors.reset}`);
    console.log(`${colors.blue}Skip DB: ${config.skipDb}${colors.reset}`);
    console.log('==================================\n');
  }
  
  // Run all checks
  const checks = [
    ['System Requirements', checkSystemRequirements, true],
    ['File Structure', checkFileStructure, true],
    ['Dependencies', checkDependencies, true],
    ['Environment Variables', checkEnvironmentVariables, true],
    ['Discord Configuration', checkDiscordConfiguration, true],
  ];
  
  if (!config.skipBuild) {
    checks.push(['TypeScript Compilation', checkTypeScript, true]);
    checks.push(['Build Validation', checkBuild, true]);
  }
  
  if (!config.skipDb) {
    checks.push(['Database Connectivity', checkDatabaseConnectivity, true]);
  }
  
  // Execute all checks
  let allPassed = true;
  for (const [name, checkFn, critical] of checks) {
    const passed = await runCheck(name, checkFn, critical);
    if (!passed && critical) {
      allPassed = false;
    }
  }
  
  // Output results
  if (config.jsonOutput) {
    console.log(JSON.stringify(results, null, 2));
  } else {
    showSummary();
  }
  
  process.exit(allPassed ? 0 : 1);
}

function showSummary() {
  console.log(`\n${colors.bold}${colors.blue}📊 PREFLIGHT SUMMARY${colors.reset}`);
  console.log('==================================');
  console.log(`Environment: ${colors.cyan}${config.environment}${colors.reset}`);
  console.log(`Total checks: ${colors.blue}${results.summary.total}${colors.reset}`);
  console.log(`Passed: ${colors.green}${results.summary.passed}${colors.reset}`);
  console.log(`Failed: ${colors.red}${results.summary.failed}${colors.reset}`);
  console.log(`Warnings: ${colors.yellow}${results.summary.warnings}${colors.reset}\n`);
  
  if (results.summary.failed === 0) {
    console.log(`${colors.green}🎉 PREFLIGHT PASSED${colors.reset}`);
    console.log(`${colors.green}Your Discord bot is ready for deployment/startup!${colors.reset}`);
    
    if (results.summary.warnings > 0) {
      console.log(`${colors.yellow}Note: ${results.summary.warnings} warnings found - review above for optimization opportunities${colors.reset}`);
    }
    
    console.log(`\n${colors.blue}Next steps:${colors.reset}`);
    if (config.environment === 'prod') {
      console.log('  • Deploy to production: docker build -t discord-bot .');
      console.log('  • Or start production: pnpm run start:prod');
    } else {
      console.log('  • Start development: pnpm run dev');
      console.log('  • Or build and start: pnpm run build && pnpm run start');
    }
  } else {
    console.log(`${colors.red}❌ PREFLIGHT FAILED${colors.reset}`);
    console.log(`${colors.red}${results.summary.failed} critical issues must be resolved before deployment${colors.reset}\n`);
    console.log(`${colors.blue}Recommended actions:${colors.reset}`);
    console.log('  • Review error messages above');
    console.log('  • Fix critical issues');
    console.log('  • Re-run preflight: node scripts/preflight.js');
  }
}

// Handle script interruption
process.on('SIGINT', () => {
  log.error('Preflight check interrupted');
  process.exit(1);
});

process.on('SIGTERM', () => {
  log.error('Preflight check terminated');
  process.exit(1);
});

// Run preflight
runPreflight().catch(error => {
  log.error(`Preflight check failed: ${error.message}`);
  process.exit(1);
});
