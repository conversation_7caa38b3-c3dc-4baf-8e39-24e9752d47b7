#!/bin/bash

# Docker Preflight Check for Discord Bot EnergeX
# Lightweight validation for Docker container builds
# Usage: ./scripts/docker-preflight.sh

set -euo pipefail

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging
log() { echo -e "${BLUE}[DOCKER-PREFLIGHT]${NC} $1"; }
success() { echo -e "${GREEN}✅${NC} $1"; }
warning() { echo -e "${YELLOW}⚠️${NC} $1"; }
error() { echo -e "${RED}❌${NC} $1" >&2; }

# Check counters
CHECKS_PASSED=0
CHECKS_FAILED=0

# Run a check
check() {
    local name="$1"
    local command="$2"
    
    log "Checking: $name"
    
    if eval "$command" >/dev/null 2>&1; then
        success "$name"
        ((CHECKS_PASSED++))
        return 0
    else
        error "$name"
        ((CHECKS_FAILED++))
        return 1
    fi
}

# Docker-specific checks
main() {
    log "🐳 Docker Preflight Check Starting"
    echo ""
    
    # Basic file structure
    check "package.json exists" "test -f package.json"
    check "Source directory exists" "test -d src"
    check "Main entry point exists" "test -f src/main.ts"
    check "NestJS config exists" "test -f nest-cli.json"
    check "TypeScript config exists" "test -f tsconfig.json"
    
    # Dependencies
    check "node_modules exists" "test -d node_modules"
    check "pnpm lockfile exists" "test -f pnpm-lock.yaml"
    
    # Build artifacts (if they should exist)
    if [[ -d "dist" ]]; then
        check "Build output valid" "test -f dist/src/main.js"
    else
        warning "Build directory not found (will be created during Docker build)"
    fi
    
    # Docker-specific files
    check "Dockerfile exists" "test -f Dockerfile"
    
    # Production script
    check "Production script exists" "test -f scripts/production-start.sh"
    check "Production script executable" "test -x scripts/production-start.sh"
    
    # Environment template
    check "Environment example exists" "test -f .env.example"
    
    echo ""
    log "📊 Docker Preflight Summary"
    echo "Passed: $CHECKS_PASSED"
    echo "Failed: $CHECKS_FAILED"
    
    if [[ $CHECKS_FAILED -eq 0 ]]; then
        success "Docker preflight passed - ready for container build"
        echo ""
        echo "Next steps:"
        echo "  docker build -t discord-bot-energex ."
        echo "  docker run --env-file .env -p 8080:8080 discord-bot-energex"
        return 0
    else
        error "Docker preflight failed - fix issues before building"
        echo ""
        echo "Common fixes:"
        echo "  pnpm install    # Install dependencies"
        echo "  pnpm run build  # Build application"
        echo "  chmod +x scripts/production-start.sh  # Fix permissions"
        return 1
    fi
}

# Change to project root
cd "$(dirname "$0")/.."

# Run main function
main "$@"
