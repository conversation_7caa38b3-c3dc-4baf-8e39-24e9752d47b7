#!/usr/bin/env node

/**
 * Quick Preflight Check for Discord Bot EnergeX
 * Fast validation of essential requirements
 * 
 * Usage: node scripts/quick-preflight.js
 */

const fs = require('fs');
const path = require('path');

// Colors
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

// Quick logging
const log = {
  info: (msg) => console.log(`${colors.blue}ℹ️  ${msg}${colors.reset}`),
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`),
  error: (msg) => console.error(`${colors.red}❌ ${msg}${colors.reset}`)
};

// Quick checks
const checks = [
  {
    name: 'Node.js Version',
    check: () => {
      const version = process.version;
      const major = parseInt(version.slice(1).split('.')[0]);
      return {
        success: major >= 18,
        message: major >= 18 ? `Node.js ${version} ✓` : `Node.js ${version} (requires >= 18.17.0)`
      };
    }
  },
  
  {
    name: 'Package Manager',
    check: () => {
      try {
        require('child_process').execSync('pnpm --version', { stdio: 'pipe' });
        return { success: true, message: 'pnpm available ✓' };
      } catch {
        return { success: false, message: 'pnpm not found - install with: npm install -g pnpm' };
      }
    }
  },
  
  {
    name: 'Dependencies',
    check: () => {
      const hasNodeModules = fs.existsSync('node_modules');
      const hasPackageJson = fs.existsSync('package.json');
      
      if (!hasPackageJson) {
        return { success: false, message: 'package.json not found' };
      }
      
      if (!hasNodeModules) {
        return { success: false, message: 'node_modules not found - run: pnpm install' };
      }
      
      return { success: true, message: 'Dependencies installed ✓' };
    }
  },
  
  {
    name: 'Environment',
    check: () => {
      const hasEnv = fs.existsSync('.env.local') || fs.existsSync('.env');
      const hasExample = fs.existsSync('.env.example');
      
      if (!hasEnv && !hasExample) {
        return { success: false, message: 'No environment configuration found' };
      }
      
      if (!hasEnv) {
        return { success: false, message: 'Environment file missing - copy .env.example to .env.local' };
      }
      
      // Check critical env vars
      const critical = ['DATABASE_URL', 'BOT_CLIENT_ID', 'BOT_CLIENT_SECRET'];
      const missing = critical.filter(v => !process.env[v]);
      
      if (missing.length > 0) {
        return { success: false, message: `Missing: ${missing.join(', ')}` };
      }
      
      return { success: true, message: 'Environment configured ✓' };
    }
  },
  
  {
    name: 'Source Code',
    check: () => {
      const requiredFiles = ['src/main.ts', 'src/app.module.ts'];
      const missing = requiredFiles.filter(f => !fs.existsSync(f));
      
      if (missing.length > 0) {
        return { success: false, message: `Missing files: ${missing.join(', ')}` };
      }
      
      return { success: true, message: 'Source code structure ✓' };
    }
  },
  
  {
    name: 'Configuration',
    check: () => {
      const configFiles = ['tsconfig.json', 'nest-cli.json', 'drizzle.config.ts'];
      const missing = configFiles.filter(f => !fs.existsSync(f));
      
      if (missing.length > 0) {
        return { success: false, message: `Missing config: ${missing.join(', ')}` };
      }
      
      return { success: true, message: 'Configuration files ✓' };
    }
  }
];

// Run quick preflight
async function runQuickPreflight() {
  console.log(`${colors.blue}🚀 Discord Bot EnergeX - Quick Preflight Check${colors.reset}`);
  console.log('================================================\n');
  
  let passed = 0;
  let failed = 0;
  
  for (const { name, check } of checks) {
    try {
      const result = check();
      
      if (result.success) {
        log.success(`${name}: ${result.message}`);
        passed++;
      } else {
        log.error(`${name}: ${result.message}`);
        failed++;
      }
    } catch (error) {
      log.error(`${name}: Check failed - ${error.message}`);
      failed++;
    }
  }
  
  console.log('\n================================================');
  console.log(`${colors.blue}📊 Quick Check Results${colors.reset}`);
  console.log(`Passed: ${colors.green}${passed}${colors.reset}`);
  console.log(`Failed: ${colors.red}${failed}${colors.reset}`);
  
  if (failed === 0) {
    console.log(`\n${colors.green}🎉 Quick preflight passed!${colors.reset}`);
    console.log(`${colors.cyan}Run full preflight for comprehensive validation:${colors.reset}`);
    console.log(`  pnpm run preflight:dev   # Development`);
    console.log(`  pnpm run preflight:prod  # Production`);
    console.log(`\n${colors.cyan}Start the application:${colors.reset}`);
    console.log(`  pnpm run dev             # Development mode`);
    console.log(`  pnpm run start:prod      # Production mode`);
  } else {
    console.log(`\n${colors.red}❌ Quick preflight failed${colors.reset}`);
    console.log(`${colors.yellow}Fix the issues above and try again${colors.reset}`);
    console.log(`\n${colors.cyan}Common fixes:${colors.reset}`);
    console.log(`  pnpm install             # Install dependencies`);
    console.log(`  cp .env.example .env.local  # Create environment file`);
    console.log(`  pnpm run build           # Build the application`);
  }
  
  process.exit(failed === 0 ? 0 : 1);
}

// Handle interruption
process.on('SIGINT', () => {
  log.error('Quick preflight interrupted');
  process.exit(1);
});

// Change to project root
process.chdir(path.join(__dirname, '..'));

// Run the check
runQuickPreflight().catch(error => {
  log.error(`Quick preflight failed: ${error.message}`);
  process.exit(1);
});
