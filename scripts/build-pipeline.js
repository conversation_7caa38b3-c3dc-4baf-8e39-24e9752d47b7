#!/usr/bin/env node

/**
 * Build Pipeline for Discord Bot EnergeX
 * Complete build workflow with validation, optimization, and testing
 * 
 * Usage: node scripts/build-pipeline.js [--target=dev|prod|docker] [--test] [--deploy]
 */

const fs = require('fs');
const path = require('path');
const { execSync, spawn } = require('child_process');

// Configuration
const config = {
  target: 'dev',
  runTests: false,
  deploy: false,
  verbose: false,
  skipCache: false
};

// Parse arguments
process.argv.slice(2).forEach(arg => {
  if (arg.startsWith('--target=')) {
    config.target = arg.split('=')[1];
  } else if (arg === '--test') {
    config.runTests = true;
  } else if (arg === '--deploy') {
    config.deploy = true;
  } else if (arg === '--verbose') {
    config.verbose = true;
  } else if (arg === '--skip-cache') {
    config.skipCache = true;
  } else if (arg === '--help') {
    console.log(`
Build Pipeline for Discord Bot EnergeX

Usage: node scripts/build-pipeline.js [options]

Options:
  --target=dev|prod|docker  Build target (default: dev)
  --test                   Run tests after build
  --deploy                 Deploy after successful build
  --verbose                Enable verbose output
  --skip-cache             Skip build cache
  --help                   Show this help message

Targets:
  dev     - Development build with source maps
  prod    - Production build with optimizations
  docker  - Docker-optimized build
    `);
    process.exit(0);
  }
});

// Colors
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  bold: '\x1b[1m'
};

// Logging
const log = {
  step: (msg) => console.log(`\n${colors.bold}${colors.blue}🔄 ${msg}${colors.reset}`),
  info: (msg) => console.log(`${colors.blue}ℹ️  ${msg}${colors.reset}`),
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`),
  error: (msg) => console.error(`${colors.red}❌ ${msg}${colors.reset}`),
  verbose: (msg) => config.verbose && console.log(`${colors.cyan}🔍 ${msg}${colors.reset}`)
};

// Pipeline metrics
const metrics = {
  startTime: Date.now(),
  steps: {},
  totalDuration: 0,
  buildSize: 0,
  testResults: null
};

// Execute command with timing
function executeStep(stepName, command, options = {}) {
  const stepStart = Date.now();
  log.info(`Executing: ${stepName}`);
  log.verbose(`Command: ${command}`);
  
  try {
    const result = execSync(command, {
      encoding: 'utf8',
      stdio: config.verbose ? 'inherit' : 'pipe',
      ...options
    });
    
    const stepDuration = Date.now() - stepStart;
    metrics.steps[stepName] = {
      duration: stepDuration,
      success: true
    };
    
    log.success(`${stepName} completed in ${(stepDuration / 1000).toFixed(2)}s`);
    return result;
  } catch (error) {
    const stepDuration = Date.now() - stepStart;
    metrics.steps[stepName] = {
      duration: stepDuration,
      success: false,
      error: error.message
    };
    
    log.error(`${stepName} failed after ${(stepDuration / 1000).toFixed(2)}s`);
    throw error;
  }
}

// Pre-build steps
async function preBuildSteps() {
  log.step('Pre-Build Validation');
  
  // Run preflight checks
  try {
    executeStep('Preflight Check', 'node scripts/quick-preflight.js');
  } catch (error) {
    log.error('Preflight check failed - fix issues before building');
    throw error;
  }
  
  // Clean build if needed
  if (config.skipCache || config.target === 'prod') {
    executeStep('Clean Build Directory', 'rm -rf dist');
  }
  
  // Install/update dependencies if needed
  if (!fs.existsSync('node_modules') || config.skipCache) {
    executeStep('Install Dependencies', 'pnpm install --frozen-lockfile');
  }
}

// Build steps based on target
async function buildSteps() {
  log.step(`Building for ${config.target.toUpperCase()} target`);
  
  let buildCommand = '';
  
  switch (config.target) {
    case 'dev':
      buildCommand = 'nest build';
      break;
    case 'prod':
      buildCommand = 'nest build --webpack';
      break;
    case 'docker':
      buildCommand = 'nest build --webpack';
      // Additional Docker optimizations
      process.env.NODE_ENV = 'production';
      break;
    default:
      throw new Error(`Unknown build target: ${config.target}`);
  }
  
  executeStep('TypeScript Compilation', buildCommand);
  
  // Validate build output
  executeStep('Build Validation', 'node scripts/build-validator.js');
}

// Post-build steps
async function postBuildSteps() {
  log.step('Post-Build Processing');
  
  // Calculate build metrics
  if (fs.existsSync('dist')) {
    const sizeOutput = execSync('du -sb dist', { encoding: 'utf8' });
    metrics.buildSize = parseInt(sizeOutput.split('\t')[0]);
    log.info(`Build size: ${(metrics.buildSize / 1024 / 1024).toFixed(2)}MB`);
  }
  
  // Target-specific optimizations
  if (config.target === 'prod' || config.target === 'docker') {
    // Remove source maps in production
    try {
      executeStep('Remove Source Maps', 'find dist -name "*.map" -delete');
    } catch (error) {
      log.warning('Could not remove source maps');
    }
    
    // Optimize file permissions for Docker
    if (config.target === 'docker') {
      executeStep('Set File Permissions', 'chmod -R 755 dist');
    }
  }
}

// Test execution
async function runTests() {
  if (!config.runTests) {
    return;
  }
  
  log.step('Running Tests');
  
  try {
    // Run unit tests
    const testOutput = executeStep('Unit Tests', 'pnpm run test --passWithNoTests');
    
    // Parse test results if possible
    if (testOutput.includes('Tests:') || testOutput.includes('Test Suites:')) {
      metrics.testResults = {
        executed: true,
        output: testOutput
      };
    }
    
    // Run build-specific tests
    if (fs.existsSync('test/build.test.js')) {
      executeStep('Build Tests', 'pnpm run test test/build.test.js');
    }
    
  } catch (error) {
    log.error('Tests failed - build may have issues');
    throw error;
  }
}

// Deployment preparation
async function deploymentPrep() {
  if (!config.deploy) {
    return;
  }
  
  log.step('Deployment Preparation');
  
  // Create deployment package
  if (config.target === 'docker') {
    executeStep('Docker Build Test', './scripts/docker-preflight.sh');
    log.info('Docker build validation passed');
  }
  
  // Generate deployment manifest
  const manifest = {
    timestamp: new Date().toISOString(),
    target: config.target,
    buildSize: metrics.buildSize,
    nodeVersion: process.version,
    commit: execSync('git rev-parse HEAD 2>/dev/null || echo "unknown"', { encoding: 'utf8' }).trim(),
    branch: execSync('git branch --show-current 2>/dev/null || echo "unknown"', { encoding: 'utf8' }).trim()
  };
  
  fs.writeFileSync('dist/deployment-manifest.json', JSON.stringify(manifest, null, 2));
  log.success('Deployment manifest created');
}

// Generate comprehensive report
function generatePipelineReport() {
  metrics.totalDuration = Date.now() - metrics.startTime;
  
  const report = {
    pipeline: {
      timestamp: new Date().toISOString(),
      target: config.target,
      duration: metrics.totalDuration,
      success: true
    },
    steps: metrics.steps,
    build: {
      size: metrics.buildSize,
      sizeFormatted: `${(metrics.buildSize / 1024 / 1024).toFixed(2)}MB`
    },
    validation: {
      passed: results.passed,
      failed: results.failed,
      warnings: results.warnings
    },
    tests: metrics.testResults,
    environment: {
      nodeVersion: process.version,
      platform: process.platform,
      target: config.target
    }
  };
  
  const reportFile = `pipeline-report-${config.target}-${Date.now()}.json`;
  fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));
  log.info(`Pipeline report saved: ${reportFile}`);
  
  return report;
}

// Main pipeline execution
async function runBuildPipeline() {
  try {
    console.log(`${colors.bold}${colors.blue}🏗️  Build Pipeline - Discord Bot EnergeX${colors.reset}`);
    console.log(`${colors.blue}Target: ${config.target} | Tests: ${config.runTests} | Deploy: ${config.deploy}${colors.reset}`);
    console.log('================================================================\n');
    
    // Execute pipeline steps
    await preBuildSteps();
    await buildSteps();
    await postBuildSteps();
    
    if (config.runTests) {
      await runTests();
    }
    
    if (config.deploy) {
      await deploymentPrep();
    }
    
    // Generate final report
    const report = generatePipelineReport();
    
    // Success summary
    console.log(`\n${colors.bold}${colors.green}🎉 BUILD PIPELINE COMPLETED SUCCESSFULLY${colors.reset}`);
    console.log('================================================================');
    console.log(`Target: ${colors.cyan}${config.target}${colors.reset}`);
    console.log(`Duration: ${colors.cyan}${(metrics.totalDuration / 1000).toFixed(2)}s${colors.reset}`);
    console.log(`Build size: ${colors.cyan}${(metrics.buildSize / 1024 / 1024).toFixed(2)}MB${colors.reset}`);
    
    if (config.target === 'prod') {
      console.log(`\n${colors.green}🚀 Production build ready for deployment${colors.reset}`);
      console.log('Next steps:');
      console.log('  • Test production build: pnpm run start:prod');
      console.log('  • Deploy to platform: follow deployment guide');
      console.log('  • Monitor health: curl http://localhost:8080/api/health');
    } else if (config.target === 'docker') {
      console.log(`\n${colors.green}🐳 Docker build ready${colors.reset}`);
      console.log('Next steps:');
      console.log('  • Build container: docker build -t discord-bot-energex .');
      console.log('  • Run container: docker run --env-file .env -p 8080:8080 discord-bot-energex');
    } else {
      console.log(`\n${colors.green}🛠️  Development build ready${colors.reset}`);
      console.log('Next steps:');
      console.log('  • Start dev server: pnpm run dev');
      console.log('  • Run tests: pnpm run test');
    }
    
    process.exit(0);
    
  } catch (error) {
    log.error(`Build pipeline failed: ${error.message}`);
    
    // Generate failure report
    const failureReport = {
      timestamp: new Date().toISOString(),
      target: config.target,
      error: error.message,
      duration: Date.now() - metrics.startTime,
      steps: metrics.steps,
      failed: results.failed
    };
    
    fs.writeFileSync(`pipeline-failure-${Date.now()}.json`, JSON.stringify(failureReport, null, 2));
    
    process.exit(1);
  }
}

// Handle interruption
process.on('SIGINT', () => {
  log.error('Build pipeline interrupted');
  process.exit(1);
});

// Change to project root
process.chdir(path.join(__dirname, '..'));

// Run the pipeline
runBuildPipeline();
