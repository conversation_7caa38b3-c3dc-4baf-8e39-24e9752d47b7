#!/usr/bin/env node

/**
 * Build Optimizer for Discord Bot EnergeX
 * Analyzes and optimizes build output for better performance
 * 
 * Usage: node scripts/build-optimizer.js [--analyze] [--optimize] [--report]
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const config = {
  analyze: false,
  optimize: false,
  generateReport: false,
  verbose: false
};

// Parse arguments
process.argv.slice(2).forEach(arg => {
  if (arg === '--analyze') config.analyze = true;
  else if (arg === '--optimize') config.optimize = true;
  else if (arg === '--report') config.generateReport = true;
  else if (arg === '--verbose') config.verbose = true;
  else if (arg === '--help') {
    console.log(`
Build Optimizer for Discord Bot EnergeX

Usage: node scripts/build-optimizer.js [options]

Options:
  --analyze     Analyze build output for optimization opportunities
  --optimize    Apply optimizations to build output
  --report      Generate detailed optimization report
  --verbose     Enable verbose output
  --help        Show this help message

Examples:
  node scripts/build-optimizer.js --analyze --report
  node scripts/build-optimizer.js --optimize
    `);
    process.exit(0);
  }
});

// Colors
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  bold: '\x1b[1m'
};

// Logging
const log = {
  info: (msg) => console.log(`${colors.blue}ℹ️  ${msg}${colors.reset}`),
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`),
  error: (msg) => console.error(`${colors.red}❌ ${msg}${colors.reset}`),
  verbose: (msg) => config.verbose && console.log(`${colors.cyan}🔍 ${msg}${colors.reset}`)
};

// Analysis results
const analysis = {
  files: {},
  dependencies: {},
  optimization: {},
  recommendations: []
};

// Analyze build files
function analyzeFiles() {
  log.info('Analyzing build files...');
  
  if (!fs.existsSync('dist')) {
    throw new Error('Build directory not found - run build first');
  }
  
  // Get all JavaScript files
  const jsFiles = execSync('find dist -name "*.js"', { encoding: 'utf8' })
    .trim()
    .split('\n')
    .filter(file => file.length > 0);
  
  // Analyze each file
  jsFiles.forEach(file => {
    const stats = fs.statSync(file);
    const content = fs.readFileSync(file, 'utf8');
    
    analysis.files[file] = {
      size: stats.size,
      lines: content.split('\n').length,
      hasSourceMap: fs.existsSync(file + '.map'),
      hasComments: content.includes('//') || content.includes('/*'),
      hasConsoleLog: content.includes('console.log'),
      hasDebugCode: content.includes('debugger') || content.includes('console.debug')
    };
  });
  
  // Calculate totals
  const totalSize = Object.values(analysis.files).reduce((sum, file) => sum + file.size, 0);
  const totalFiles = jsFiles.length;
  const sourceMaps = Object.values(analysis.files).filter(file => file.hasSourceMap).length;
  
  analysis.optimization.totalSize = totalSize;
  analysis.optimization.totalFiles = totalFiles;
  analysis.optimization.sourceMaps = sourceMaps;
  
  log.success(`Analyzed ${totalFiles} files (${(totalSize / 1024 / 1024).toFixed(2)}MB)`);
  
  return analysis.files;
}

// Analyze dependencies
function analyzeDependencies() {
  log.info('Analyzing dependencies in build...');
  
  try {
    // Check for large dependencies
    const bundleFiles = execSync('find dist -name "*.js" -size +100k', { encoding: 'utf8' })
      .trim()
      .split('\n')
      .filter(file => file.length > 0);
    
    bundleFiles.forEach(file => {
      const size = fs.statSync(file).size;
      analysis.dependencies[file] = {
        size: size,
        sizeFormatted: `${(size / 1024).toFixed(2)}KB`
      };
      
      if (size > 500 * 1024) { // 500KB
        analysis.recommendations.push(`Large file detected: ${file} (${(size / 1024).toFixed(2)}KB)`);
      }
    });
    
    log.success(`Found ${bundleFiles.length} large files`);
    
  } catch (error) {
    log.warning('Could not analyze dependencies');
  }
  
  return analysis.dependencies;
}

// Generate optimization recommendations
function generateRecommendations() {
  log.info('Generating optimization recommendations...');
  
  const { totalSize, sourceMaps } = analysis.optimization;
  
  // Size-based recommendations
  if (totalSize > 50 * 1024 * 1024) { // 50MB
    analysis.recommendations.push('Build size is large (>50MB) - consider code splitting');
  }
  
  if (totalSize > 100 * 1024 * 1024) { // 100MB
    analysis.recommendations.push('Build size is very large (>100MB) - review dependencies');
  }
  
  // Source map recommendations
  if (sourceMaps > 0 && process.env.NODE_ENV === 'production') {
    analysis.recommendations.push(`Remove ${sourceMaps} source maps from production build`);
  }
  
  // File-specific recommendations
  Object.entries(analysis.files).forEach(([file, info]) => {
    if (info.hasConsoleLog) {
      analysis.recommendations.push(`Remove console.log statements from ${file}`);
    }
    
    if (info.hasDebugCode) {
      analysis.recommendations.push(`Remove debug code from ${file}`);
    }
    
    if (info.size > 1024 * 1024) { // 1MB
      analysis.recommendations.push(`Large file: ${file} (${(info.size / 1024 / 1024).toFixed(2)}MB)`);
    }
  });
  
  log.success(`Generated ${analysis.recommendations.length} recommendations`);
  
  return analysis.recommendations;
}

// Apply optimizations
function applyOptimizations() {
  if (!config.optimize) {
    return;
  }
  
  log.info('Applying build optimizations...');
  
  let optimizationsApplied = 0;
  
  // Remove source maps in production
  if (process.env.NODE_ENV === 'production' || analysis.optimization.sourceMaps > 0) {
    try {
      execSync('find dist -name "*.map" -delete');
      log.success('Removed source maps');
      optimizationsApplied++;
    } catch (error) {
      log.warning('Could not remove source maps');
    }
  }
  
  // Remove comments from JavaScript files
  Object.entries(analysis.files).forEach(([file, info]) => {
    if (info.hasComments) {
      try {
        // Simple comment removal (basic implementation)
        let content = fs.readFileSync(file, 'utf8');
        
        // Remove single-line comments (basic regex)
        content = content.replace(/\/\/.*$/gm, '');
        
        // Remove multi-line comments (basic regex)
        content = content.replace(/\/\*[\s\S]*?\*\//g, '');
        
        fs.writeFileSync(file, content);
        optimizationsApplied++;
      } catch (error) {
        log.verbose(`Could not optimize ${file}: ${error.message}`);
      }
    }
  });
  
  // Compress large files
  if (optimizationsApplied > 0) {
    log.success(`Applied ${optimizationsApplied} optimizations`);
    
    // Recalculate size after optimization
    const newSize = parseInt(execSync('du -sb dist', { encoding: 'utf8' }).split('\t')[0]);
    const sizeSaved = analysis.optimization.totalSize - newSize;
    
    if (sizeSaved > 0) {
      log.success(`Saved ${(sizeSaved / 1024 / 1024).toFixed(2)}MB through optimization`);
    }
  } else {
    log.info('No optimizations applied');
  }
}

// Generate optimization report
function generateOptimizationReport() {
  if (!config.generateReport) {
    return;
  }
  
  const report = {
    timestamp: new Date().toISOString(),
    analysis: analysis,
    configuration: config,
    summary: {
      totalFiles: analysis.optimization.totalFiles,
      totalSize: analysis.optimization.totalSize,
      totalSizeFormatted: `${(analysis.optimization.totalSize / 1024 / 1024).toFixed(2)}MB`,
      sourceMaps: analysis.optimization.sourceMaps,
      recommendations: analysis.recommendations.length
    },
    environment: {
      nodeVersion: process.version,
      platform: process.platform,
      buildTime: new Date().toISOString()
    }
  };
  
  const reportFile = `build-optimization-${Date.now()}.json`;
  fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));
  log.success(`Optimization report saved: ${reportFile}`);
  
  return report;
}

// Main optimizer function
async function runOptimizer() {
  console.log(`${colors.bold}${colors.blue}⚡ Build Optimizer - Discord Bot EnergeX${colors.reset}`);
  console.log(`${colors.blue}Analyze: ${config.analyze} | Optimize: ${config.optimize} | Report: ${config.generateReport}${colors.reset}`);
  console.log('================================================================\n');
  
  try {
    // Always analyze first
    analyzeFiles();
    analyzeDependencies();
    generateRecommendations();
    
    // Apply optimizations if requested
    if (config.optimize) {
      applyOptimizations();
    }
    
    // Generate report
    const report = generateOptimizationReport();
    
    // Show summary
    console.log(`\n${colors.bold}${colors.blue}📊 Optimization Summary${colors.reset}`);
    console.log('================================');
    console.log(`Files analyzed: ${colors.cyan}${analysis.optimization.totalFiles}${colors.reset}`);
    console.log(`Total size: ${colors.cyan}${(analysis.optimization.totalSize / 1024 / 1024).toFixed(2)}MB${colors.reset}`);
    console.log(`Source maps: ${colors.cyan}${analysis.optimization.sourceMaps}${colors.reset}`);
    console.log(`Recommendations: ${colors.yellow}${analysis.recommendations.length}${colors.reset}`);
    
    if (analysis.recommendations.length > 0) {
      console.log(`\n${colors.yellow}💡 Optimization Recommendations:${colors.reset}`);
      analysis.recommendations.forEach((rec, index) => {
        console.log(`  ${index + 1}. ${rec}`);
      });
      
      if (!config.optimize) {
        console.log(`\n${colors.cyan}To apply optimizations, run:${colors.reset}`);
        console.log(`  node scripts/build-optimizer.js --optimize`);
      }
    } else {
      console.log(`\n${colors.green}✅ Build is already optimized${colors.reset}`);
    }
    
    process.exit(0);
    
  } catch (error) {
    log.error(`Build optimization failed: ${error.message}`);
    process.exit(1);
  }
}

// Handle interruption
process.on('SIGINT', () => {
  log.error('Build optimization interrupted');
  process.exit(1);
});

// Change to project root
process.chdir(path.join(__dirname, '..'));

// Run optimizer
runOptimizer();
