#!/usr/bin/env node

/**
 * Setup Wizard for Discord Bot EnergeX
 * Interactive setup for first-time configuration
 * 
 * Usage: node scripts/setup-wizard.js
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');
const crypto = require('crypto');

// Colors
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  bold: '\x1b[1m'
};

// Create readline interface
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Promisify readline question
const question = (query) => new Promise(resolve => rl.question(query, resolve));

// Logging
const log = {
  title: (msg) => console.log(`\n${colors.bold}${colors.blue}${msg}${colors.reset}`),
  info: (msg) => console.log(`${colors.cyan}ℹ️  ${msg}${colors.reset}`),
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`),
  error: (msg) => console.error(`${colors.red}❌ ${msg}${colors.reset}`)
};

// Configuration storage
const config = {};

// Setup wizard steps
async function welcomeMessage() {
  log.title('🚀 Discord Bot EnergeX - Setup Wizard');
  console.log('This wizard will help you configure your Discord bot for the first time.\n');
  
  log.info('What you\'ll need:');
  console.log('  • Discord Application (from https://discord.com/developers/applications)');
  console.log('  • PostgreSQL database (Neon recommended: https://neon.tech)');
  console.log('  • 5-10 minutes of your time\n');
  
  const proceed = await question('Ready to start? (y/N): ');
  if (proceed.toLowerCase() !== 'y' && proceed.toLowerCase() !== 'yes') {
    log.info('Setup cancelled. Run again when ready!');
    process.exit(0);
  }
}

async function selectEnvironment() {
  log.title('🌍 Environment Selection');
  console.log('1. Development (local development with hot reload)');
  console.log('2. Production (deployment to cloud platforms)');
  
  const choice = await question('\nSelect environment (1-2): ');
  
  switch (choice) {
    case '1':
      config.environment = 'development';
      config.port = '3000';
      config.nodeEnv = 'development';
      break;
    case '2':
      config.environment = 'production';
      config.port = '8080';
      config.nodeEnv = 'production';
      break;
    default:
      log.warning('Invalid choice, defaulting to development');
      config.environment = 'development';
      config.port = '3000';
      config.nodeEnv = 'development';
  }
  
  log.success(`Environment: ${config.environment}`);
}

async function configureDiscord() {
  log.title('🤖 Discord Configuration');
  log.info('Go to https://discord.com/developers/applications and create a new application');
  
  config.botClientId = await question('Discord Application ID (Client ID): ');
  config.botClientSecret = await question('Discord Application Client Secret: ');
  
  // Use same values for OAuth
  config.discordClientId = config.botClientId;
  config.discordClientSecret = config.botClientSecret;
  
  const addBot = await question('\nDo you want to enable bot functionality? (y/N): ');
  if (addBot.toLowerCase() === 'y' || addBot.toLowerCase() === 'yes') {
    log.info('Go to the Bot section in your Discord application and create a bot');
    config.discordToken = await question('Discord Bot Token: ');
    
    if (config.environment === 'development') {
      config.guildId = await question('Test Server Guild ID (optional, for faster development): ');
    }
  } else {
    log.info('Bot functionality disabled - OAuth-only mode');
  }
}

async function configureDatabase() {
  log.title('🗄️ Database Configuration');
  log.info('Recommended: Create a free database at https://neon.tech');
  
  config.databaseUrl = await question('PostgreSQL Database URL: ');
  
  // Validate database URL format
  if (!config.databaseUrl.startsWith('postgresql://')) {
    log.warning('Database URL should start with postgresql://');
  }
}

async function configureSecurity() {
  if (config.environment === 'development') {
    const addSecurity = await question('\nAdd security keys for development? (y/N): ');
    if (addSecurity.toLowerCase() !== 'y' && addSecurity.toLowerCase() !== 'yes') {
      log.info('Skipping security configuration for development');
      return;
    }
  }
  
  log.title('🔐 Security Configuration');
  log.info('Generating secure encryption keys...');
  
  config.userEncryptionKey = crypto.randomBytes(32).toString('hex');
  config.sessionEncryptionKey = crypto.randomBytes(32).toString('hex');
  config.csrfEncryptionKey = crypto.randomBytes(32).toString('hex');
  
  log.success('Security keys generated');
}

async function configureUrls() {
  if (config.environment === 'development') {
    config.webUrl = 'http://localhost:3000';
    config.appUrl = 'http://localhost:3000';
    config.apiEndpoint = 'http://localhost:8080';
    log.info('Using default development URLs');
    return;
  }
  
  log.title('🌐 URL Configuration');
  config.webUrl = await question('Frontend URL (e.g., https://your-app.com): ');
  config.appUrl = config.webUrl;
  config.apiEndpoint = await question('Backend API URL (e.g., https://api.your-app.com): ');
}

async function configureOptional() {
  log.title('🎛️ Optional Features');
  
  const addAi = await question('Enable AI features with Anthropic Claude? (y/N): ');
  if (addAi.toLowerCase() === 'y' || addAi.toLowerCase() === 'yes') {
    config.anthropicApiKey = await question('Anthropic API Key: ');
    config.useMastra = 'true';
    config.defaultAiChannel = 'general';
  }
  
  const addRedis = await question('Add Redis for caching? (y/N): ');
  if (addRedis.toLowerCase() === 'y' || addRedis.toLowerCase() === 'yes') {
    config.redisUrl = await question('Redis URL (default: redis://localhost:6379): ') || 'redis://localhost:6379';
  }
}

async function generateEnvFile() {
  log.title('📝 Generating Environment File');
  
  const envContent = [
    '# Discord Bot EnergeX - Generated Configuration',
    `# Generated on: ${new Date().toISOString()}`,
    `# Environment: ${config.environment}`,
    '',
    '# =============================================================================',
    '# ENVIRONMENT CONFIGURATION',
    '# =============================================================================',
    `NODE_ENV=${config.nodeEnv}`,
    `PORT=${config.port}`,
    'HOST=0.0.0.0',
    '',
    '# =============================================================================',
    '# DISCORD CONFIGURATION',
    '# =============================================================================',
    `BOT_CLIENT_ID=${config.botClientId}`,
    `BOT_CLIENT_SECRET=${config.botClientSecret}`,
    `DISCORD_CLIENT_ID=${config.discordClientId}`,
    `DISCORD_CLIENT_SECRET=${config.discordClientSecret}`,
  ];
  
  if (config.discordToken) {
    envContent.push(`DISCORD_TOKEN=${config.discordToken}`);
  }
  
  if (config.guildId) {
    envContent.push(`GUILD_ID=${config.guildId}`);
  }
  
  envContent.push(
    '',
    '# =============================================================================',
    '# DATABASE CONFIGURATION',
    '# =============================================================================',
    `DATABASE_URL=${config.databaseUrl}`,
  );
  
  if (config.userEncryptionKey) {
    envContent.push(
      '',
      '# =============================================================================',
      '# SECURITY CONFIGURATION',
      '# =============================================================================',
      `USER_ENCRYPTION_KEY=${config.userEncryptionKey}`,
      `SESSION_ENCRYPTION_KEY=${config.sessionEncryptionKey}`,
      `CSRF_ENCRYPTION_KEY=${config.csrfEncryptionKey}`,
    );
  }
  
  if (config.webUrl) {
    envContent.push(
      '',
      '# =============================================================================',
      '# APPLICATION URLS',
      '# =============================================================================',
      `WEB_URL=${config.webUrl}`,
      `APP_URL=${config.appUrl}`,
      `NEXT_PUBLIC_API_ENDPOINT=${config.apiEndpoint}`,
      `INTERNAL_API_ENDPOINT=${config.apiEndpoint}`,
    );
  }
  
  if (config.anthropicApiKey) {
    envContent.push(
      '',
      '# =============================================================================',
      '# AI CONFIGURATION',
      '# =============================================================================',
      `ANTHROPIC_API_KEY=${config.anthropicApiKey}`,
      `USE_MASTRA=${config.useMastra}`,
      `DEFAULT_AI_CHANNEL=${config.defaultAiChannel}`,
    );
  }
  
  if (config.redisUrl) {
    envContent.push(
      '',
      '# =============================================================================',
      '# REDIS CONFIGURATION',
      '# =============================================================================',
      `REDIS_URL=${config.redisUrl}`,
    );
  }
  
  const envFile = config.environment === 'development' ? '.env.local' : '.env';
  fs.writeFileSync(envFile, envContent.join('\n') + '\n');
  
  log.success(`Environment file created: ${envFile}`);
}

async function runFinalValidation() {
  log.title('🔍 Final Validation');
  log.info('Running preflight checks...');
  
  try {
    const { execSync } = require('child_process');
    const result = execSync(`node scripts/preflight.js --env=${config.environment}`, { 
      encoding: 'utf8',
      stdio: 'pipe'
    });
    
    log.success('Preflight validation passed!');
    return true;
  } catch (error) {
    log.error('Preflight validation failed');
    log.warning('You may need to fix some issues manually');
    return false;
  }
}

async function showNextSteps() {
  log.title('🎉 Setup Complete!');
  
  console.log(`Your Discord bot is configured for ${colors.cyan}${config.environment}${colors.reset} environment.\n`);
  
  log.info('Next steps:');
  
  if (config.environment === 'development') {
    console.log('  1. Start development server:');
    console.log(`     ${colors.green}pnpm run dev${colors.reset}`);
    console.log('  2. Open your browser:');
    console.log(`     ${colors.blue}http://localhost:3000${colors.reset}`);
    console.log('  3. API documentation:');
    console.log(`     ${colors.blue}http://localhost:8080/docs${colors.reset}`);
  } else {
    console.log('  1. Deploy to your platform (Sevalla, Railway, etc.)');
    console.log('  2. Configure environment variables in platform dashboard');
    console.log('  3. Deploy and monitor logs');
    console.log(`  4. Test deployment: ${colors.blue}${config.webUrl}${colors.reset}`);
  }
  
  console.log('\n📚 Documentation:');
  console.log('  • PREFLIGHT_GUIDE.md - Comprehensive setup guide');
  console.log('  • PREFLIGHT_README.md - Preflight system overview');
  console.log('  • README.md - Project documentation');
  
  console.log('\n🔧 Useful commands:');
  console.log(`  ${colors.green}pnpm run preflight:quick${colors.reset}  # Quick health check`);
  console.log(`  ${colors.green}pnpm run check:env${colors.reset}       # Check environment variables`);
  console.log(`  ${colors.green}pnpm run build${colors.reset}           # Build application`);
  console.log(`  ${colors.green}pnpm run test${colors.reset}            # Run tests`);
}

// Main setup wizard
async function runSetupWizard() {
  try {
    await welcomeMessage();
    await selectEnvironment();
    await configureDiscord();
    await configureDatabase();
    await configureSecurity();
    await configureUrls();
    await configureOptional();
    await generateEnvFile();
    
    const validationPassed = await runFinalValidation();
    await showNextSteps();
    
    if (!validationPassed) {
      log.warning('Some validation checks failed - review the output above');
      process.exit(1);
    }
    
  } catch (error) {
    log.error(`Setup wizard failed: ${error.message}`);
    process.exit(1);
  } finally {
    rl.close();
  }
}

// Handle interruption
process.on('SIGINT', () => {
  console.log('\n');
  log.info('Setup wizard cancelled');
  rl.close();
  process.exit(0);
});

// Change to project root
process.chdir(path.join(__dirname, '..'));

// Run the wizard
runSetupWizard();
