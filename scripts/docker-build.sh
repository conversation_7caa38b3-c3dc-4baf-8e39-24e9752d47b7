#!/bin/bash

# Docker Build Script for Discord Bot EnergeX
# Optimized multi-stage Docker build with validation
# Usage: ./scripts/docker-build.sh [--tag=name] [--push] [--test] [--no-cache]

set -euo pipefail

# Configuration
IMAGE_TAG="discord-bot-energex"
PUSH_IMAGE=false
TEST_BUILD=false
NO_CACHE=false
PLATFORM="linux/amd64"

# Parse arguments
for arg in "$@"; do
    case $arg in
        --tag=*)
            IMAGE_TAG="${arg#*=}"
            ;;
        --push)
            PUSH_IMAGE=true
            ;;
        --test)
            TEST_BUILD=true
            ;;
        --no-cache)
            NO_CACHE=true
            ;;
        --platform=*)
            PLATFORM="${arg#*=}"
            ;;
        --help)
            echo "Usage: $0 [options]"
            echo "Options:"
            echo "  --tag=name         Docker image tag (default: discord-bot-energex)"
            echo "  --push            Push image to registry after build"
            echo "  --test            Test the built image"
            echo "  --no-cache        Build without cache"
            echo "  --platform=arch   Target platform (default: linux/amd64)"
            exit 0
            ;;
        *)
            echo "Unknown argument: $arg"
            exit 1
            ;;
    esac
done

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m'

# Logging
log() { echo -e "${BLUE}[$(date '+%H:%M:%S')]${NC} $1"; }
success() { echo -e "${GREEN}✅ [$(date '+%H:%M:%S')]${NC} $1"; }
warning() { echo -e "${YELLOW}⚠️  [$(date '+%H:%M:%S')]${NC} $1"; }
error() { echo -e "${RED}❌ [$(date '+%H:%M:%S')]${NC} $1" >&2; }
info() { echo -e "${CYAN}ℹ️  [$(date '+%H:%M:%S')]${NC} $1"; }

# Build metrics
BUILD_START=""
BUILD_END=""
IMAGE_SIZE=""
BUILD_DURATION=""

# Pre-build validation
validate_docker_environment() {
    log "Validating Docker environment..."
    
    # Check Docker availability
    if ! command -v docker >/dev/null 2>&1; then
        error "Docker is not installed or not available"
        return 1
    fi
    
    # Check Docker daemon
    if ! docker info >/dev/null 2>&1; then
        error "Docker daemon is not running"
        return 1
    fi
    
    # Check Dockerfile
    if [[ ! -f "Dockerfile" ]]; then
        error "Dockerfile not found"
        return 1
    fi
    
    # Run Docker preflight
    if ! ./scripts/docker-preflight.sh >/dev/null 2>&1; then
        error "Docker preflight check failed"
        return 1
    fi
    
    success "Docker environment validation passed"
    return 0
}

# Build Docker image
build_docker_image() {
    log "Building Docker image: $IMAGE_TAG"
    
    BUILD_START=$(date +%s)
    
    # Prepare build arguments
    local build_args=""
    local cache_args=""
    
    # Add cache bust for production builds
    local cache_bust=$(date +%s)
    build_args="--build-arg CACHE_BUST=$cache_bust"
    
    # Platform specification
    build_args="$build_args --platform $PLATFORM"
    
    # Cache configuration
    if [[ "$NO_CACHE" == "true" ]]; then
        cache_args="--no-cache"
    else
        cache_args="--cache-from $IMAGE_TAG:latest"
    fi
    
    # Build command
    local docker_command="docker build $build_args $cache_args -t $IMAGE_TAG ."
    
    info "Docker build command: $docker_command"
    
    if eval "$docker_command"; then
        BUILD_END=$(date +%s)
        BUILD_DURATION=$((BUILD_END - BUILD_START))
        
        # Get image size
        IMAGE_SIZE=$(docker images $IMAGE_TAG --format "table {{.Size}}" | tail -n +2)
        
        success "Docker image built successfully in ${BUILD_DURATION}s"
        info "Image size: $IMAGE_SIZE"
        return 0
    else
        BUILD_END=$(date +%s)
        BUILD_DURATION=$((BUILD_END - BUILD_START))
        error "Docker build failed after ${BUILD_DURATION}s"
        return 1
    fi
}

# Test Docker image
test_docker_image() {
    if [[ "$TEST_BUILD" != "true" ]]; then
        return 0
    fi
    
    log "Testing Docker image..."
    
    # Test image can start
    local container_id=""
    if container_id=$(docker run -d --env NODE_ENV=production --env PORT=8080 --env DATABASE_URL=postgresql://test:test@localhost:5432/test $IMAGE_TAG); then
        sleep 5
        
        # Check if container is still running
        if docker ps | grep -q "$container_id"; then
            success "Docker image test passed - container started successfully"
            docker stop "$container_id" >/dev/null 2>&1
            docker rm "$container_id" >/dev/null 2>&1
        else
            error "Docker container exited immediately"
            docker logs "$container_id" 2>&1 | head -20
            docker rm "$container_id" >/dev/null 2>&1
            return 1
        fi
    else
        error "Failed to start Docker container"
        return 1
    fi
    
    return 0
}

# Security scan
security_scan() {
    log "Running security scan..."
    
    # Check for known vulnerabilities in base image
    if command -v docker >/dev/null 2>&1; then
        # Basic security check - look for common issues
        local security_issues=0
        
        # Check if running as root
        if docker run --rm $IMAGE_TAG whoami | grep -q "root"; then
            warning "Container runs as root user"
            ((security_issues++))
        fi
        
        # Check exposed ports
        local exposed_ports=$(docker inspect $IMAGE_TAG | grep -o '"ExposedPorts":{[^}]*}' || echo "")
        if [[ -n "$exposed_ports" ]]; then
            info "Exposed ports: $exposed_ports"
        fi
        
        if [[ $security_issues -eq 0 ]]; then
            success "Basic security scan passed"
        else
            warning "$security_issues security concerns found"
        fi
    fi
    
    return 0
}

# Push to registry
push_image() {
    if [[ "$PUSH_IMAGE" != "true" ]]; then
        return 0
    fi
    
    log "Pushing image to registry..."
    
    if docker push "$IMAGE_TAG"; then
        success "Image pushed successfully"
        return 0
    else
        error "Failed to push image"
        return 1
    fi
}

# Generate build report
generate_docker_report() {
    local report_file="docker-build-report-$(date +%Y%m%d-%H%M%S).json"
    
    cat > "$report_file" << EOF
{
  "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "image": {
    "tag": "$IMAGE_TAG",
    "size": "$IMAGE_SIZE",
    "platform": "$PLATFORM"
  },
  "build": {
    "duration": $BUILD_DURATION,
    "cache_used": $([ "$NO_CACHE" = "true" ] && echo "false" || echo "true"),
    "target": "$config.target"
  },
  "testing": {
    "executed": $TEST_BUILD,
    "passed": true
  },
  "deployment": {
    "pushed": $PUSH_IMAGE
  },
  "environment": {
    "docker_version": "$(docker --version)",
    "build_platform": "$(uname -m)",
    "build_os": "$(uname -s)"
  }
}
EOF
    
    info "Docker build report saved: $report_file"
}

# Main Docker build function
main() {
    echo -e "${BOLD}${BLUE}🐳 Docker Build Pipeline - Discord Bot EnergeX${NC}"
    echo -e "${BLUE}Image: $IMAGE_TAG | Platform: $PLATFORM | Test: $TEST_BUILD${NC}"
    echo "=================================================================="
    echo ""
    
    # Change to project root
    cd "$(dirname "$0")/.."
    
    # Run validation and build steps
    if ! validate_docker_environment; then
        error "Docker environment validation failed"
        exit 1
    fi
    
    if ! build_docker_image; then
        error "Docker image build failed"
        exit 1
    fi
    
    if ! test_docker_image; then
        error "Docker image test failed"
        exit 1
    fi
    
    security_scan || true  # Non-critical
    
    if ! push_image; then
        warning "Image push failed or skipped"
    fi
    
    # Generate report
    generate_docker_report
    
    echo ""
    echo -e "${BOLD}${GREEN}🎉 DOCKER BUILD COMPLETED SUCCESSFULLY${NC}"
    echo "=================================================================="
    echo -e "Image: ${CYAN}$IMAGE_TAG${NC}"
    echo -e "Size: ${CYAN}$IMAGE_SIZE${NC}"
    echo -e "Duration: ${CYAN}${BUILD_DURATION}s${NC}"
    echo ""
    echo -e "${GREEN}🚀 Docker image ready for deployment${NC}"
    echo "Next steps:"
    echo "  • Test locally: docker run --env-file .env -p 8080:8080 $IMAGE_TAG"
    echo "  • Deploy to platform: use your deployment pipeline"
    echo "  • Health check: curl http://localhost:8080/api/health"
    echo ""
}

# Handle interruption
trap 'error "Docker build interrupted"; exit 1' INT TERM

# Run main function
main "$@"
