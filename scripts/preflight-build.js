#!/usr/bin/env node

/**
 * Preflight Build System for Discord Bot EnergeX
 * Advanced build validation with metrics and optimization
 * 
 * Usage: node scripts/preflight-build.js [options]
 * Options:
 *   --mode=dev|prod    Build mode (default: dev)
 *   --clean           Clean build (remove dist/)
 *   --analyze         Analyze build output and dependencies
 *   --fix             Auto-fix common TypeScript/ESLint issues
 *   --watch           Watch mode for development
 *   --json            Output results in JSON format
 *   --verbose         Enable verbose output
 */

const fs = require('fs');
const path = require('path');
const { execSync, spawn } = require('child_process');
const crypto = require('crypto');

// Configuration
const config = {
  mode: 'dev',
  clean: false,
  analyze: false,
  autoFix: false,
  watch: false,
  json: false,
  verbose: false
};

// Parse command line arguments
process.argv.slice(2).forEach(arg => {
  if (arg.startsWith('--mode=')) {
    config.mode = arg.split('=')[1];
  } else if (arg === '--clean') {
    config.clean = true;
  } else if (arg === '--analyze') {
    config.analyze = true;
  } else if (arg === '--fix') {
    config.autoFix = true;
  } else if (arg === '--watch') {
    config.watch = true;
  } else if (arg === '--json') {
    config.json = true;
  } else if (arg === '--verbose') {
    config.verbose = true;
  } else if (arg === '--help') {
    console.log(`
Usage: node scripts/preflight-build.js [options]

Options:
  --mode=dev|prod    Build mode (default: dev)
  --clean           Clean build (remove dist/)
  --analyze         Analyze build output and dependencies
  --fix             Auto-fix common TypeScript/ESLint issues
  --watch           Watch mode for development
  --json            Output results in JSON format
  --verbose         Enable verbose output
  --help            Show this help message
    `);
    process.exit(0);
  }
});

// Colors
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  bold: '\x1b[1m'
};

// Logging functions
const log = {
  info: (msg) => !config.json && console.log(`${colors.blue}ℹ️  ${msg}${colors.reset}`),
  success: (msg) => !config.json && console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  warning: (msg) => !config.json && console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`),
  error: (msg) => !config.json && console.error(`${colors.red}❌ ${msg}${colors.reset}`),
  verbose: (msg) => config.verbose && !config.json && console.log(`${colors.cyan}🔍 ${msg}${colors.reset}`)
};

// Build metrics
const metrics = {
  startTime: null,
  endTime: null,
  duration: 0,
  size: 0,
  files: 0,
  errors: [],
  warnings: [],
  dependencies: {},
  performance: {}
};

// Pre-build validation
async function validatePreBuild() {
  log.info('Running pre-build validation...');
  
  // Check Node.js version
  const nodeVersion = process.version;
  const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
  
  if (majorVersion < 18) {
    throw new Error(`Node.js version must be >= 18.17.0, found: ${nodeVersion}`);
  }
  
  // Check required files
  const requiredFiles = [
    'package.json',
    'tsconfig.json',
    'nest-cli.json',
    'src/main.ts',
    'src/app.module.ts'
  ];
  
  for (const file of requiredFiles) {
    if (!fs.existsSync(file)) {
      throw new Error(`Required file missing: ${file}`);
    }
  }
  
  // Check dependencies
  if (!fs.existsSync('node_modules')) {
    throw new Error('node_modules not found - run "pnpm install"');
  }
  
  log.success('Pre-build validation passed');
  return true;
}

// TypeScript validation with detailed error reporting
async function validateTypeScript() {
  log.info('Validating TypeScript...');
  
  try {
    execSync('npx tsc --noEmit --skipLibCheck', { stdio: 'pipe' });
    log.success('TypeScript validation passed');
    return true;
  } catch (error) {
    if (config.autoFix) {
      log.warning('TypeScript errors found - attempting auto-fix...');
      
      try {
        execSync('pnpm run lint:fix', { stdio: 'pipe' });
        execSync('npx tsc --noEmit --skipLibCheck', { stdio: 'pipe' });
        log.success('TypeScript errors auto-fixed');
        return true;
      } catch (fixError) {
        metrics.errors.push('TypeScript compilation failed after auto-fix');
        throw new Error('TypeScript compilation errors persist after auto-fix');
      }
    } else {
      metrics.errors.push('TypeScript compilation failed');
      throw new Error('TypeScript compilation errors found - use --fix to auto-fix');
    }
  }
}

// ESLint validation
async function validateESLint() {
  log.info('Running ESLint validation...');
  
  try {
    execSync('npx eslint "{src,apps,libs,test}/**/*.ts" --quiet', { stdio: 'pipe' });
    log.success('ESLint validation passed');
    return true;
  } catch (error) {
    if (config.autoFix) {
      log.warning('ESLint issues found - attempting auto-fix...');
      try {
        execSync('pnpm run lint:fix', { stdio: 'pipe' });
        log.success('ESLint issues auto-fixed');
      } catch (fixError) {
        log.warning('Some ESLint issues could not be auto-fixed');
      }
    } else {
      metrics.warnings.push('ESLint issues found');
      log.warning('ESLint issues found - run "pnpm run lint:fix" to resolve');
    }
    return true; // Non-critical
  }
}

// Clean build directory
async function cleanBuild() {
  if (config.clean || config.mode === 'prod') {
    log.info('Cleaning build directory...');
    
    if (fs.existsSync('dist')) {
      fs.rmSync('dist', { recursive: true, force: true });
    }
    
    log.success('Build directory cleaned');
  }
}

// Execute build with metrics
async function executeBuild() {
  log.info(`Executing ${config.mode} build...`);
  
  metrics.startTime = Date.now();
  
  const buildCommand = config.mode === 'prod' 
    ? 'nest build --webpack'
    : 'nest build';
  
  try {
    const output = execSync(buildCommand, { 
      encoding: 'utf8',
      stdio: config.verbose ? 'inherit' : 'pipe'
    });
    
    metrics.endTime = Date.now();
    metrics.duration = (metrics.endTime - metrics.startTime) / 1000;
    
    log.verbose(`Build output: ${output}`);
    log.success(`Build completed in ${metrics.duration.toFixed(2)}s`);
    
    return true;
  } catch (error) {
    metrics.endTime = Date.now();
    metrics.duration = (metrics.endTime - metrics.startTime) / 1000;
    metrics.errors.push(`Build failed: ${error.message}`);
    
    throw new Error(`Build failed after ${metrics.duration.toFixed(2)}s`);
  }
}

// Validate build output with detailed analysis
async function validateBuildOutput() {
  log.info('Validating build output...');
  
  // Check dist directory
  if (!fs.existsSync('dist')) {
    throw new Error('Build directory "dist" was not created');
  }
  
  // Check main entry point
  if (!fs.existsSync('dist/src/main.js')) {
    throw new Error('Main entry point "dist/src/main.js" not found');
  }
  
  // Calculate build metrics
  const distStats = fs.statSync('dist');
  const distSize = execSync('du -sb dist', { encoding: 'utf8' }).split('\t')[0];
  metrics.size = parseInt(distSize);
  
  // Count files
  const jsFiles = execSync('find dist -name "*.js" | wc -l', { encoding: 'utf8' }).trim();
  metrics.files = parseInt(jsFiles);
  
  // Test syntax of main file
  try {
    execSync('node -c dist/src/main.js', { stdio: 'pipe' });
  } catch (error) {
    throw new Error('Built main.js has syntax errors');
  }
  
  log.success('Build output validation passed');
  log.info(`Build size: ${(metrics.size / 1024 / 1024).toFixed(2)}MB`);
  log.info(`JavaScript files: ${metrics.files}`);
  
  return true;
}

// Analyze build for optimization opportunities
async function analyzeBuild() {
  if (!config.analyze) {
    return true;
  }
  
  log.info('Analyzing build for optimization...');
  
  try {
    // Find large files
    const largeFiles = execSync('find dist -type f -size +1M', { encoding: 'utf8' }).trim();
    if (largeFiles) {
      metrics.warnings.push('Large files found in build');
      log.warning('Large files detected:');
      largeFiles.split('\n').forEach(file => {
        const size = execSync(`du -h "${file}"`, { encoding: 'utf8' }).split('\t')[0];
        log.warning(`  ${file} (${size})`);
      });
    }
    
    // Check for source maps in production
    if (config.mode === 'prod') {
      const sourceMaps = execSync('find dist -name "*.map" | wc -l', { encoding: 'utf8' }).trim();
      if (parseInt(sourceMaps) > 0) {
        metrics.warnings.push(`${sourceMaps} source maps in production build`);
        log.warning(`Source maps found in production build (${sourceMaps} files)`);
      }
    }
    
    // Dependency analysis
    if (fs.existsSync('dist/src')) {
      const bundleFiles = execSync('find dist/src -name "*.js" | head -5', { encoding: 'utf8' }).trim().split('\n');
      metrics.dependencies.bundled = bundleFiles.length;
    }
    
    log.success('Build analysis completed');
    return true;
  } catch (error) {
    log.warning(`Build analysis failed: ${error.message}`);
    return true; // Non-critical
  }
}

// Test build execution
async function testBuildExecution() {
  log.info('Testing build execution...');
  
  // Set test environment
  const testEnv = {
    ...process.env,
    NODE_ENV: config.mode,
    PORT: '0',
    DATABASE_URL: 'postgresql://test:test@localhost:5432/test'
  };
  
  return new Promise((resolve) => {
    const child = spawn('node', ['dist/src/main.js'], {
      env: testEnv,
      stdio: 'pipe'
    });
    
    let output = '';
    let hasStarted = false;
    
    child.stdout.on('data', (data) => {
      output += data.toString();
      if (output.includes('Application is running') || output.includes('Bootstrap')) {
        hasStarted = true;
        child.kill();
      }
    });
    
    child.stderr.on('data', (data) => {
      output += data.toString();
    });
    
    const timeout = setTimeout(() => {
      child.kill();
      if (!hasStarted) {
        metrics.warnings.push('Build execution test timed out');
        log.warning('Build execution test timed out');
      }
      resolve(true);
    }, 10000);
    
    child.on('close', (code) => {
      clearTimeout(timeout);
      
      if (hasStarted || code === 0) {
        log.success('Build execution test passed');
        resolve(true);
      } else {
        metrics.errors.push(`Build execution failed with code ${code}`);
        log.error(`Build execution test failed (exit code: ${code})`);
        log.verbose(`Output: ${output}`);
        resolve(false);
      }
    });
  });
}

// Generate comprehensive build report
function generateBuildReport() {
  const reportData = {
    timestamp: new Date().toISOString(),
    mode: config.mode,
    configuration: config,
    metrics: {
      ...metrics,
      duration: metrics.duration,
      sizeFormatted: `${(metrics.size / 1024 / 1024).toFixed(2)}MB`,
      filesCount: metrics.files
    },
    environment: {
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch,
      cwd: process.cwd()
    },
    tools: {
      pnpm: execSync('pnpm --version', { encoding: 'utf8' }).trim(),
      typescript: execSync('npx tsc --version', { encoding: 'utf8' }).split(' ')[1],
      nestjs: execSync('npx nest --version', { encoding: 'utf8' }).split('\n')[0].split(' ')[1]
    }
  };
  
  if (config.json) {
    console.log(JSON.stringify(reportData, null, 2));
  } else {
    const reportFile = `build-report-${Date.now()}.json`;
    fs.writeFileSync(reportFile, JSON.stringify(reportData, null, 2));
    log.info(`Build report saved: ${reportFile}`);
  }
  
  return reportData;
}

// Watch mode for development
async function watchMode() {
  if (!config.watch) {
    return;
  }
  
  log.info('Starting watch mode...');
  
  const watcher = spawn('nest', ['start', '--watch'], {
    stdio: 'inherit',
    env: { ...process.env, NODE_ENV: 'development' }
  });
  
  watcher.on('close', (code) => {
    log.info(`Watch mode exited with code ${code}`);
  });
  
  // Handle graceful shutdown
  process.on('SIGINT', () => {
    log.info('Stopping watch mode...');
    watcher.kill();
    process.exit(0);
  });
  
  return new Promise((resolve) => {
    watcher.on('close', resolve);
  });
}

// Main preflight build function
async function runPreflightBuild() {
  if (!config.json) {
    console.log(`${colors.bold}${colors.blue}🏗️  Discord Bot EnergeX - Preflight Build${colors.reset}`);
    console.log(`${colors.blue}Mode: ${config.mode} | Clean: ${config.clean} | Analyze: ${config.analyze}${colors.reset}`);
    console.log('==================================================================\n');
  }
  
  try {
    // Pre-build validation
    await validatePreBuild();
    
    // TypeScript validation
    await validateTypeScript();
    
    // ESLint validation (non-critical)
    await validateESLint();
    
    // Clean build if requested
    if (config.clean || config.mode === 'prod') {
      log.info('Cleaning build directory...');
      if (fs.existsSync('dist')) {
        fs.rmSync('dist', { recursive: true, force: true });
      }
      log.success('Build directory cleaned');
    }
    
    // Execute build
    await executeBuild();
    
    // Validate build output
    await validateBuildOutput();
    
    // Test build execution
    const executionPassed = await testBuildExecution();
    
    // Analyze build
    if (config.analyze) {
      await analyzeBuild();
    }
    
    // Generate report
    const report = generateBuildReport();
    
    // Watch mode
    if (config.watch) {
      await watchMode();
      return;
    }
    
    // Final summary
    if (!config.json) {
      console.log(`\n${colors.bold}${colors.green}🎉 PREFLIGHT BUILD COMPLETED${colors.reset}`);
      console.log('==================================');
      console.log(`Duration: ${colors.cyan}${metrics.duration.toFixed(2)}s${colors.reset}`);
      console.log(`Size: ${colors.cyan}${(metrics.size / 1024 / 1024).toFixed(2)}MB${colors.reset}`);
      console.log(`Files: ${colors.cyan}${metrics.files}${colors.reset}`);
      console.log(`Errors: ${colors.red}${metrics.errors.length}${colors.reset}`);
      console.log(`Warnings: ${colors.yellow}${metrics.warnings.length}${colors.reset}`);
      
      if (metrics.errors.length === 0) {
        console.log(`\n${colors.green}✅ Build ready for ${config.mode} environment${colors.reset}`);
        
        if (config.mode === 'prod') {
          console.log('\nNext steps:');
          console.log('  • Test: pnpm run start:prod');
          console.log('  • Deploy: docker build -t discord-bot .');
        } else {
          console.log('\nNext steps:');
          console.log('  • Start: pnpm run dev');
          console.log('  • Debug: pnpm run start:debug');
        }
      }
    }
    
    process.exit(metrics.errors.length === 0 ? 0 : 1);
    
  } catch (error) {
    metrics.errors.push(error.message);
    
    if (config.json) {
      console.log(JSON.stringify({ error: error.message, metrics }, null, 2));
    } else {
      log.error(`Preflight build failed: ${error.message}`);
    }
    
    process.exit(1);
  }
}

// Handle script interruption
process.on('SIGINT', () => {
  if (!config.json) {
    log.info('Preflight build interrupted');
  }
  process.exit(1);
});

// Change to project root
process.chdir(path.join(__dirname, '..'));

// Run preflight build
runPreflightBuild();
