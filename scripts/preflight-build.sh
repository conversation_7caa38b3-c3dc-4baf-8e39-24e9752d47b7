#!/bin/bash

# Preflight Build System for Discord Bot EnergeX
# Comprehensive build validation and optimization
# Usage: ./scripts/preflight-build.sh [--mode=dev|prod] [--clean] [--analyze] [--fix]

set -euo pipefail

# Configuration
MODE="dev"
CLEAN_BUILD=false
ANALYZE_BUILD=false
AUTO_FIX=false
VERBOSE=false

# Parse arguments
for arg in "$@"; do
    case $arg in
        --mode=*)
            MODE="${arg#*=}"
            ;;
        --clean)
            CLEAN_BUILD=true
            ;;
        --analyze)
            ANALYZE_BUILD=true
            ;;
        --fix)
            AUTO_FIX=true
            ;;
        --verbose)
            VERBOSE=true
            ;;
        --help)
            echo "Usage: $0 [options]"
            echo "Options:"
            echo "  --mode=dev|prod    Build mode (default: dev)"
            echo "  --clean           Clean build (remove dist/)"
            echo "  --analyze         Analyze build output"
            echo "  --fix             Auto-fix common issues"
            echo "  --verbose         Verbose output"
            exit 0
            ;;
        *)
            echo "Unknown argument: $arg"
            exit 1
            ;;
    esac
done

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m'

# Logging functions
log() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ [$(date '+%H:%M:%S')]${NC} $1"
}

warning() {
    echo -e "${YELLOW}⚠️  [$(date '+%H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}❌ [$(date '+%H:%M:%S')]${NC} $1" >&2
}

info() {
    echo -e "${CYAN}ℹ️  [$(date '+%H:%M:%S')]${NC} $1"
}

verbose() {
    [[ "$VERBOSE" == "true" ]] && echo -e "${CYAN}🔍 [$(date '+%H:%M:%S')]${NC} $1"
}

# Build metrics
BUILD_START_TIME=""
BUILD_END_TIME=""
BUILD_DURATION=""
BUILD_SIZE=""
BUILD_FILES=""

# Pre-build validation
validate_prebuild() {
    log "Running pre-build validation..."
    
    # Check Node.js version
    local node_version=$(node --version | sed 's/v//')
    local major_version=$(echo $node_version | cut -d. -f1)
    
    if [[ $major_version -lt 18 ]]; then
        error "Node.js version must be >= 18.17.0, found: v$node_version"
        return 1
    fi
    
    # Check pnpm
    if ! command -v pnpm >/dev/null 2>&1; then
        error "pnpm is not installed"
        return 1
    fi
    
    # Check dependencies
    if [[ ! -d "node_modules" ]]; then
        error "node_modules not found - run 'pnpm install'"
        return 1
    fi
    
    # Check TypeScript config
    if [[ ! -f "tsconfig.json" ]]; then
        error "tsconfig.json not found"
        return 1
    fi
    
    # Check NestJS config
    if [[ ! -f "nest-cli.json" ]]; then
        error "nest-cli.json not found"
        return 1
    fi
    
    success "Pre-build validation passed"
    return 0
}

# TypeScript validation
validate_typescript() {
    log "Validating TypeScript..."
    
    # Check for TypeScript errors
    if ! npx tsc --noEmit --skipLibCheck 2>/dev/null; then
        if [[ "$AUTO_FIX" == "true" ]]; then
            warning "TypeScript errors found - attempting auto-fix..."
            
            # Try to fix common issues
            pnpm run lint:fix >/dev/null 2>&1 || true
            
            # Retry TypeScript check
            if npx tsc --noEmit --skipLibCheck 2>/dev/null; then
                success "TypeScript errors auto-fixed"
            else
                error "TypeScript errors persist after auto-fix"
                error "Run 'npx tsc --noEmit' to see detailed errors"
                return 1
            fi
        else
            error "TypeScript compilation errors found"
            error "Run 'npx tsc --noEmit' for details or use --fix to auto-fix"
            return 1
        fi
    else
        success "TypeScript validation passed"
    fi
    
    return 0
}

# ESLint validation
validate_eslint() {
    log "Running ESLint validation..."
    
    if ! npx eslint "{src,apps,libs,test}/**/*.ts" --quiet 2>/dev/null; then
        if [[ "$AUTO_FIX" == "true" ]]; then
            warning "ESLint issues found - attempting auto-fix..."
            pnpm run lint:fix >/dev/null 2>&1 || true
            
            if npx eslint "{src,apps,libs,test}/**/*.ts" --quiet 2>/dev/null; then
                success "ESLint issues auto-fixed"
            else
                warning "Some ESLint issues remain after auto-fix"
            fi
        else
            warning "ESLint issues found - run 'pnpm run lint:fix' to resolve"
        fi
    else
        success "ESLint validation passed"
    fi
    
    return 0
}

# Clean build directory
clean_build() {
    if [[ "$CLEAN_BUILD" == "true" ]] || [[ "$MODE" == "prod" ]]; then
        log "Cleaning build directory..."
        rm -rf dist
        success "Build directory cleaned"
    fi
}

# Execute build
execute_build() {
    log "Executing build for $MODE mode..."
    
    BUILD_START_TIME=$(date +%s)
    
    local build_command=""
    local build_output=""
    
    if [[ "$MODE" == "prod" ]]; then
        build_command="nest build --webpack"
        info "Production build with webpack optimization"
    else
        build_command="nest build"
        info "Development build"
    fi
    
    # Capture build output
    if build_output=$(eval "$build_command" 2>&1); then
        BUILD_END_TIME=$(date +%s)
        BUILD_DURATION=$((BUILD_END_TIME - BUILD_START_TIME))
        success "Build completed in ${BUILD_DURATION}s"
        
        verbose "Build output: $build_output"
        return 0
    else
        BUILD_END_TIME=$(date +%s)
        BUILD_DURATION=$((BUILD_END_TIME - BUILD_START_TIME))
        error "Build failed after ${BUILD_DURATION}s"
        error "Build output: $build_output"
        return 1
    fi
}

# Validate build output
validate_build_output() {
    log "Validating build output..."
    
    # Check if dist directory was created
    if [[ ! -d "dist" ]]; then
        error "Build directory 'dist' was not created"
        return 1
    fi
    
    # Check main entry point
    if [[ ! -f "dist/src/main.js" ]]; then
        error "Main entry point 'dist/src/main.js' not found"
        return 1
    fi
    
    # Check if main.js is executable
    if ! node -c "dist/src/main.js" 2>/dev/null; then
        error "Built main.js has syntax errors"
        return 1
    fi
    
    # Calculate build size
    if command -v du >/dev/null 2>&1; then
        BUILD_SIZE=$(du -sh dist 2>/dev/null | cut -f1)
        info "Build size: $BUILD_SIZE"
    fi
    
    # Count build files
    if command -v find >/dev/null 2>&1; then
        BUILD_FILES=$(find dist -type f -name "*.js" | wc -l)
        info "JavaScript files: $BUILD_FILES"
    fi
    
    success "Build output validation passed"
    return 0
}

# Analyze build for optimization
analyze_build() {
    if [[ "$ANALYZE_BUILD" != "true" ]]; then
        return 0
    fi
    
    log "Analyzing build for optimization opportunities..."
    
    # Check for large files
    if command -v find >/dev/null 2>&1; then
        local large_files=$(find dist -type f -size +1M 2>/dev/null || true)
        if [[ -n "$large_files" ]]; then
            warning "Large files found in build:"
            echo "$large_files" | while read -r file; do
                local size=$(du -h "$file" 2>/dev/null | cut -f1)
                warning "  $file ($size)"
            done
        fi
    fi
    
    # Check for source maps in production
    if [[ "$MODE" == "prod" ]]; then
        local source_maps=$(find dist -name "*.map" 2>/dev/null | wc -l)
        if [[ $source_maps -gt 0 ]]; then
            warning "Source maps found in production build ($source_maps files)"
            warning "Consider disabling source maps for production"
        fi
    fi
    
    # Check for development dependencies in build
    if grep -r "development\|debug\|test" dist/ >/dev/null 2>&1; then
        warning "Development/debug code may be present in build"
    fi
    
    success "Build analysis completed"
    return 0
}

# Test build execution
test_build_execution() {
    log "Testing build execution..."
    
    # Set minimal environment for testing
    export NODE_ENV="${MODE}"
    export PORT="0"  # Use port 0 to avoid conflicts
    export DATABASE_URL="postgresql://test:test@localhost:5432/test"
    
    # Test if the built application can start (dry run)
    local test_output=""
    if test_output=$(timeout 10s node dist/src/main.js 2>&1 || true); then
        if echo "$test_output" | grep -q "Application is running\|Bootstrap\|NestApplication"; then
            success "Build execution test passed"
            return 0
        else
            warning "Build starts but may have issues"
            verbose "Test output: $test_output"
            return 0
        fi
    else
        error "Build execution test failed"
        error "Test output: $test_output"
        return 1
    fi
}

# Generate build report
generate_build_report() {
    local report_file="build-report-$(date +%Y%m%d-%H%M%S).json"
    
    log "Generating build report..."
    
    cat > "$report_file" << EOF
{
  "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "mode": "$MODE",
  "duration": $BUILD_DURATION,
  "size": "$BUILD_SIZE",
  "files": $BUILD_FILES,
  "node_version": "$(node --version)",
  "pnpm_version": "$(pnpm --version)",
  "typescript_version": "$(npx tsc --version | cut -d' ' -f2)",
  "nestjs_version": "$(npx nest --version | head -n1 | cut -d' ' -f2)",
  "clean_build": $CLEAN_BUILD,
  "analyzed": $ANALYZE_BUILD,
  "auto_fixed": $AUTO_FIX
}
EOF
    
    info "Build report saved: $report_file"
}

# Main preflight build function
main() {
    echo -e "${BOLD}${BLUE}🏗️  Discord Bot EnergeX - Preflight Build${NC}"
    echo -e "${BLUE}Mode: $MODE | Clean: $CLEAN_BUILD | Analyze: $ANALYZE_BUILD | Auto-fix: $AUTO_FIX${NC}"
    echo "=================================================================="
    echo ""
    
    # Change to project root
    cd "$(dirname "$0")/.."
    
    # Run validation steps
    if ! validate_prebuild; then
        error "Pre-build validation failed"
        exit 1
    fi
    
    if ! validate_typescript; then
        error "TypeScript validation failed"
        exit 1
    fi
    
    validate_eslint || true  # Non-critical
    
    # Clean if requested
    clean_build
    
    # Execute build
    if ! execute_build; then
        error "Build execution failed"
        exit 1
    fi
    
    # Validate build output
    if ! validate_build_output; then
        error "Build output validation failed"
        exit 1
    fi
    
    # Test build execution
    if ! test_build_execution; then
        warning "Build execution test failed - build may have runtime issues"
    fi
    
    # Analyze build
    analyze_build
    
    # Generate report
    generate_build_report
    
    echo ""
    echo -e "${BOLD}${GREEN}🎉 PREFLIGHT BUILD COMPLETED SUCCESSFULLY${NC}"
    echo "=================================================================="
    echo -e "Mode: ${CYAN}$MODE${NC}"
    echo -e "Duration: ${CYAN}${BUILD_DURATION}s${NC}"
    echo -e "Size: ${CYAN}$BUILD_SIZE${NC}"
    echo -e "Files: ${CYAN}$BUILD_FILES${NC}"
    echo ""
    
    if [[ "$MODE" == "prod" ]]; then
        echo -e "${GREEN}🚀 Production build ready for deployment${NC}"
        echo "Next steps:"
        echo "  • Test: pnpm run start:prod"
        echo "  • Deploy: docker build -t discord-bot ."
        echo "  • Health check: curl http://localhost:8080/api/health"
    else
        echo -e "${GREEN}🛠️  Development build ready${NC}"
        echo "Next steps:"
        echo "  • Start dev server: pnpm run dev"
        echo "  • Run tests: pnpm run test"
        echo "  • Debug: pnpm run start:debug"
    fi
    
    echo ""
}

# Handle interruption
trap 'error "Preflight build interrupted"; exit 1' INT TERM

# Run main function
main "$@"
