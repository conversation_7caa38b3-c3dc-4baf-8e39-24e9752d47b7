#!/bin/bash

# Preflight Check Script for Discord Bot EnergeX
# Comprehensive pre-deployment and pre-startup validation
# Usage: ./scripts/preflight.sh [--environment=dev|prod] [--skip-build] [--skip-db]

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m'

# Configuration
ENVIRONMENT="dev"
SKIP_BUILD=false
SKIP_DB=false
VERBOSE=false

# Parse command line arguments
for arg in "$@"; do
    case $arg in
        --environment=*)
            ENVIRONMENT="${arg#*=}"
            ;;
        --skip-build)
            SKIP_BUILD=true
            ;;
        --skip-db)
            SKIP_DB=true
            ;;
        --verbose)
            VERBOSE=true
            ;;
        --help)
            echo "Usage: $0 [--environment=dev|prod] [--skip-build] [--skip-db] [--verbose]"
            echo "  --environment=dev|prod  Set environment mode (default: dev)"
            echo "  --skip-build           Skip build validation"
            echo "  --skip-db              Skip database checks"
            echo "  --verbose              Enable verbose output"
            exit 0
            ;;
        *)
            echo "Unknown argument: $arg"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Logging functions
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ❌ ERROR:${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] ✅ SUCCESS:${NC} $1"
}

warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] ⚠️  WARNING:${NC} $1"
}

info() {
    echo -e "${CYAN}[$(date '+%Y-%m-%d %H:%M:%S')] ℹ️  INFO:${NC} $1"
}

# Global counters
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0
WARNING_CHECKS=0

# Function to run a check and track results
run_check() {
    local check_name="$1"
    local check_function="$2"
    local is_critical="${3:-true}"
    
    ((TOTAL_CHECKS++))
    
    log "Running check: $check_name"
    
    if $check_function; then
        success "$check_name passed"
        ((PASSED_CHECKS++))
        return 0
    else
        if [[ "$is_critical" == "true" ]]; then
            error "$check_name failed (CRITICAL)"
            ((FAILED_CHECKS++))
            return 1
        else
            warning "$check_name failed (NON-CRITICAL)"
            ((WARNING_CHECKS++))
            return 0
        fi
    fi
}

# System Requirements Check
check_system_requirements() {
    local node_version
    local pnpm_version
    
    # Check Node.js version
    if ! command -v node >/dev/null 2>&1; then
        error "Node.js is not installed"
        return 1
    fi
    
    node_version=$(node --version | sed 's/v//')
    if ! node -e "
        const version = process.version.slice(1);
        const [major] = version.split('.');
        if (parseInt(major) < 18) {
            console.error('Node.js version must be >= 18.17.0, found: ' + version);
            process.exit(1);
        }
    " 2>/dev/null; then
        error "Node.js version must be >= 18.17.0, found: v$node_version"
        return 1
    fi
    
    # Check pnpm
    if ! command -v pnpm >/dev/null 2>&1; then
        error "pnpm is not installed"
        return 1
    fi
    
    pnpm_version=$(pnpm --version)
    info "Node.js: v$node_version, pnpm: v$pnpm_version"
    
    return 0
}

# File Structure Check
check_file_structure() {
    local required_files=(
        "package.json"
        "tsconfig.json"
        "nest-cli.json"
        "src/main.ts"
        "src/app.module.ts"
        "drizzle.config.ts"
    )
    
    local required_dirs=(
        "src"
        "src/core"
        "src/discord"
        "src/api"
        "src/features"
        "migrations"
        "scripts"
    )
    
    # Check required files
    for file in "${required_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            error "Required file missing: $file"
            return 1
        fi
    done
    
    # Check required directories
    for dir in "${required_dirs[@]}"; do
        if [[ ! -d "$dir" ]]; then
            error "Required directory missing: $dir"
            return 1
        fi
    done
    
    return 0
}

# Dependencies Check
check_dependencies() {
    if [[ ! -d "node_modules" ]]; then
        error "node_modules directory not found - run 'pnpm install'"
        return 1
    fi
    
    # Check if package-lock is in sync
    if [[ -f "pnpm-lock.yaml" ]]; then
        if [[ "package.json" -nt "pnpm-lock.yaml" ]]; then
            warning "package.json is newer than pnpm-lock.yaml - consider running 'pnpm install'"
        fi
    fi
    
    # Check critical dependencies
    local critical_deps=(
        "@nestjs/core"
        "discord.js"
        "necord"
        "drizzle-orm"
        "pg"
    )
    
    for dep in "${critical_deps[@]}"; do
        if [[ ! -d "node_modules/$dep" ]]; then
            error "Critical dependency missing: $dep"
            return 1
        fi
    done
    
    return 0
}

# Environment Variables Check
check_environment_variables() {
    local missing_critical=()
    local missing_optional=()
    
    # Critical variables based on environment
    local critical_vars=()
    local optional_vars=()
    
    if [[ "$ENVIRONMENT" == "prod" ]]; then
        critical_vars=(
            "DATABASE_URL"
            "NODE_ENV"
            "PORT"
            "BOT_CLIENT_ID"
            "BOT_CLIENT_SECRET"
            "DISCORD_CLIENT_ID"
            "DISCORD_CLIENT_SECRET"
            "USER_ENCRYPTION_KEY"
            "SESSION_ENCRYPTION_KEY"
            "CSRF_ENCRYPTION_KEY"
            "WEB_URL"
        )
        optional_vars=(
            "DISCORD_TOKEN"
            "ANTHROPIC_API_KEY"
            "APP_URL"
            "NEXT_PUBLIC_API_ENDPOINT"
            "INTERNAL_API_ENDPOINT"
        )
    else
        critical_vars=(
            "DATABASE_URL"
            "BOT_CLIENT_ID"
            "BOT_CLIENT_SECRET"
            "DISCORD_CLIENT_ID"
            "DISCORD_CLIENT_SECRET"
        )
        optional_vars=(
            "DISCORD_TOKEN"
            "ANTHROPIC_API_KEY"
            "NODE_ENV"
            "PORT"
            "GUILD_ID"
        )
    fi
    
    # Check critical variables
    for var in "${critical_vars[@]}"; do
        if [[ -z "${!var:-}" ]]; then
            missing_critical+=("$var")
        fi
    done
    
    # Check optional variables
    for var in "${optional_vars[@]}"; do
        if [[ -z "${!var:-}" ]]; then
            missing_optional+=("$var")
        fi
    done
    
    # Report results
    if [[ ${#missing_critical[@]} -gt 0 ]]; then
        error "Missing critical environment variables: ${missing_critical[*]}"
        return 1
    fi
    
    if [[ ${#missing_optional[@]} -gt 0 ]]; then
        warning "Missing optional environment variables: ${missing_optional[*]}"
    fi
    
    return 0
}

# TypeScript Compilation Check
check_typescript() {
    if [[ "$SKIP_BUILD" == "true" ]]; then
        info "Skipping TypeScript compilation check"
        return 0
    fi
    
    log "Checking TypeScript compilation..."
    
    # Check if TypeScript is available
    if ! command -v npx >/dev/null 2>&1; then
        error "npx not available for TypeScript check"
        return 1
    fi
    
    # Run TypeScript compiler check
    if ! npx tsc --noEmit --skipLibCheck >/dev/null 2>&1; then
        error "TypeScript compilation errors found"
        warning "Run 'npx tsc --noEmit' to see detailed errors"
        return 1
    fi
    
    return 0
}

# Build Check
check_build() {
    if [[ "$SKIP_BUILD" == "true" ]]; then
        info "Skipping build check"
        return 0
    fi
    
    # Check if dist directory exists
    if [[ ! -d "dist" ]]; then
        error "Build directory 'dist' not found - run 'pnpm run build'"
        return 1
    fi
    
    # Check main entry point
    if [[ ! -f "dist/src/main.js" ]]; then
        error "Main application file 'dist/src/main.js' not found"
        return 1
    fi
    
    # Check if build is recent
    if [[ "src" -nt "dist" ]]; then
        warning "Source files are newer than build - consider rebuilding"
    fi
    
    return 0
}

# Database Connectivity Check
check_database_connectivity() {
    if [[ "$SKIP_DB" == "true" ]]; then
        info "Skipping database connectivity check"
        return 0
    fi
    
    if [[ -z "${DATABASE_URL:-}" ]]; then
        error "DATABASE_URL not set"
        return 1
    fi
    
    # Test database connection
    if ! node -e "
        const { Client } = require('pg');
        const client = new Client({ connectionString: process.env.DATABASE_URL });
        client.connect()
            .then(() => {
                console.log('Database connection successful');
                return client.query('SELECT 1 as test');
            })
            .then(() => {
                console.log('Database query test successful');
                client.end();
                process.exit(0);
            })
            .catch(err => {
                console.error('Database test failed:', err.message);
                process.exit(1);
            });
    " 2>/dev/null; then
        return 0
    else
        error "Database connectivity test failed"
        return 1
    fi
}

# Database Schema Check
check_database_schema() {
    if [[ "$SKIP_DB" == "true" ]]; then
        info "Skipping database schema check"
        return 0
    fi
    
    # Check if migrations directory exists
    if [[ ! -d "migrations" ]]; then
        warning "Migrations directory not found"
        return 0
    fi
    
    # Check if there are migration files
    if [[ -z "$(ls -A migrations/ 2>/dev/null)" ]]; then
        warning "No migration files found"
        return 0
    fi
    
    # Basic schema validation
    if ! node -e "
        const { Client } = require('pg');
        const client = new Client({ connectionString: process.env.DATABASE_URL });
        client.connect()
            .then(() => client.query('SELECT table_name FROM information_schema.tables WHERE table_schema = \\'public\\' LIMIT 1'))
            .then(result => {
                if (result.rows.length === 0) {
                    console.error('No tables found in database');
                    process.exit(1);
                }
                console.log('Database schema validation passed');
                client.end();
                process.exit(0);
            })
            .catch(err => {
                console.error('Schema validation failed:', err.message);
                process.exit(1);
            });
    " 2>/dev/null; then
        return 0
    else
        warning "Database schema validation failed"
        return 0  # Non-critical for preflight
    fi
}

# Port Availability Check
check_port_availability() {
    local port=${PORT:-8080}
    
    if command -v netstat >/dev/null 2>&1; then
        if netstat -tuln 2>/dev/null | grep -q ":$port "; then
            warning "Port $port is already in use"
            return 0  # Non-critical for preflight
        fi
    elif command -v ss >/dev/null 2>&1; then
        if ss -tuln 2>/dev/null | grep -q ":$port "; then
            warning "Port $port is already in use"
            return 0  # Non-critical for preflight
        fi
    fi
    
    return 0
}

# Discord Token Validation
check_discord_configuration() {
    # Check OAuth configuration (critical)
    if [[ -z "${BOT_CLIENT_ID:-}" ]] || [[ -z "${BOT_CLIENT_SECRET:-}" ]]; then
        error "Discord OAuth configuration incomplete"
        return 1
    fi
    
    # Check bot token (optional)
    if [[ -z "${DISCORD_TOKEN:-}" ]]; then
        warning "DISCORD_TOKEN not set - bot will run in OAuth-only mode"
        return 0
    fi
    
    # Basic token format validation
    if [[ ! "$DISCORD_TOKEN" =~ ^[A-Za-z0-9._-]+$ ]]; then
        error "DISCORD_TOKEN appears to have invalid format"
        return 1
    fi
    
    return 0
}

# Security Configuration Check
check_security_configuration() {
    local security_vars=(
        "USER_ENCRYPTION_KEY"
        "SESSION_ENCRYPTION_KEY"
        "CSRF_ENCRYPTION_KEY"
    )
    
    for var in "${security_vars[@]}"; do
        if [[ -z "${!var:-}" ]]; then
            if [[ "$ENVIRONMENT" == "prod" ]]; then
                error "Security variable $var is required in production"
                return 1
            else
                warning "Security variable $var not set (recommended for $ENVIRONMENT)"
            fi
        else
            # Check key length (should be 64 characters for 256-bit keys)
            local key_length=${#!var}
            if [[ $key_length -lt 32 ]]; then
                warning "$var appears to be too short (${key_length} chars, recommended: 64)"
            fi
        fi
    done
    
    return 0
}

# Lint Check
check_code_quality() {
    if ! command -v npx >/dev/null 2>&1; then
        warning "npx not available - skipping lint check"
        return 0
    fi
    
    # Run ESLint check
    if ! npx eslint "{src,apps,libs,test}/**/*.ts" --quiet >/dev/null 2>&1; then
        warning "ESLint found issues - run 'pnpm run lint:fix' to resolve"
        return 0  # Non-critical for preflight
    fi
    
    return 0
}

# Docker Configuration Check
check_docker_configuration() {
    if [[ ! -f "Dockerfile" ]]; then
        warning "Dockerfile not found"
        return 0
    fi
    
    # Check if Docker is available
    if command -v docker >/dev/null 2>&1; then
        # Validate Dockerfile syntax
        if ! docker build --dry-run . >/dev/null 2>&1; then
            warning "Dockerfile validation failed"
            return 0
        fi
    else
        info "Docker not available - skipping Docker validation"
    fi
    
    return 0
}

# Performance and Resource Check
check_system_resources() {
    # Check available memory
    if command -v free >/dev/null 2>&1; then
        local available_mem=$(free -m | awk 'NR==2{printf "%.0f", $7}')
        if [[ $available_mem -lt 512 ]]; then
            warning "Low available memory: ${available_mem}MB (recommended: >512MB)"
        fi
    fi
    
    # Check disk space
    if command -v df >/dev/null 2>&1; then
        local available_space=$(df . | awk 'NR==2{print $4}')
        if [[ $available_space -lt 1048576 ]]; then  # 1GB in KB
            warning "Low disk space available"
        fi
    fi
    
    return 0
}

# Show summary
show_summary() {
    echo ""
    echo -e "${BOLD}${BLUE}📊 PREFLIGHT SUMMARY${NC}"
    echo "=================================="
    echo -e "Environment: ${CYAN}$ENVIRONMENT${NC}"
    echo -e "Total checks: ${BLUE}$TOTAL_CHECKS${NC}"
    echo -e "Passed: ${GREEN}$PASSED_CHECKS${NC}"
    echo -e "Failed: ${RED}$FAILED_CHECKS${NC}"
    echo -e "Warnings: ${YELLOW}$WARNING_CHECKS${NC}"
    echo ""
    
    if [[ $FAILED_CHECKS -eq 0 ]]; then
        echo -e "${GREEN}🎉 PREFLIGHT PASSED${NC}"
        echo -e "${GREEN}Your Discord bot is ready for deployment/startup!${NC}"
        
        if [[ $WARNING_CHECKS -gt 0 ]]; then
            echo -e "${YELLOW}Note: $WARNING_CHECKS warnings found - review above for optimization opportunities${NC}"
        fi
        
        echo ""
        echo -e "${BLUE}Next steps:${NC}"
        if [[ "$ENVIRONMENT" == "prod" ]]; then
            echo "  • Deploy to production: docker build -t discord-bot ."
            echo "  • Or start production: pnpm run start:prod"
        else
            echo "  • Start development: pnpm run dev"
            echo "  • Or build and start: pnpm run build && pnpm run start"
        fi
        
        return 0
    else
        echo -e "${RED}❌ PREFLIGHT FAILED${NC}"
        echo -e "${RED}$FAILED_CHECKS critical issues must be resolved before deployment${NC}"
        echo ""
        echo -e "${BLUE}Recommended actions:${NC}"
        echo "  • Review error messages above"
        echo "  • Fix critical issues"
        echo "  • Re-run preflight: ./scripts/preflight.sh"
        
        return 1
    fi
}

# Main preflight execution
main() {
    echo -e "${BOLD}${BLUE}🚀 Discord Bot EnergeX - Preflight Check${NC}"
    echo -e "${BLUE}Environment: $ENVIRONMENT${NC}"
    echo -e "${BLUE}Skip Build: $SKIP_BUILD${NC}"
    echo -e "${BLUE}Skip DB: $SKIP_DB${NC}"
    echo "=================================="
    echo ""
    
    # Run all checks
    run_check "System Requirements" check_system_requirements true
    run_check "File Structure" check_file_structure true
    run_check "Dependencies" check_dependencies true
    run_check "Environment Variables" check_environment_variables true
    run_check "Security Configuration" check_security_configuration true
    run_check "Discord Configuration" check_discord_configuration true
    
    if [[ "$SKIP_BUILD" == "false" ]]; then
        run_check "TypeScript Compilation" check_typescript true
        run_check "Build Validation" check_build true
    fi
    
    if [[ "$SKIP_DB" == "false" ]]; then
        run_check "Database Connectivity" check_database_connectivity true
        run_check "Database Schema" check_database_schema false
    fi
    
    run_check "Code Quality" check_code_quality false
    run_check "Docker Configuration" check_docker_configuration false
    run_check "System Resources" check_system_resources false
    run_check "Port Availability" check_port_availability false
    
    # Show final summary
    show_summary
}

# Handle script interruption
trap 'error "Preflight check interrupted"; exit 1' INT TERM

# Change to script directory
cd "$(dirname "$0")/.."

# Run main function
main "$@"
