# 🚀 Preflight System - Discord Bot EnergeX

The preflight system ensures your Discord bot is properly configured and ready for deployment across different environments.

## 📁 Preflight Files Created

```
scripts/
├── preflight.sh              # Comprehensive bash-based preflight checker
├── preflight.js              # Node.js-based preflight checker with JSON output
├── quick-preflight.js        # Fast essential checks
├── docker-preflight.sh       # Docker-specific validation
└── check-env-vars.sh         # Environment variables checker (existing)

preflight.config.json         # Detailed preflight configuration
.env.example                  # Enhanced environment template
PREFLIGHT_GUIDE.md           # Comprehensive setup and troubleshooting guide
```

## 🎯 Quick Commands

### Essential Preflight Commands
```bash
# Quick health check (30 seconds)
pnpm run preflight:quick

# Full development check
pnpm run preflight:dev

# Full production check  
pnpm run preflight:prod

# Environment variables only
pnpm run check:env

# Docker-specific check
./scripts/docker-preflight.sh
```

### Setup Commands
```bash
# Complete development setup
pnpm run setup:dev

# Complete production setup
pnpm run setup:prod
```

## 🔍 What Gets Checked

### ✅ System Requirements
- Node.js version >= 18.17.0
- pnpm package manager
- System resources (memory, disk)

### ✅ Project Structure
- Required files (package.json, tsconfig.json, etc.)
- Source directories (src/, migrations/, scripts/)
- Configuration files

### ✅ Dependencies
- node_modules directory
- Critical packages (@nestjs/core, discord.js, etc.)
- Package lock file sync

### ✅ Environment Configuration
- **Development**: DATABASE_URL, Discord OAuth credentials
- **Production**: All dev vars + security keys, production URLs
- Security key validation (length, format)

### ✅ Discord Setup
- OAuth application configuration
- Bot token validation (optional)
- Required permissions and intents

### ✅ Database
- PostgreSQL connectivity
- Basic query execution
- Schema validation

### ✅ Build System
- TypeScript compilation
- Build output validation
- Code quality (ESLint)

## 🚨 Common Issues & Solutions

### "Node.js version must be >= 18.17.0"
```bash
# Using nvm
nvm install 18
nvm use 18

# Or update your system Node.js
```

### "pnpm is not installed"
```bash
npm install -g pnpm
```

### "Missing critical dependencies"
```bash
rm -rf node_modules pnpm-lock.yaml
pnpm install
```

### "Environment file missing"
```bash
cp .env.example .env.local
# Edit .env.local with your values
```

### "Database connectivity failed"
- Check DATABASE_URL format
- Verify database server is running
- Test connection manually

### "Discord configuration incomplete"
- Set up Discord application at https://discord.com/developers/applications
- Copy Client ID and Secret
- Configure OAuth redirect URLs

### "Build directory not found"
```bash
pnpm run build
```

## 🔧 Environment Setup Guide

### 1. Discord Application Setup
1. Go to https://discord.com/developers/applications
2. Create new application
3. Copy Application ID → `BOT_CLIENT_ID` and `DISCORD_CLIENT_ID`
4. Go to OAuth2 → General, copy Client Secret → `BOT_CLIENT_SECRET` and `DISCORD_CLIENT_SECRET`
5. Add redirect URLs:
   - Dev: `http://localhost:3000/auth/discord/callback`
   - Prod: `https://your-domain.com/auth/discord/callback`

### 2. Bot Token (Optional)
1. Go to Bot section in Discord Developer Portal
2. Create bot and copy token → `DISCORD_TOKEN`
3. Enable required intents: Server Members, Message Content

### 3. Database Setup
```bash
# Using Neon (recommended)
# 1. Create account at https://neon.tech
# 2. Create new project
# 3. Copy connection string to DATABASE_URL

# Local PostgreSQL
DATABASE_URL=postgresql://username:password@localhost:5432/discord_bot
```

### 4. Security Keys
```bash
# Generate encryption keys
node -e "console.log('USER_ENCRYPTION_KEY=' + require('crypto').randomBytes(32).toString('hex'))"
node -e "console.log('SESSION_ENCRYPTION_KEY=' + require('crypto').randomBytes(32).toString('hex'))"
node -e "console.log('CSRF_ENCRYPTION_KEY=' + require('crypto').randomBytes(32).toString('hex'))"
```

## 🐳 Docker Integration

### Dockerfile Integration
The preflight checks are integrated into the Docker build process:

```dockerfile
# Add to your Dockerfile before the main build
RUN ./scripts/docker-preflight.sh
```

### Docker Compose
```yaml
version: '3.8'
services:
  discord-bot:
    build: .
    environment:
      - NODE_ENV=production
      - PORT=8080
    ports:
      - "8080:8080"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
```

## 🌐 Deployment Platforms

### Sevalla Cloud
```bash
# 1. Run production preflight
pnpm run preflight:prod

# 2. Configure environment variables in Sevalla dashboard
# 3. Deploy using Sevalla CLI or dashboard
```

### Railway/Render/Heroku
```bash
# 1. Set environment variables in platform dashboard
# 2. Add build command: pnpm run build
# 3. Add start command: pnpm run start:prod
# 4. Configure health check: /api/health
```

### VPS/Self-hosted
```bash
# 1. Clone repository
# 2. Run setup: pnpm run setup:prod
# 3. Configure systemd service (see scripts/discord-bot.service)
# 4. Start: systemctl start discord-bot
```

## 📊 Monitoring Integration

The preflight system integrates with:
- **Prometheus metrics** - Application health and performance
- **Winston logging** - Structured logging with levels
- **Health checks** - Kubernetes/Docker health endpoints
- **Error tracking** - Comprehensive error reporting

## 🔄 CI/CD Integration

### GitHub Actions
```yaml
name: Preflight Check
on: [push, pull_request]
jobs:
  preflight:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm install -g pnpm
      - run: pnpm install
      - run: pnpm run preflight:prod --skip-db
```

### GitLab CI
```yaml
preflight:
  stage: test
  script:
    - npm install -g pnpm
    - pnpm install
    - pnpm run preflight:prod --skip-db
```

## 🎛️ Advanced Configuration

### Custom Preflight Rules
Edit `preflight.config.json` to customize:
- Environment-specific requirements
- Security validation thresholds
- Database connection timeouts
- Build validation rules

### Selective Checks
```bash
# Skip specific checks
./scripts/preflight.sh --skip-build --skip-db

# Environment-specific
./scripts/preflight.sh --environment=prod

# Verbose output
./scripts/preflight.sh --verbose

# JSON output for automation
node scripts/preflight.js --json --env=prod
```

## 📞 Support

If preflight checks fail:

1. **Run verbose mode**: `./scripts/preflight.sh --verbose`
2. **Check specific errors** in the output
3. **Review PREFLIGHT_GUIDE.md** for detailed troubleshooting
4. **Check recent changes** that might have broken configuration
5. **Verify environment variables** with `pnpm run check:env`

## 🎉 Success Indicators

When preflight passes, you'll see:
- ✅ All critical checks passed
- 🎉 Ready for deployment message
- 📋 Next steps for your environment
- 🔗 Relevant commands to start the application

The preflight system ensures your Discord bot deployment is reliable, secure, and follows best practices across all environments.
