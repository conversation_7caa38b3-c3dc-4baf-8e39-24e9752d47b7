# Discord Bot EnergeX - Preflight Guide

This guide covers the comprehensive preflight checks and setup procedures for the Discord Bot EnergeX project.

## 🚀 Quick Start

### Development Environment
```bash
# Quick setup for development
pnpm run setup:dev

# Or run preflight manually
pnpm run preflight:dev
```

### Production Environment
```bash
# Quick setup for production
pnpm run setup:prod

# Or run preflight manually
pnpm run preflight:prod
```

## 📋 Available Preflight Commands

| Command | Description | Use Case |
|---------|-------------|----------|
| `pnpm run preflight` | Full preflight check (development mode) | Before starting development |
| `pnpm run preflight:dev` | Development environment check | Local development setup |
| `pnpm run preflight:prod` | Production environment check | Before production deployment |
| `pnpm run preflight:quick` | Quick check (skip build & DB) | Fast validation |
| `pnpm run preflight:bash` | Bash version of preflight | Alternative implementation |
| `pnpm run check:env` | Environment variables only | Environment troubleshooting |

## 🔧 Preflight Check Categories

### 1. System Requirements
- ✅ Node.js version >= 18.17.0
- ✅ pnpm package manager installed
- ✅ System resources (memory, disk space)

### 2. File Structure
- ✅ Required configuration files (package.json, tsconfig.json, etc.)
- ✅ Source code directories (src/, migrations/, scripts/)
- ✅ Essential application files (main.ts, app.module.ts)

### 3. Dependencies
- ✅ node_modules directory exists
- ✅ Critical dependencies installed (@nestjs/core, discord.js, etc.)
- ✅ Package lock file synchronization

### 4. Environment Variables

#### Critical (Development)
- `DATABASE_URL` - PostgreSQL connection string
- `BOT_CLIENT_ID` - Discord application client ID
- `BOT_CLIENT_SECRET` - Discord application client secret
- `DISCORD_CLIENT_ID` - Discord OAuth client ID
- `DISCORD_CLIENT_SECRET` - Discord OAuth client secret

#### Critical (Production)
All development variables plus:
- `NODE_ENV=production`
- `PORT` - Application port (usually 8080)
- `USER_ENCRYPTION_KEY` - 64-character hex key
- `SESSION_ENCRYPTION_KEY` - 64-character hex key
- `CSRF_ENCRYPTION_KEY` - 64-character hex key
- `WEB_URL` - Frontend application URL

#### Optional
- `DISCORD_TOKEN` - Bot token (enables bot features)
- `ANTHROPIC_API_KEY` - AI features
- `GUILD_ID` - Development server ID
- `REDIS_URL` - Caching and sessions

### 5. Discord Configuration
- ✅ OAuth application setup
- ✅ Bot token validation (if provided)
- ✅ Required permissions and intents

### 6. Database Connectivity
- ✅ PostgreSQL connection test
- ✅ Basic query execution
- ✅ Schema validation (if migrations exist)

### 7. Build Validation
- ✅ TypeScript compilation
- ✅ Build output exists (dist/ directory)
- ✅ Main application entry point

### 8. Security Configuration
- ✅ Encryption keys present and valid length
- ✅ Security headers configuration
- ✅ CORS settings

## 🛠️ Setup Instructions

### 1. Initial Setup

1. **Clone and Install Dependencies**
   ```bash
   git clone <repository>
   cd Discordbot-EnergeX
   pnpm install
   ```

2. **Environment Configuration**
   ```bash
   cp .env.example .env.local
   # Edit .env.local with your actual values
   ```

3. **Generate Security Keys**
   ```bash
   # Generate encryption keys
   node -e "console.log('USER_ENCRYPTION_KEY=' + require('crypto').randomBytes(32).toString('hex'))"
   node -e "console.log('SESSION_ENCRYPTION_KEY=' + require('crypto').randomBytes(32).toString('hex'))"
   node -e "console.log('CSRF_ENCRYPTION_KEY=' + require('crypto').randomBytes(32).toString('hex'))"
   ```

### 2. Discord Application Setup

1. **Create Discord Application**
   - Go to https://discord.com/developers/applications
   - Create new application
   - Note the Application ID (use as BOT_CLIENT_ID and DISCORD_CLIENT_ID)

2. **Configure OAuth2**
   - Go to OAuth2 → General
   - Copy Client Secret (use as BOT_CLIENT_SECRET and DISCORD_CLIENT_SECRET)
   - Add redirect URLs:
     - Development: `http://localhost:3000/auth/discord/callback`
     - Production: `https://your-domain.com/auth/discord/callback`

3. **Create Bot (Optional)**
   - Go to Bot section
   - Create bot and copy token (use as DISCORD_TOKEN)
   - Enable required intents: Server Members, Message Content

### 3. Database Setup

1. **Neon Database (Recommended)**
   ```bash
   # Create account at https://neon.tech
   # Create new project
   # Copy connection string to DATABASE_URL
   ```

2. **Run Migrations**
   ```bash
   pnpm run db:push
   # Or if using migration files:
   pnpm run db:migrate
   ```

### 4. Validation

```bash
# Run full preflight check
pnpm run preflight:dev

# Check specific components
pnpm run check:env
./scripts/preflight.sh --verbose
```

## 🚨 Troubleshooting

### Common Issues

1. **"Node.js version must be >= 18.17.0"**
   ```bash
   # Update Node.js using nvm
   nvm install 18
   nvm use 18
   ```

2. **"pnpm is not installed"**
   ```bash
   npm install -g pnpm
   ```

3. **"Missing critical dependencies"**
   ```bash
   rm -rf node_modules pnpm-lock.yaml
   pnpm install
   ```

4. **"Database connectivity failed"**
   - Check DATABASE_URL format
   - Verify database server is running
   - Check network connectivity and firewall

5. **"Discord token validation failed"**
   - Verify token format (no extra spaces/characters)
   - Check bot permissions in Discord Developer Portal
   - Ensure bot is added to test server

6. **"TypeScript compilation errors"**
   ```bash
   # Check for errors
   npx tsc --noEmit
   
   # Fix common issues
   pnpm run lint:fix
   ```

7. **"Build directory not found"**
   ```bash
   pnpm run build
   ```

### Environment-Specific Issues

#### Development
- Set `NODE_ENV=development`
- Use `PORT=3000` for frontend compatibility
- `GUILD_ID` speeds up command deployment

#### Production
- Set `NODE_ENV=production`
- Use `PORT=8080` for container compatibility
- All security keys must be set
- HTTPS URLs required for WEB_URL

## 📊 Preflight Options

### Command Line Options

```bash
# Environment selection
./scripts/preflight.sh --environment=dev
./scripts/preflight.sh --environment=prod

# Skip specific checks
./scripts/preflight.sh --skip-build
./scripts/preflight.sh --skip-db

# Verbose output
./scripts/preflight.sh --verbose

# Node.js version with JSON output
node scripts/preflight.js --json --env=prod
```

### Configuration File

The `preflight.config.json` file contains detailed configuration for:
- Environment-specific requirements
- System requirements and thresholds
- Security validation rules
- Database and Discord requirements
- Deployment target specifications

## 🔄 Integration with CI/CD

### GitHub Actions Example
```yaml
- name: Run Preflight Checks
  run: |
    pnpm install
    pnpm run preflight:prod
```

### Docker Build Integration
```dockerfile
# Add to Dockerfile before main build
RUN pnpm run preflight:prod --skip-db
```

### Sevalla Deployment
```bash
# In your deployment script
pnpm run preflight:prod
pnpm run start:prod:managed
```

## 📈 Performance Considerations

- Preflight checks typically complete in 10-30 seconds
- Database connectivity check may take longer with remote databases
- Build validation can be skipped for faster checks during development
- Use `--skip-db` for offline development

## 🔐 Security Notes

- Never commit `.env` files to version control
- Use strong, unique encryption keys (64 characters)
- Regularly rotate API keys and tokens
- Enable HTTPS in production environments
- Review security warnings from preflight checks

## 📞 Support

If preflight checks fail and you can't resolve the issues:

1. Run with verbose output: `./scripts/preflight.sh --verbose`
2. Check the specific error messages
3. Review this troubleshooting guide
4. Check project documentation in `/docs` directory
5. Review recent changes that might have broken the configuration

## 🎯 Next Steps After Successful Preflight

### Development
```bash
pnpm run dev          # Start with hot reload
pnpm run start:debug  # Start with debugging
```

### Production
```bash
pnpm run start:prod           # Direct production start
pnpm run start:prod:managed   # Managed production start with monitoring
docker build -t discord-bot . # Docker deployment
```
