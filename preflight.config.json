{"name": "Discord Bot EnergeX Preflight Configuration", "version": "1.0.0", "description": "Comprehensive preflight checks for Discord bot deployment and development", "environments": {"development": {"name": "Development", "description": "Local development environment with relaxed requirements", "critical_env_vars": ["DATABASE_URL", "BOT_CLIENT_ID", "BOT_CLIENT_SECRET", "DISCORD_CLIENT_ID", "DISCORD_CLIENT_SECRET"], "optional_env_vars": ["DISCORD_TOKEN", "ANTHROPIC_API_KEY", "NODE_ENV", "PORT", "GUILD_ID", "USER_ENCRYPTION_KEY", "SESSION_ENCRYPTION_KEY", "CSRF_ENCRYPTION_KEY"], "port": 3000, "build_required": false, "strict_validation": false}, "production": {"name": "Production", "description": "Production environment with strict security requirements", "critical_env_vars": ["DATABASE_URL", "NODE_ENV", "PORT", "BOT_CLIENT_ID", "BOT_CLIENT_SECRET", "DISCORD_CLIENT_ID", "DISCORD_CLIENT_SECRET", "USER_ENCRYPTION_KEY", "SESSION_ENCRYPTION_KEY", "CSRF_ENCRYPTION_KEY", "WEB_URL"], "optional_env_vars": ["DISCORD_TOKEN", "ANTHROPIC_API_KEY", "APP_URL", "NEXT_PUBLIC_API_ENDPOINT", "INTERNAL_API_ENDPOINT", "USE_MASTRA", "DEFAULT_AI_CHANNEL", "ENABLE_ENV_LOGIN", "SESSION_ISOLATION_ENABLED", "SESSION_FINGERPRINTING_ENABLED", "AUTOMATIC_TOKEN_ROTATION"], "port": 8080, "build_required": true, "strict_validation": true}}, "system_requirements": {"node_version": ">=18.17.0", "package_manager": "pnpm", "pnpm_version": ">=8.0.0", "memory_mb": 512, "disk_space_mb": 1024}, "required_files": ["package.json", "tsconfig.json", "nest-cli.json", "src/main.ts", "src/app.module.ts", "drizzle.config.ts"], "required_directories": ["src", "src/core", "src/discord", "src/api", "src/features", "migrations", "scripts"], "critical_dependencies": ["@nestjs/core", "@nestjs/common", "discord.js", "necord", "drizzle-orm", "pg", "class-validator", "class-transformer"], "security_requirements": {"encryption_key_length": 32, "required_security_headers": ["helmet", "cors"], "session_security": true, "csrf_protection": true}, "database_requirements": {"type": "postgresql", "ssl_required": true, "connection_pool": true, "migration_support": true, "required_extensions": []}, "discord_requirements": {"oauth_required": true, "bot_token_optional": true, "required_intents": ["Guilds", "GuildMembers", "GuildMessages", "MessageContent"], "required_permissions": ["SendMessages", "ReadMessageHistory", "UseSlashCommands"]}, "build_requirements": {"typescript_compilation": true, "eslint_validation": false, "test_execution": false, "bundle_optimization": true}, "deployment_targets": {"sevalla": {"name": "Sevalla Cloud Platform", "backend_url": "https://discordbot-energex-backend-nqzv2.sevalla.app", "frontend_url": "https://discordbot-energex-jkhvk.sevalla.app", "health_check_endpoint": "/api/health", "ready_check_endpoint": "/api/health/ready", "environment_variables_required": true, "docker_support": true}, "docker": {"name": "Docker Container", "dockerfile_required": true, "multi_stage_build": true, "health_check": true, "non_root_user": true, "port_exposure": 8080}, "local": {"name": "Local Development", "hot_reload": true, "debug_mode": true, "file_watching": true, "port": 3000}}, "health_checks": {"startup_timeout": 60, "health_endpoint_timeout": 30, "database_connection_timeout": 10, "discord_connection_timeout": 15}, "monitoring": {"prometheus_metrics": true, "winston_logging": true, "error_tracking": true, "performance_monitoring": false}}