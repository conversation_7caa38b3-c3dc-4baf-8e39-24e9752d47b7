{"name": "Discord Bot EnergeX Build Configuration", "version": "1.0.0", "description": "Advanced build configuration for preflight build system", "targets": {"development": {"name": "Development Build", "description": "Fast build for local development with debugging support", "webpack": false, "sourceMap": true, "minification": false, "optimization": false, "removeComments": false, "bundleAnalyzer": false, "outputPath": "dist", "target": "ES2020", "moduleResolution": "node", "allowJs": true, "checkJs": false, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true}, "production": {"name": "Production Build", "description": "Optimized build for production deployment", "webpack": true, "sourceMap": false, "minification": true, "optimization": true, "removeComments": true, "bundleAnalyzer": true, "outputPath": "dist", "target": "ES2020", "moduleResolution": "node", "allowJs": false, "checkJs": false, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "treeshaking": true, "deadCodeElimination": true}, "docker": {"name": "Docker Build", "description": "Container-optimized build with minimal footprint", "webpack": true, "sourceMap": false, "minification": true, "optimization": true, "removeComments": true, "bundleAnalyzer": false, "outputPath": "dist", "target": "ES2020", "moduleResolution": "node", "allowJs": false, "checkJs": false, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "treeshaking": true, "deadCodeElimination": true, "containerOptimization": true}}, "validation": {"typescript": {"enabled": true, "strict": true, "noEmitOnError": false, "skipLibCheck": true, "checkUnusedLocals": false, "checkUnusedParameters": false}, "eslint": {"enabled": true, "autoFix": true, "failOnError": false, "failOnWarning": false, "extensions": [".ts", ".js"], "ignorePatterns": ["dist/", "node_modules/", "*.d.ts"]}, "prettier": {"enabled": true, "autoFix": true, "checkFormatting": true}, "dependencies": {"checkOutdated": false, "checkVulnerabilities": true, "allowedLicenses": ["MIT", "Apache-2.0", "BSD-3-<PERSON><PERSON>", "ISC"], "criticalDependencies": ["@nestjs/core", "@nestjs/common", "discord.js", "necord", "drizzle-orm", "pg"]}}, "optimization": {"bundleSize": {"maxSizeMB": 100, "warnSizeMB": 50, "trackLargeFiles": true, "largeFileThresholdMB": 1}, "performance": {"maxBuildTimeSeconds": 300, "warnBuildTimeSeconds": 120, "trackBuildMetrics": true}, "caching": {"enabled": true, "incrementalBuild": true, "dependencyCache": true, "buildCache": true}}, "testing": {"preBuild": {"enabled": true, "runLinting": true, "runTypeCheck": true, "runUnitTests": false}, "postBuild": {"enabled": true, "validateArtifacts": true, "testExecution": true, "runIntegrationTests": false, "performanceTests": false}}, "deployment": {"artifacts": {"include": ["dist/**/*", "package.json", "pnpm-lock.yaml", "scripts/production-start.sh"], "exclude": ["**/*.map", "**/*.test.js", "**/*.spec.js", "**/test/**", "**/.DS_Store"]}, "docker": {"baseImage": "node:18-alpine", "workdir": "/app", "user": "<PERSON><PERSON><PERSON>", "healthCheck": {"enabled": true, "endpoint": "/api/health", "interval": "60s", "timeout": "30s", "retries": 5}, "multiStage": true, "layers": {"dependencies": true, "source": true, "build": true, "runtime": true}}, "platforms": {"sevalla": {"name": "Sevalla Cloud", "buildCommand": "pnpm run build:pipeline:prod", "startCommand": "pnpm run start:prod:managed", "healthCheck": "/api/health", "environmentVariables": "required"}, "railway": {"name": "Railway", "buildCommand": "pnpm run build", "startCommand": "pnpm run start:prod", "healthCheck": "/api/health"}, "render": {"name": "Render", "buildCommand": "pnpm install && pnpm run build", "startCommand": "pnpm run start:prod", "healthCheck": "/api/health"}}}, "monitoring": {"buildMetrics": {"enabled": true, "trackDuration": true, "trackSize": true, "trackErrors": true, "exportFormat": "json"}, "alerts": {"buildFailure": true, "buildSizeIncrease": true, "buildTimeIncrease": true, "securityIssues": true}}, "hooks": {"preBuild": ["node scripts/quick-preflight.js"], "postBuild": ["node scripts/build-validator.js"], "preTest": [], "postTest": [], "preDeploy": ["node scripts/preflight.js --env=prod"], "postDeploy": []}}