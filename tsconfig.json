{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2020", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "strict": false, "exactOptionalPropertyTypes": false, "noImplicitReturns": false, "noUncheckedIndexedAccess": false, "ignoreDeprecations": "5.0", "noEmitOnError": false, "paths": {"@/*": ["src/*"], "@/core/*": ["src/core/*"], "@/discord/*": ["src/discord/*"], "@/api/*": ["src/api/*"], "@/agents/*": ["src/agents/*"], "@/features/*": ["src/features/*"], "@/common/*": ["src/common/*"]}}, "include": ["src/**/*", "test/**/*"], "exclude": ["node_modules", "dist", "**/*.spec.ts", "**/*.test.ts"]}