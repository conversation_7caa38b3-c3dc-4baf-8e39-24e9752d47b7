/**
 * Example usage of the type-safe health monitoring system
 * This demonstrates how to use the comprehensive monitoring types and interfaces
 */

import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    AlertStatus,
    AlertThresholds,
    AlertType,
    DependencyStatus,
    DependencyType,
    HealthCheckResult,
    HealthStatus,
    MonitoringData,
    NotificationChannel,
    PerformanceMetrics,
    SystemHealthResponse
} from '../types/monitoring.types';

// Example: Creating a comprehensive health status response
export function createExampleHealthResponse(): SystemHealthResponse {
  const now = new Date();
  
  const databaseCheck: HealthCheckResult = {
    name: 'database',
    status: HealthStatus.HEALTHY,
    responseTime: 45,
    message: 'Database connection successful',
    timestamp: now,
    details: {
      query: 'SELECT 1',
      connectionPool: 'active',
      metadata: {
        version: '14.1',
        component: 'postgresql',
        checkType: 'connectivity'
      }
    }
  };

  const redisCheck: HealthCheckResult = {
    name: 'redis',
    status: HealthStatus.HEALTHY,
    responseTime: 12,
    message: 'Redis connection successful',
    timestamp: now,
    details: {
      command: 'PING',
      result: 'PONG',
      metadata: {
        version: '7.0.0',
        component: 'redis-cache',
        checkType: 'connectivity'
      }
    }
  };

  const memoryCheck: HealthCheckResult = {
    name: 'memory',
    status: HealthStatus.DEGRADED,
    responseTime: 0,
    message: 'Memory usage: 156.23 MB (78.5%)',
    timestamp: now,
    details: {
      heapUsed: '156.23 MB',
      heapTotal: '199.12 MB',
      percentage: 78.5,
      thresholds: {
        memory: { warning: 75, critical: 90 },
        cpu: { warning: 70, critical: 85 },
        responseTime: { warning: 1000, critical: 5000 },
        disk: { warning: 80, critical: 95 },
        throughput: { minWarning: 100, minCritical: 50 },
        errorRate: { warning: 5, critical: 10, window: 300 }
      }
    }
  };

  const diskCheck: HealthCheckResult = {
    name: 'disk',
    status: HealthStatus.HEALTHY,
    responseTime: 0,
    message: 'Disk usage: 45.2%',
    timestamp: now,
    details: {
      totalSpace: '100 GB',
      usedSpace: '45.2 GB',
      freeSpace: '54.8 GB',
      percentage: 45.2
    }
  };

  const healthResponse: SystemHealthResponse = {
    status: HealthStatus.DEGRADED, // Overall status based on worst individual check
    timestamp: now,
    services: [
      {
        name: 'discord-bot',
        status: HealthStatus.HEALTHY,
        responseTime: 50,
        uptime: 3600.5,
        lastCheck: now
      },
      {
        name: 'database',
        status: HealthStatus.DEGRADED,
        responseTime: 150,
        uptime: 3600.5,
        lastCheck: now,
        issues: ['High response time']
      }
    ],
    alerts: [],
    checks: {
      database: databaseCheck,
      redis: redisCheck,
      discord: { name: 'discord', status: HealthStatus.HEALTHY, responseTime: 50, message: 'Discord API healthy', timestamp: now },
      external: { name: 'external', status: HealthStatus.HEALTHY, responseTime: 100, message: 'External services healthy', timestamp: now },
      memory: memoryCheck,
      disk: diskCheck,
    },
    uptime: 3600.5, // 1 hour uptime
    metrics: createExampleSystemMetrics()
  };

  return healthResponse;
}

// Example: Creating system metrics
export function createExampleSystemMetrics(): PerformanceMetrics {
  const metrics: PerformanceMetrics = {
    timestamp: new Date(),
    uptime: 3600.5,
    memory: {
      rss: 163840000,     // 156.25 MB
      heapTotal: 209715200, // 200 MB
      heapUsed: 163840000,  // 156.25 MB
      external: 1048576,    // 1 MB
      arrayBuffers: 524288, // 0.5 MB
      percentage: 78.1,
    },
    cpu: {
      user: 1234567,
      system: 456789,
      percentage: 23.5,
      loadAverage: [1.5, 1.8, 2.1],
    },
    process: {
      pid: 12345,
      version: '18.17.0',
      platform: 'linux',
      arch: 'x64',
      nodeVersion: 'v18.17.0',
      uptime: 3600.5,
      threads: 8,
      handles: 256,
      fileDescriptors: 45
    }
  };

  return metrics;
}

// Example: Creating alerts
export function createExampleAlerts(): Alert[] {
  const now = new Date();
  
  const memoryAlert: Alert = {
    id: 'alert_memory_1703875200000',
    type: AlertType.MEMORY_HIGH,
    severity: AlertSeverity.HIGH,
    status: AlertStatus.ACTIVE,
    title: 'High Memory Usage',
    message: 'Memory usage has exceeded 75% threshold (current: 78.5%)',
    details: {
      service: 'discord-bot',
      component: 'nodejs-process',
      metric: 'memory_usage_percentage',
      currentValue: 78.5,
      thresholdValue: 75,
      trend: 'increasing',
      impact: {
        severity: 'high',
        affectedUsers: 0,
        businessImpact: 'May affect response times'
      },
      recommendation: 'Consider scaling up memory or optimizing memory usage',
      runbookUrl: 'https://docs.company.com/runbooks/memory-alerts',
      environment: 'production',
      region: 'us-east-1'
    },
    source: {
      service: 'system-health-service',
      host: 'app-server-01',
      environment: 'production',
      region: 'us-east-1'
    },
    timestamp: now,
    acknowledged: false,
    escalated: false,
    firstOccurrence: new Date(now.getTime() - 300000), // 5 minutes ago
    lastOccurrence: now,
    occurrenceCount: 3,
    notificationsSent: [
      {
        channel: NotificationChannel.EMAIL,
        recipient: '<EMAIL>',
        sentAt: new Date(now.getTime() - 60000), // 1 minute ago
        status: 'delivered'
      }
    ]
  };

  return [memoryAlert];
}

// Example: Creating comprehensive monitoring data
export function createExampleMonitoringData(): MonitoringData {
  const healthChecks = [
    createExampleHealthResponse().checks.database,
    createExampleHealthResponse().checks.redis,
    createExampleHealthResponse().checks.memory,
    createExampleHealthResponse().checks.disk
  ];

  const dependencies: DependencyStatus[] = [
    {
      name: 'postgresql',
      type: DependencyType.DATABASE,
      status: HealthStatus.HEALTHY,
      responseTime: 45,
      lastCheck: new Date(),
      version: '14.1',
      endpoint: 'postgresql://localhost:5432/botdb'
    },
    {
      name: 'redis',
      type: DependencyType.CACHE,
      status: HealthStatus.HEALTHY,
      responseTime: 12,
      lastCheck: new Date(),
      version: '7.0.0',
      endpoint: 'redis://localhost:6379'
    },
    {
      name: 'discord-api',
      type: DependencyType.EXTERNAL_API,
      status: HealthStatus.HEALTHY,
      responseTime: 156,
      lastCheck: new Date(),
      endpoint: 'https://discord.com/api/v10'
    }
  ];

  const monitoringData: MonitoringData = {
    service: 'discord-bot-energex',
    instance: 'app-server-01',
    environment: 'production',
    status: HealthStatus.DEGRADED,
    timestamp: new Date(),
    healthChecks,
    metrics: createExampleSystemMetrics(),
    alerts: createExampleAlerts(),
    uptime: 3600.5,
    version: '1.2.3',
    dependencies,
    configuration: {
      checkInterval: 30, // 30 seconds
      alertingEnabled: true,
      thresholds: {
        memory: { warning: 75, critical: 90 },
        cpu: { warning: 70, critical: 85 },
        responseTime: { warning: 1000, critical: 5000 },
        disk: { warning: 80, critical: 95 },
        throughput: { minWarning: 10, minCritical: 5 },
        errorRate: { warning: 1, critical: 5, window: 300 }
      },
      retentionPeriod: 30, // 30 days
      enabledChecks: ['database', 'redis', 'memory', 'disk', 'external-services'],
      disabledAlerts: [],
      notificationChannels: [NotificationChannel.EMAIL, NotificationChannel.SLACK],
      escalationRules: [
        {
          alertTypes: [AlertType.MEMORY_CRITICAL, AlertType.DATABASE_CONNECTION_FAILED],
          severities: [AlertSeverity.CRITICAL],
          delay: 5, // 5 minutes
          recipients: [
            {
              type: 'team',
              identifier: 'platform-team',
              channels: [NotificationChannel.EMAIL, NotificationChannel.SLACK]
            }
          ]
        }
      ]
    }
  };

  return monitoringData;
}

// Example: Alert threshold configuration
export function createExampleAlertThresholds(): AlertThresholds {
  const thresholds: AlertThresholds = {
    memory: {
      warning: 75,
      critical: 90,
      duration: 60 // Alert after 1 minute
    },
    cpu: {
      warning: 70,
      critical: 85,
      duration: 120 // Alert after 2 minutes
    },
    disk: {
      warning: 80,
      critical: 95,
      duration: 300 // Alert after 5 minutes
    },
    responseTime: {
      warning: 1000,   // 1 second
      critical: 5000,  // 5 seconds
      p95Warning: 2000,
      p95Critical: 10000
    },
    throughput: {
      minWarning: 10,    // 10 RPS minimum
      minCritical: 5     // 5 RPS minimum
    },
    errorRate: {
      warning: 1,        // 1% error rate
      critical: 5,       // 5% error rate
      window: 300        // 5-minute window
    }
  };

  return thresholds;
}

// Example utility functions
export class HealthMonitoringExamples {
  
  /**
   * Calculate overall health score from individual checks
   */
  static calculateHealthScore(checks: HealthCheckResult[]): number {
    if (checks.length === 0) return 0;
    
    const scoreMap = {
      [HealthStatus.HEALTHY]: 100,
      [HealthStatus.DEGRADED]: 75,
      [HealthStatus.CRITICAL]: 25,
      [HealthStatus.UNHEALTHY]: 0
    };
    
    const totalScore = checks.reduce((sum, check) => {
      return sum + scoreMap[check.status];
    }, 0);
    
    return Math.round(totalScore / checks.length);
  }
  
  /**
   * Determine if system needs immediate attention
   */
  static requiresImmediateAttention(monitoringData: MonitoringData): boolean {
    // Check for critical alerts
    const criticalAlerts = monitoringData.alerts.filter(
      alert => alert.severity === AlertSeverity.CRITICAL && alert.status === 'active'
    );
    
    if (criticalAlerts.length > 0) {
      return true;
    }
    
    // Check for multiple unhealthy services
    const unhealthyChecks = monitoringData.healthChecks.filter(
      check => check.status === HealthStatus.UNHEALTHY
    );
    
    return unhealthyChecks.length >= 2;
  }
  
  /**
   * Get summary statistics for monitoring dashboard
   */
  static getSummaryStats(monitoringData: MonitoringData) {
    const totalChecks = monitoringData.healthChecks.length;
    const healthyChecks = monitoringData.healthChecks.filter(
      check => check.status === HealthStatus.HEALTHY
    ).length;
    const degradedChecks = monitoringData.healthChecks.filter(
      check => check.status === HealthStatus.DEGRADED
    ).length;
    const unhealthyChecks = monitoringData.healthChecks.filter(
      check => check.status === HealthStatus.UNHEALTHY
    ).length;
    
    const activeAlerts = monitoringData.alerts.filter(
      alert => alert.status === 'active'
    ).length;
    
    const criticalAlerts = monitoringData.alerts.filter(
      alert => alert.severity === AlertSeverity.CRITICAL && alert.status === 'active'
    ).length;
    
    return {
      totalChecks,
      healthyChecks,
      degradedChecks,
      unhealthyChecks,
      activeAlerts,
      criticalAlerts,
      overallHealthScore: this.calculateHealthScore(monitoringData.healthChecks),
      availability: ((healthyChecks + degradedChecks) / totalChecks) * 100
    };
  }
}