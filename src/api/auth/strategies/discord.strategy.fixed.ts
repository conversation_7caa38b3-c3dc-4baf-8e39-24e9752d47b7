import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PassportStrategy } from '@nestjs/passport';
import { Profile, Strategy } from 'passport-discord';
import { DiscordOAuthProfile } from '../types/auth.types';

@Injectable()
export class DiscordStrategy extends PassportStrategy(Strategy, 'discord') {
  private readonly logger = new Logger(DiscordStrategy.name);

  constructor(private readonly configService: ConfigService) {
    super({
      clientID: configService.get<string>('DISCORD_CLIENT_ID') || configService.get<string>('BOT_CLIENT_ID'),
      clientSecret: configService.get<string>('DISCORD_CLIENT_SECRET') || configService.get<string>('BOT_CLIENT_SECRET'),
      callbackURL: '/api/auth/callback',
      scope: ['identify', 'guilds', 'email'],
      state: true,
      passReqToCallback: false
    });
    
    this.logger.log('Discord OAuth strategy initialized');
  }

  async validate(
    accessToken: string,
    refreshToken: string,
    profile: Profile
  ): Promise<DiscordOAuthProfile> {
    try {
      this.logger.debug(`Validating Discord user: ${profile.username}#${profile.discriminator}`);
      
      const discordProfile: DiscordOAuthProfile = {
        id: profile.id,
        username: profile.username,
        discriminator: profile.discriminator,
        avatar: profile.avatar || '',
        email: profile.email || undefined,
        verified: profile.verified || false,
        locale: profile.locale || 'en-US',
        flags: profile.flags || 0,
        premium_type: profile.premium_type || undefined,
        public_flags: profile.public_flags || 0
      };

      // Additional validation
      if (!discordProfile.id || !discordProfile.username) {
        this.logger.error('Invalid Discord profile received');
        throw new Error('Invalid Discord profile');
      }

      this.logger.log(`Discord user validated: ${profile.username}#${profile.discriminator}`);
      return discordProfile;
    } catch (error) {
      this.logger.error('Discord OAuth validation failed:', error);
      throw error;
    }
  }
}