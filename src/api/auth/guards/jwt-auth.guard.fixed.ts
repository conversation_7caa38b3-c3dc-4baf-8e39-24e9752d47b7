import {
    CanActivate,
    ExecutionContext,
    Injectable,
    Logger,
    SetMetadata,
    UnauthorizedException
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { AuthGuard } from '@nestjs/passport';
import { AuthService } from '../auth.service.fixed';
import {
    AUTH_CONSTANTS,
    AuthUser,
    hasPermission,
    isDiscordToken,
    OrganizationRole,
    Permission
} from '../types/auth.types';
import {
    EnhancedRequest
} from '../types/security.types';

// Decorators for permission-based access control
export const IS_PUBLIC_KEY = 'isPublic';
export const Public = () => SetMetadata(IS_PUBLIC_KEY, true);

export const PERMISSIONS_KEY = 'permissions';
export const Permissions = (...permissions: Permission[]) => 
  SetMetadata(PERMISSIONS_KEY, permissions);

export const ROLES_KEY = 'roles';
export const Roles = (...roles: string[]) => 
  SetMetadata(ROLES_KEY, roles);

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') implements CanActivate {
  private readonly logger = new Logger(JwtAuthGuard.name);

  constructor(
    private readonly reflector: Reflector,
    private readonly authService: AuthService
  ) {
    super();
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    // Check if endpoint is marked as public
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isPublic) {
      return true;
    }

    const request = context.switchToHttp().getRequest<EnhancedRequest>();
    
    try {
      this.logger.debug('Starting authentication check...');
      
      // Attempt authentication
      const authResult = await this.authenticateRequest(request);
      
      if (!authResult) {
        return false;
      }

      // Create security context
      const securityContext = await this.authService.createSecurityContext(
        request,
        request.user as AuthUser,
        undefined
      );

      // Attach security context to request (using any to bypass readonly)
      (request as any).securityContext = securityContext;

      // Check permissions and roles
      const hasRequiredAccess = await this.checkAccessControl(context, request.user as AuthUser);
      
      if (!hasRequiredAccess) {
        this.logger.warn(`Access denied for user ${(request.user as AuthUser)?.username}: insufficient permissions`);
        throw new UnauthorizedException('Insufficient permissions');
      }

      // Log successful authentication
      this.logger.debug(`Authentication successful for user: ${(request.user as AuthUser)?.username}`);
      
      return true;

    } catch (error) {
      this.logger.error('Authentication failed:', error);
      
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      
      throw new UnauthorizedException('Authentication failed');
    }
  }

  private async authenticateRequest(request: EnhancedRequest): Promise<boolean> {
    const authHeader = request.get(AUTH_CONSTANTS.HEADER_NAMES.AUTHORIZATION);
    
    if (!authHeader?.startsWith(`${AUTH_CONSTANTS.TOKEN_TYPES.BEARER} `)) {
      // Try cookie-based authentication as fallback
      return this.authenticateWithCookies(request);
    }

    const token = authHeader.replace(`${AUTH_CONSTANTS.TOKEN_TYPES.BEARER} `, '');
    
    // Validate token format and length
    if (!token || token.length < 10) {
      this.logger.warn('Invalid token format received');
      return false;
    }

    this.logger.debug(`Token received - Length: ${token.length}, Type: ${this.getTokenType(token)}`);

    // Handle Discord tokens
    if (isDiscordToken(token)) {
      return this.authenticateWithDiscordToken(request, token);
    }

    // Handle JWT tokens
    return this.authenticateWithJWT(request, token);
  }

  private async authenticateWithDiscordToken(request: EnhancedRequest, token: string): Promise<boolean> {
    try {
      this.logger.debug('Validating Discord token...');
      
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);

      const response = await fetch('https://discord.com/api/v10/users/@me', {
        headers: {
          [AUTH_CONSTANTS.HEADER_NAMES.AUTHORIZATION]: `${AUTH_CONSTANTS.TOKEN_TYPES.BEARER} ${token}`
        },
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        this.logger.warn(`Discord API validation failed: ${response.status}`);
        return false;
      }

      const discordUser = await response.json();
      
      if (!discordUser.id || !discordUser.username) {
        this.logger.warn('Invalid Discord user data received');
        return false;
      }

      // Get organization information
      const userOrganization = await this.authService.getUserOrganization(discordUser.id);

      // Create enhanced user object
      const authUser: AuthUser = {
        id: discordUser.id,
        discordId: discordUser.id,
        username: discordUser.username,
        discriminator: discordUser.discriminator,
        avatar: discordUser.avatar,
        email: discordUser.email,
        tier: userOrganization?.organization?.tier || 'free',
        organization: userOrganization?.organization || null,
        role: (userOrganization?.role as OrganizationRole) || null,
        permissions: [],
        verified: discordUser.verified || false,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Attach user to request
      request.user = authUser;
      (request as any).session = {
        sessionId: 'discord-session',
        type: 'discord-token',
        expiresAt: new Date(Date.now() + AUTH_CONSTANTS.SESSION.DEFAULT_EXPIRY),
        lastAccessedAt: new Date()
      };

      this.logger.debug(`Discord user authenticated: ${discordUser.username}#${discordUser.discriminator}`);
      return true;

    } catch (error) {
      this.logger.error('Discord token validation failed:', error);
      return false;
    }
  }

  private async authenticateWithJWT(request: EnhancedRequest, token: string): Promise<boolean> {
    try {
      this.logger.debug('Validating JWT token...');
      
      // Use the JWT strategy for validation
      const result = await super.canActivate({ 
        switchToHttp: () => ({ 
          getRequest: () => ({ 
            ...request,
            get: (header: string) => header === 'Authorization' ? `Bearer ${token}` : request.get(header)
          }) 
        })
      } as ExecutionContext) as boolean;

      return result;

    } catch (error) {
      this.logger.debug('JWT validation failed:', error instanceof Error ? error.message : String(error));
      return false;
    }
  }

  private async authenticateWithCookies(request: EnhancedRequest): Promise<boolean> {
    const sessionToken = request.cookies?.[AUTH_CONSTANTS.COOKIE_NAMES.SESSION_TOKEN];
    
    if (!sessionToken) {
      this.logger.debug('No session token found in cookies');
      return false;
    }

    try {
      this.logger.debug('Validating session cookie...');
      
      const session = await this.authService.validateSession(sessionToken, request);
      
      if (!session || !session.user) {
        this.logger.debug('Invalid session token');
        return false;
      }

      // Attach session data to request
      request.user = session.user;
      (request as any).session = session;

      this.logger.debug(`Session authenticated for user: ${session.user.username || session.user.discordId}`);
      return true;

    } catch (error) {
      this.logger.error('Session validation failed:', error);
      return false;
    }
  }

  private async checkAccessControl(context: ExecutionContext, user: AuthUser): Promise<boolean> {
    // Get required permissions from metadata
    const requiredPermissions = this.reflector.getAllAndOverride<Permission[]>(PERMISSIONS_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    // Get required roles from metadata
    const requiredRoles = this.reflector.getAllAndOverride<string[]>(ROLES_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    // If no specific permissions or roles required, allow access
    if (!requiredPermissions?.length && !requiredRoles?.length) {
      return true;
    }

    // Check role-based access
    if (requiredRoles?.length) {
      const userRole = user.role;
      if (!userRole || !requiredRoles.includes(userRole)) {
        this.logger.warn(`User ${user.username} missing required role. Has: ${userRole}, Required: ${requiredRoles.join(', ')}`);
        return false;
      }
    }

    // Check permission-based access
    if (requiredPermissions?.length) {
      for (const permission of requiredPermissions) {
        if (!hasPermission(user.permissions, permission.scope, permission.action, permission.resource)) {
          this.logger.warn(`User ${user.username} missing required permission: ${permission.scope}:${permission.action}`);
          return false;
        }
      }
    }

    return true;
  }

  private getTokenType(token: string): string {
    if (isDiscordToken(token)) {
      return 'Discord';
    }
    if (token.startsWith('eyJ')) {
      return 'JWT';
    }
    return 'Unknown';
  }

  // Handle authentication errors with proper logging
  handleRequest<TUser = any>(err: any, user: any, info: any, context: ExecutionContext): TUser {
    const request = context.switchToHttp().getRequest();
    
    if (err) {
      this.logger.error('Authentication error:', err);
      throw err;
    }

    if (!user) {
      const message = info?.message || 'Authentication failed';
      this.logger.warn(`Authentication failed: ${message}`);
      throw new UnauthorizedException(message);
    }

    // Log successful authentication
    this.logger.debug(`User authenticated: ${user.username || user.id}`);
    
    return user;
  }

  // Rate limiting and security checks
  private async performSecurityChecks(request: EnhancedRequest, user: AuthUser): Promise<void> {
    // Check for suspicious activity
    const suspiciousActivity = await this.authService.detectSuspiciousActivity(request, user);
    
    if (suspiciousActivity) {
      await this.authService.logSecurityEvent('SUSPICIOUS_ACTIVITY', 'warning', {
        userId: user.id,
        ipAddress: request.ip,
        userAgent: request.get('User-Agent')
      });
      
      throw new UnauthorizedException('Suspicious activity detected');
    }

    // Enforce account security policies
    await this.authService.enforceAccountSecurity(user);
  }
}