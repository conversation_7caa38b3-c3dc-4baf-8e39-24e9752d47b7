/**
 * Comprehensive guild event handling types for Discord operations
 * Provides type safety for event handling, webhooks, and real-time updates
 */

import type {
    Snowflake
} from 'discord.js';
import type {
    DiscordInvite,
    EnhancedDiscordChannel,
    EnhancedDiscordGuild,
    EnhancedDiscordMember,
    EnhancedDiscordRole
} from './discord.types';

// Import Discord.js enums with correct names

// ====== GUILD EVENT CORE TYPES ======

/**
 * Base guild event interface
 */
export interface BaseGuildEvent {
  id: string;
  type: GuildEventType;
  guildId: Snowflake;
  timestamp: Date;
  source: GuildEventSource;
  version: number;
  metadata?: Record<string, any>;
}

/**
 * Guild event types
 */
export enum GuildEventType {
  // Guild lifecycle
  GUILD_CREATE = 'guild_create',
  GUILD_UPDATE = 'guild_update',
  GUILD_DELETE = 'guild_delete',
  GUILD_UNAVAILABLE = 'guild_unavailable',
  GUILD_AVAILABLE = 'guild_available',
  
  // Member events
  MEMBER_ADD = 'member_add',
  MEMBER_REMOVE = 'member_remove',
  MEMBER_UPDATE = 'member_update',
  MEMBER_CHUNK = 'member_chunk',
  
  // Role events
  ROLE_CREATE = 'role_create',
  ROLE_UPDATE = 'role_update',
  ROLE_DELETE = 'role_delete',
  
  // Channel events
  CHANNEL_CREATE = 'channel_create',
  CHANNEL_UPDATE = 'channel_update',
  CHANNEL_DELETE = 'channel_delete',
  
  // Permission events
  PERMISSION_UPDATE = 'permission_update',
  OVERWRITE_CREATE = 'overwrite_create',
  OVERWRITE_UPDATE = 'overwrite_update',
  OVERWRITE_DELETE = 'overwrite_delete',
  
  // Moderation events
  BAN_ADD = 'ban_add',
  BAN_REMOVE = 'ban_remove',
  MEMBER_TIMEOUT = 'member_timeout',
  MEMBER_TIMEOUT_REMOVE = 'member_timeout_remove',
  
  // Content events
  EMOJI_CREATE = 'emoji_create',
  EMOJI_UPDATE = 'emoji_update',
  EMOJI_DELETE = 'emoji_delete',
  STICKER_CREATE = 'sticker_create',
  STICKER_UPDATE = 'sticker_update',
  STICKER_DELETE = 'sticker_delete',
  
  // Integration events
  INTEGRATION_CREATE = 'integration_create',
  INTEGRATION_UPDATE = 'integration_update',
  INTEGRATION_DELETE = 'integration_delete',
  
  // Invite events
  INVITE_CREATE = 'invite_create',
  INVITE_DELETE = 'invite_delete',
  
  // Voice events
  VOICE_STATE_UPDATE = 'voice_state_update',
  
  // Scheduled events
  SCHEDULED_EVENT_CREATE = 'scheduled_event_create',
  SCHEDULED_EVENT_UPDATE = 'scheduled_event_update',
  SCHEDULED_EVENT_DELETE = 'scheduled_event_delete',
  
  // Auto moderation events
  AUTO_MOD_RULE_CREATE = 'auto_mod_rule_create',
  AUTO_MOD_RULE_UPDATE = 'auto_mod_rule_update',
  AUTO_MOD_RULE_DELETE = 'auto_mod_rule_delete',
  AUTO_MOD_ACTION = 'auto_mod_action',
  
  // Webhook events
  WEBHOOK_UPDATE = 'webhook_update'
}

/**
 * Guild event sources
 */
export enum GuildEventSource {
  DISCORD_API = 'discord_api',
  GATEWAY = 'gateway',
  WEBHOOK = 'webhook',
  INTERNAL = 'internal',
  AUDIT_LOG = 'audit_log'
}

// ====== SPECIFIC EVENT TYPES ======

/**
 * Guild create event
 */
export interface GuildCreateEvent extends BaseGuildEvent {
  type: GuildEventType.GUILD_CREATE;
  data: {
    guild: EnhancedDiscordGuild;
    isNew: boolean; // True if bot just joined, false if coming back online
    memberCount: number;
    large: boolean;
    unavailable: boolean;
  };
}

/**
 * Guild update event
 */
export interface GuildUpdateEvent extends BaseGuildEvent {
  type: GuildEventType.GUILD_UPDATE;
  data: {
    before: Partial<EnhancedDiscordGuild>;
    after: EnhancedDiscordGuild;
    changes: GuildPropertyChange[];
  };
}

/**
 * Guild property change
 */
export interface GuildPropertyChange {
  property: GuildProperty;
  oldValue: any;
  newValue: any;
  significant: boolean; // Whether this change should trigger notifications
}

/**
 * Guild properties that can change
 */
export enum GuildProperty {
  NAME = 'name',
  ICON = 'icon',
  SPLASH = 'splash',
  DISCOVERY_SPLASH = 'discovery_splash',
  BANNER = 'banner',
  OWNER = 'owner',
  REGION = 'region',
  AFK_CHANNEL = 'afk_channel',
  AFK_TIMEOUT = 'afk_timeout',
  VERIFICATION_LEVEL = 'verification_level',
  EXPLICIT_CONTENT_FILTER = 'explicit_content_filter',
  DEFAULT_MESSAGE_NOTIFICATIONS = 'default_message_notifications',
  MFA_LEVEL = 'mfa_level',
  SYSTEM_CHANNEL = 'system_channel',
  SYSTEM_CHANNEL_FLAGS = 'system_channel_flags',
  RULES_CHANNEL = 'rules_channel',
  PUBLIC_UPDATES_CHANNEL = 'public_updates_channel',
  PREFERRED_LOCALE = 'preferred_locale',
  FEATURES = 'features',
  DESCRIPTION = 'description',
  PREMIUM_TIER = 'premium_tier',
  PREMIUM_SUBSCRIPTION_COUNT = 'premium_subscription_count',
  VANITY_URL = 'vanity_url',
  NSFW_LEVEL = 'nsfw_level',
  PREMIUM_PROGRESS_BAR_ENABLED = 'premium_progress_bar_enabled'
}

/**
 * Guild delete event
 */
export interface GuildDeleteEvent extends BaseGuildEvent {
  type: GuildEventType.GUILD_DELETE;
  data: {
    guild: Partial<EnhancedDiscordGuild>;
    unavailable: boolean; // True if just went offline, false if bot was kicked/banned
    memberCount?: number;
    reason?: GuildLeaveReason;
  };
}

/**
 * Reason for leaving a guild
 */
export enum GuildLeaveReason {
  KICKED = 'kicked',
  BANNED = 'banned',
  LEFT = 'left',
  UNAVAILABLE = 'unavailable',
  UNKNOWN = 'unknown'
}

/**
 * Member add event
 */
export interface MemberAddEvent extends BaseGuildEvent {
  type: GuildEventType.MEMBER_ADD;
  data: {
    member: EnhancedDiscordMember;
    inviteUsed?: DiscordInvite;
    joinMethod: MemberJoinMethod;
    accountAge: number; // Days since Discord account creation
    flagsDetected: MemberJoinFlag[];
  };
}

/**
 * Member join methods
 */
export enum MemberJoinMethod {
  INVITE = 'invite',
  VANITY_URL = 'vanity_url',
  WIDGET = 'widget',
  DISCOVERY = 'discovery',
  UNKNOWN = 'unknown'
}

/**
 * Member join flags for security
 */
export enum MemberJoinFlag {
  NEW_ACCOUNT = 'new_account',      // Account < 7 days old
  NO_AVATAR = 'no_avatar',          // No profile picture
  SUSPICIOUS_NAME = 'suspicious_name', // Contains suspicious patterns
  BOT = 'bot',                      // Is a bot
  SYSTEM = 'system',                // Is a system user
  VERIFIED = 'verified',            // Has verified email
  PHONE_VERIFIED = 'phone_verified'  // Has verified phone
}

/**
 * Member remove event
 */
export interface MemberRemoveEvent extends BaseGuildEvent {
  type: GuildEventType.MEMBER_REMOVE;
  data: {
    member: Partial<EnhancedDiscordMember>;
    reason: MemberLeaveReason;
    timeInGuild: number; // Milliseconds
    roles: Snowflake[];
    nickname: string | null;
    lastSeen: Date;
    messageCount?: number;
  };
}

/**
 * Member leave reasons
 */
export enum MemberLeaveReason {
  LEFT = 'left',
  KICKED = 'kicked',
  BANNED = 'banned',
  UNKNOWN = 'unknown'
}

/**
 * Member update event
 */
export interface MemberUpdateEvent extends BaseGuildEvent {
  type: GuildEventType.MEMBER_UPDATE;
  data: {
    before: Partial<EnhancedDiscordMember>;
    after: EnhancedDiscordMember;
    changes: MemberPropertyChange[];
  };
}

/**
 * Member property change
 */
export interface MemberPropertyChange {
  property: MemberProperty;
  oldValue: any;
  newValue: any;
  significant: boolean;
}

/**
 * Member properties that can change
 */
export enum MemberProperty {
  NICK = 'nick',
  AVATAR = 'avatar',
  ROLES = 'roles',
  PENDING = 'pending',
  COMMUNICATION_DISABLED_UNTIL = 'communication_disabled_until',
  FLAGS = 'flags',
  PREMIUM_SINCE = 'premium_since'
}

/**
 * Role create event
 */
export interface RoleCreateEvent extends BaseGuildEvent {
  type: GuildEventType.ROLE_CREATE;
  data: {
    role: EnhancedDiscordRole;
    position: number;
    dangerous: boolean; // Has dangerous permissions
    createdBy?: Snowflake;
  };
}

/**
 * Role update event
 */
export interface RoleUpdateEvent extends BaseGuildEvent {
  type: GuildEventType.ROLE_UPDATE;
  data: {
    before: Partial<EnhancedDiscordRole>;
    after: EnhancedDiscordRole;
    changes: RolePropertyChange[];
  };
}

/**
 * Role property change
 */
export interface RolePropertyChange {
  property: RoleProperty;
  oldValue: any;
  newValue: any;
  significant: boolean;
  dangerous: boolean; // Whether this change affects dangerous permissions
}

/**
 * Role properties that can change
 */
export enum RoleProperty {
  NAME = 'name',
  COLOR = 'color',
  HOIST = 'hoist',
  MENTIONABLE = 'mentionable',
  PERMISSIONS = 'permissions',
  POSITION = 'position',
  ICON = 'icon',
  UNICODE_EMOJI = 'unicode_emoji'
}

/**
 * Role delete event
 */
export interface RoleDeleteEvent extends BaseGuildEvent {
  type: GuildEventType.ROLE_DELETE;
  data: {
    role: Partial<EnhancedDiscordRole>;
    memberCount: number;
    affectedMembers: Snowflake[];
    replacementRole?: Snowflake;
    deletedBy?: Snowflake;
  };
}

/**
 * Channel create event
 */
export interface ChannelCreateEvent extends BaseGuildEvent {
  type: GuildEventType.CHANNEL_CREATE;
  data: {
    channel: EnhancedDiscordChannel;
    category?: EnhancedDiscordChannel;
    position: number;
    createdBy?: Snowflake;
  };
}

/**
 * Channel update event
 */
export interface ChannelUpdateEvent extends BaseGuildEvent {
  type: GuildEventType.CHANNEL_UPDATE;
  data: {
    before: Partial<EnhancedDiscordChannel>;
    after: EnhancedDiscordChannel;
    changes: ChannelPropertyChange[];
  };
}

/**
 * Channel property change
 */
export interface ChannelPropertyChange {
  property: ChannelProperty;
  oldValue: any;
  newValue: any;
  significant: boolean;
  affectsPermissions: boolean;
}

/**
 * Channel properties that can change
 */
export enum ChannelProperty {
  NAME = 'name',
  TOPIC = 'topic',
  POSITION = 'position',
  PARENT = 'parent',
  NSFW = 'nsfw',
  RATE_LIMIT_PER_USER = 'rate_limit_per_user',
  BITRATE = 'bitrate',
  USER_LIMIT = 'user_limit',
  PERMISSION_OVERWRITES = 'permission_overwrites',
  RTC_REGION = 'rtc_region',
  VIDEO_QUALITY_MODE = 'video_quality_mode',
  DEFAULT_AUTO_ARCHIVE_DURATION = 'default_auto_archive_duration',
  AVAILABLE_TAGS = 'available_tags',
  DEFAULT_REACTION_EMOJI = 'default_reaction_emoji',
  DEFAULT_SORT_ORDER = 'default_sort_order',
  DEFAULT_FORUM_LAYOUT = 'default_forum_layout'
}

/**
 * Channel delete event
 */
export interface ChannelDeleteEvent extends BaseGuildEvent {
  type: GuildEventType.CHANNEL_DELETE;
  data: {
    channel: Partial<EnhancedDiscordChannel>;
    messageCount?: number;
    memberCount?: number;
    deletedBy?: Snowflake;
    lastActivity?: Date;
  };
}

/**
 * Ban add event
 */
export interface BanAddEvent extends BaseGuildEvent {
  type: GuildEventType.BAN_ADD;
  data: {
    user: Partial<EnhancedDiscordMember>;
    reason?: string;
    bannedBy?: Snowflake;
    duration?: number; // Milliseconds, null for permanent
    deleteMessageDays?: number;
  };
}

/**
 * Ban remove event
 */
export interface BanRemoveEvent extends BaseGuildEvent {
  type: GuildEventType.BAN_REMOVE;
  data: {
    user: Partial<EnhancedDiscordMember>;
    unbannedBy?: Snowflake;
    banDuration: number; // Milliseconds ban lasted
    reason?: string;
  };
}

// ====== EVENT HANDLING TYPES ======

/**
 * Guild event handler
 */
export interface GuildEventHandler<T extends BaseGuildEvent = BaseGuildEvent> {
  eventType: GuildEventType;
  handler: (event: T) => Promise<void> | void;
  priority: EventHandlerPriority;
  conditions?: EventCondition[];
  rateLimit?: EventRateLimit;
  retryPolicy?: EventRetryPolicy;
}

/**
 * Event handler priorities
 */
export enum EventHandlerPriority {
  CRITICAL = 0,    // Security, moderation
  HIGH = 1,        // Important business logic
  NORMAL = 2,      // Standard features
  LOW = 3,         // Analytics, logging
  BACKGROUND = 4   // Cleanup, maintenance
}

/**
 * Event condition for filtering
 */
export interface EventCondition {
  field: string;
  operator: EventConditionOperator;
  value: any;
}

/**
 * Event condition operators
 */
export enum EventConditionOperator {
  EQUALS = 'equals',
  NOT_EQUALS = 'not_equals',
  GREATER_THAN = 'greater_than',
  LESS_THAN = 'less_than',
  CONTAINS = 'contains',
  NOT_CONTAINS = 'not_contains',
  IN = 'in',
  NOT_IN = 'not_in',
  REGEX = 'regex'
}

/**
 * Event rate limiting
 */
export interface EventRateLimit {
  maxEvents: number;
  windowMs: number;
  keyGenerator?: (event: BaseGuildEvent) => string;
  skipSuccessfulEvents?: boolean;
}

/**
 * Event retry policy
 */
export interface EventRetryPolicy {
  maxRetries: number;
  backoffMs: number;
  backoffMultiplier: number;
  maxBackoffMs: number;
  retryableErrors: string[];
}

// ====== EVENT SUBSCRIPTION TYPES ======

/**
 * Event subscription
 */
export interface GuildEventSubscription {
  id: string;
  guildId: Snowflake;
  userId: Snowflake;
  eventTypes: GuildEventType[];
  filters: EventSubscriptionFilter[];
  delivery: EventDeliveryMethod;
  isActive: boolean;
  createdAt: Date;
  lastDelivery: Date | null;
  deliveryCount: number;
  failureCount: number;
}

/**
 * Event subscription filter
 */
export interface EventSubscriptionFilter {
  eventType: GuildEventType;
  conditions: EventCondition[];
  include: boolean; // true = include if matches, false = exclude if matches
}

/**
 * Event delivery methods
 */
export interface EventDeliveryMethod {
  type: EventDeliveryType;
  config: Record<string, any>;
  retryPolicy: EventRetryPolicy;
  authentication?: EventAuthentication;
}

/**
 * Event delivery types
 */
export enum EventDeliveryType {
  WEBHOOK = 'webhook',
  WEBSOCKET = 'websocket',
  QUEUE = 'queue',
  EMAIL = 'email',
  DISCORD_DM = 'discord_dm',
  INTERNAL = 'internal'
}

/**
 * Event authentication
 */
export interface EventAuthentication {
  type: 'hmac' | 'bearer' | 'basic' | 'custom';
  credentials: Record<string, string>;
  headers?: Record<string, string>;
}

// ====== EVENT ANALYTICS TYPES ======

/**
 * Event analytics data
 */
export interface GuildEventAnalytics {
  guildId: Snowflake;
  period: {
    start: Date;
    end: Date;
  };
  
  summary: {
    totalEvents: number;
    uniqueEventTypes: number;
    averageEventsPerDay: number;
    peakEventsPerHour: number;
  };
  
  breakdown: {
    byType: Record<GuildEventType, number>;
    byHour: number[];
    byDay: number[];
    bySource: Record<GuildEventSource, number>;
  };
  
  trends: {
    membershipChanges: MembershipTrend;
    activityLevels: ActivityTrend;
    moderationActions: ModerationTrend;
    configurationChanges: ConfigurationTrend;
  };
  
  anomalies: EventAnomaly[];
}

/**
 * Membership trend data
 */
export interface MembershipTrend {
  joins: number[];
  leaves: number[];
  netGrowth: number[];
  churnRate: number;
  retention: {
    day1: number;
    day7: number;
    day30: number;
  };
}

/**
 * Activity trend data
 */
export interface ActivityTrend {
  messages: number[];
  voiceActivity: number[];
  reactionActivity: number[];
  commandUsage: number[];
  peakTimes: Date[];
}

/**
 * Moderation trend data
 */
export interface ModerationTrend {
  bans: number[];
  kicks: number[];
  timeouts: number[];
  warnings: number[];
  autoModActions: number[];
  appealRate: number;
}

/**
 * Configuration trend data
 */
export interface ConfigurationTrend {
  roleChanges: number[];
  channelChanges: number[];
  permissionChanges: number[];
  settingChanges: number[];
  securityLevel: SecurityLevelTrend;
}

/**
 * Security level trend
 */
export interface SecurityLevelTrend {
  current: SecurityLevel;
  history: Array<{
    date: Date;
    level: SecurityLevel;
    reason: string;
  }>;
}

/**
 * Security levels
 */
export enum SecurityLevel {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

/**
 * Event anomaly detection
 */
export interface EventAnomaly {
  id: string;
  type: AnomalyType;
  severity: AnomialySeverity;
  detectedAt: Date;
  description: string;
  affectedEvents: number;
  suggestedAction: string;
  resolved: boolean;
  resolvedAt?: Date;
}

/**
 * Anomaly types
 */
export enum AnomalyType {
  UNUSUAL_VOLUME = 'unusual_volume',
  UNEXPECTED_PATTERN = 'unexpected_pattern',
  SECURITY_CONCERN = 'security_concern',
  PERFORMANCE_ISSUE = 'performance_issue',
  DATA_INCONSISTENCY = 'data_inconsistency'
}

/**
 * Anomaly severity levels
 */
export enum AnomialySeverity {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  CRITICAL = 'critical'
}

// ====== UTILITY TYPES ======

/**
 * Event aggregation functions
 */
export type EventAggregator<T extends BaseGuildEvent> = (events: T[]) => any;

/**
 * Event transformation functions
 */
export type EventTransformer<T extends BaseGuildEvent, R> = (event: T) => R;

/**
 * Event validation functions
 */
export type EventValidator<T extends BaseGuildEvent> = (event: T) => boolean;

/**
 * Union type for all guild events
 */
export type GuildEvent = 
  | GuildCreateEvent
  | GuildUpdateEvent
  | GuildDeleteEvent
  | MemberAddEvent
  | MemberRemoveEvent
  | MemberUpdateEvent
  | RoleCreateEvent
  | RoleUpdateEvent
  | RoleDeleteEvent
  | ChannelCreateEvent
  | ChannelUpdateEvent
  | ChannelDeleteEvent
  | BanAddEvent
  | BanRemoveEvent;