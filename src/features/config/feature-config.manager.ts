import { CacheService } from '@/core/cache/cache.service';
import { Injectable, Logger } from '@nestjs/common';
import {
    FeatureConfig,
    FeatureConfigurationError,
    ReactionRoleServiceConfig,
    StarboardServiceConfig,
    UserCommandServiceConfig,
    UtilityServiceConfig,
    WelcomeServiceConfig
} from '../types';

/**
 * Centralized configuration manager for all feature services
 * Provides type-safe configuration management with Redis persistence
 */
@Injectable()
export class FeatureConfigManager {
  private readonly logger = new Logger(FeatureConfigManager.name);
  private readonly configCache = new Map<string, Map<string, FeatureConfig>>();
  private readonly CONFIG_PREFIX = 'feature_config';
  private readonly CACHE_TTL = 300000; // 5 minutes

  constructor(private readonly cacheService: CacheService) {}

  /**
   * Get feature configuration for a specific guild and service
   */
  async getConfig<T extends FeatureConfig>(
    guildId: string,
    serviceName: string,
    defaultConfig: T
  ): Promise<T> {
    try {
      // Check memory cache first
      const guildCache = this.configCache.get(guildId);
      if (guildCache?.has(serviceName)) {
        const cachedConfig = guildCache.get(serviceName) as T;
        if (this.isConfigValid(cachedConfig)) {
          return cachedConfig;
        }
      }

      // Fetch from Redis
      const configKey = this.getConfigKey(guildId, serviceName);
      const storedConfig = await this.cacheService.get(configKey);

      if (storedConfig) {
        const parsedConfig = typeof storedConfig === 'string' 
          ? JSON.parse(storedConfig) 
          : storedConfig;
        
        const mergedConfig = { ...defaultConfig, ...parsedConfig };
        
        // Update memory cache
        this.updateMemoryCache(guildId, serviceName, mergedConfig);
        
        return mergedConfig as T;
      }

      // Return default config and cache it
      await this.setConfig(guildId, serviceName, defaultConfig);
      return defaultConfig;
    } catch (error) {
      this.logger.error(`Failed to get config for ${serviceName} in guild ${guildId}:`, error);
      return defaultConfig;
    }
  }

  /**
   * Set feature configuration for a specific guild and service
   */
  async setConfig<T extends FeatureConfig>(
    guildId: string,
    serviceName: string,
    config: T
  ): Promise<void> {
    try {
      // Validate configuration
      const validation = this.validateConfig(config);
      if (!validation.valid) {
        throw new FeatureConfigurationError(
          serviceName,
          `Invalid configuration: ${validation.errors.join(', ')}`
        );
      }

      // Store in Redis
      const configKey = this.getConfigKey(guildId, serviceName);
      await this.cacheService.set(configKey, config, this.CACHE_TTL);

      // Update memory cache
      this.updateMemoryCache(guildId, serviceName, config);

      this.logger.log(`Updated config for ${serviceName} in guild ${guildId}`);
    } catch (error) {
      this.logger.error(`Failed to set config for ${serviceName} in guild ${guildId}:`, error);
      throw error;
    }
  }

  /**
   * Update specific settings in a feature configuration
   */
  async updateConfig<T extends FeatureConfig>(
    guildId: string,
    serviceName: string,
    updates: Partial<T>,
    defaultConfig: T
  ): Promise<T> {
    try {
      const currentConfig = await this.getConfig(guildId, serviceName, defaultConfig);
      const updatedConfig = {
        ...currentConfig,
        ...updates,
        settings: {
          ...currentConfig.settings,
          ...updates.settings
        },
        metadata: {
          ...currentConfig.metadata,
          ...updates.metadata
        }
      } as T;

      await this.setConfig(guildId, serviceName, updatedConfig);
      return updatedConfig;
    } catch (error) {
      this.logger.error(`Failed to update config for ${serviceName} in guild ${guildId}:`, error);
      throw error;
    }
  }

  /**
   * Delete feature configuration for a specific guild and service
   */
  async deleteConfig(guildId: string, serviceName: string): Promise<void> {
    try {
      // Remove from Redis
      const configKey = this.getConfigKey(guildId, serviceName);
      await this.cacheService.del(configKey);

      // Remove from memory cache
      const guildCache = this.configCache.get(guildId);
      if (guildCache) {
        guildCache.delete(serviceName);
        if (guildCache.size === 0) {
          this.configCache.delete(guildId);
        }
      }

      this.logger.log(`Deleted config for ${serviceName} in guild ${guildId}`);
    } catch (error) {
      this.logger.error(`Failed to delete config for ${serviceName} in guild ${guildId}:`, error);
      throw error;
    }
  }

  /**
   * Get all feature configurations for a guild
   */
  async getAllConfigs(guildId: string): Promise<Map<string, FeatureConfig>> {
    try {
      const configs = new Map<string, FeatureConfig>();
      const pattern = `${this.CONFIG_PREFIX}:${guildId}:*`;
      
      // Get all config keys for the guild
      const keys = await this.cacheService.keys(pattern as any);
      
      for (const key of keys) {
        const serviceName = key.split(':').pop();
        if (serviceName) {
          const config = await this.cacheService.get(key);
          if (config) {
            const parsedConfig = typeof config === 'string' ? JSON.parse(config) : config;
            configs.set(serviceName, parsedConfig);
          }
        }
      }

      return configs;
    } catch (error) {
      this.logger.error(`Failed to get all configs for guild ${guildId}:`, error);
      return new Map();
    }
  }

  /**
   * Clear all configurations for a guild (useful for guild leave)
   */
  async clearGuildConfigs(guildId: string): Promise<void> {
    try {
      const pattern = `${this.CONFIG_PREFIX}:${guildId}:*`;
      const keys = await this.cacheService.keys(pattern as any);
      if (Array.isArray(keys) && keys.length > 0) {
        await (this.cacheService as any).del(...keys);
      }
      
      // Clear memory cache
      this.configCache.delete(guildId);
      
      this.logger.log(`Cleared all configs for guild ${guildId}`);
    } catch (error) {
      this.logger.error(`Failed to clear configs for guild ${guildId}:`, error);
      throw error;
    }
  }

  /**
   * Type-safe configuration getters for specific services
   */
  async getWelcomeConfig(guildId: string): Promise<WelcomeServiceConfig> {
    const defaultConfig: WelcomeServiceConfig = {
      enabled: true,
      version: '1.0.0',
      channelId: undefined,
      message: undefined,
      roles: [],
      embedEnabled: true,
      dmWelcome: false,
      autoDeleteDelay: 0
    };

    return this.getConfig(guildId, 'welcome', defaultConfig);
  }

  async getUtilityConfig(guildId: string): Promise<UtilityServiceConfig> {
    const defaultConfig: UtilityServiceConfig = {
      enabled: true,
      version: '1.0.0',
      pingEnabled: true,
      serverInfoEnabled: true,
      userInfoEnabled: true,
      helpEnabled: true,
      customCommands: []
    };

    return this.getConfig(guildId, 'utility', defaultConfig);
  }

  async getUserCommandConfig(guildId: string): Promise<UserCommandServiceConfig> {
    const defaultConfig: UserCommandServiceConfig = {
      enabled: false,
      version: '1.0.0',
      prefix: '!',
      allowedChannels: [],
      commands: [],
      moderationEnabled: true,
      logCommands: true
    };

    return this.getConfig(guildId, 'user-command', defaultConfig);
  }

  async getStarboardConfig(guildId: string): Promise<StarboardServiceConfig> {
    const defaultConfig: StarboardServiceConfig = {
      enabled: false,
      version: '1.0.0',
      channelId: undefined,
      threshold: 3,
      emoji: '⭐',
      embedColor: 0xFFD700,
      ignoreBots: true,
      ignoreChannels: [],
      requireUniqueReactions: true
    };

    return this.getConfig(guildId, 'starboard', defaultConfig);
  }

  async getReactionRoleConfig(guildId: string): Promise<ReactionRoleServiceConfig> {
    const defaultConfig: ReactionRoleServiceConfig = {
      enabled: false,
      version: '1.0.0',
      rules: [],
      requireBothActions: true,
      ignoreBots: true,
      logActions: true,
      maxRolesPerUser: 20
    };

    return this.getConfig(guildId, 'reaction-role', defaultConfig);
  }

  /**
   * Private helper methods
   */
  private getConfigKey(guildId: string, serviceName: string): string {
    return `${this.CONFIG_PREFIX}:${guildId}:${serviceName}`;
  }

  private updateMemoryCache(
    guildId: string,
    serviceName: string,
    config: FeatureConfig
  ): void {
    if (!this.configCache.has(guildId)) {
      this.configCache.set(guildId, new Map());
    }
    
    const guildCache = this.configCache.get(guildId)!;
    guildCache.set(serviceName, config);
  }

  private validateConfig(config: FeatureConfig): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (typeof config.enabled !== 'boolean') {
      errors.push('enabled must be a boolean');
    }

    if (typeof config.version !== 'string' || !config.version.trim()) {
      errors.push('version must be a non-empty string');
    }

    if (config.settings && typeof config.settings !== 'object') {
      errors.push('settings must be an object');
    }

    if (config.metadata && typeof config.metadata !== 'object') {
      errors.push('metadata must be an object');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  private isConfigValid(config: FeatureConfig): boolean {
    return this.validateConfig(config).valid;
  }

  /**
   * Health check for configuration manager
   */
  async healthCheck(): Promise<{ healthy: boolean; details: Record<string, unknown> }> {
    try {
      // Test Redis connection
      const testKey = `${this.CONFIG_PREFIX}:health_check`;
      await this.cacheService.set(testKey, { test: true }, 1000);
      const retrieved = await this.cacheService.get(testKey);
      await this.cacheService.del(testKey);

      return {
        healthy: retrieved !== null,
        details: {
          redisConnected: retrieved !== null,
          memoryCacheSize: this.configCache.size,
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      return {
        healthy: false,
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date().toISOString()
        }
      };
    }
  }
}
