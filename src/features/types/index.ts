/**
 * Feature services type definitions
 * Complete type safety for all feature modules
 */

export * from './feature-services.interface';

// Re-export enums and interfaces that are used as values
// Re-export enums and interfaces that are used as values
export { FeatureStatus } from './feature-services.interface';
export type { BaseFeatureService } from './feature-services.interface';

/**
 * Feature service type guards
 */
export const isBaseFeatureService = (obj: unknown): obj is any => {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'name' in obj &&
    'version' in obj &&
    'enabled' in obj &&
    'getStatus' in obj &&
    'getConfig' in obj &&
    typeof (obj as any).name === 'string' &&
    typeof (obj as any).version === 'string' &&
    typeof (obj as any).enabled === 'boolean' &&
    typeof (obj as any).getStatus === 'function' &&
    typeof (obj as any).getConfig === 'function'
  );
};

export const isFeatureEnabled = (service: any): boolean => {
  // Import locally to ensure value (not type-only) usage
  const { FeatureStatus } = require('./feature-services.interface') as typeof import('./feature-services.interface');
  return service.enabled && service.getStatus() === FeatureStatus.ACTIVE;
};

/**
 * Utility functions for feature services
 */
export const createFeatureConfig = (
  enabled: boolean = true,
  version: string = '1.0.0',
  settings: Record<string, unknown> = {},
  metadata: Record<string, unknown> = {}
): any => {
  return {
    enabled,
    version,
    settings,
    metadata
  };
};

export const createUserInteraction = (
  userId: string,
  guildId: string,
  channelId: string,
  type: any,
  data: Record<string, unknown> = {}
): any => {
  return {
    userId,
    guildId,
    channelId,
    timestamp: new Date(),
    type,
    data
  };
};

/**
 * Constants for feature services
 */
export const FEATURE_SERVICE_CONSTANTS = {
  DEFAULT_VERSION: '1.0.0',
  DEFAULT_COOLDOWN: 5000, // 5 seconds
  DEFAULT_PREFIX: '!',
  DEFAULT_STARBOARD_THRESHOLD: 3,
  MAX_ROLES_PER_USER: 20,
  MAX_COMMANDS_PER_GUILD: 100,
  INTERACTION_TIMEOUT: 30000, // 30 seconds
  CACHE_TTL: 300000, // 5 minutes
} as const;

/**
 * Feature service error types
 */
export class FeatureServiceError extends Error {
  constructor(
    public readonly serviceName: string,
    public readonly operation: string,
    message: string,
    public readonly cause?: Error
  ) {
    super(`[${serviceName}:${operation}] ${message}`);
    this.name = 'FeatureServiceError';
  }
}

export class FeatureConfigurationError extends FeatureServiceError {
  constructor(serviceName: string, message: string, cause?: Error) {
    super(serviceName, 'configuration', message, cause);
    this.name = 'FeatureConfigurationError';
  }
}

export class FeatureOperationError extends FeatureServiceError {
  constructor(serviceName: string, operation: string, message: string, cause?: Error) {
    super(serviceName, operation, message, cause);
    this.name = 'FeatureOperationError';
  }
}

/**
 * Feature service validation utilities
 */
export const validateFeatureConfig = (config: any): { valid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (typeof config.enabled !== 'boolean') {
    errors.push('enabled must be a boolean');
  }

  if (typeof config.version !== 'string' || !config.version.trim()) {
    errors.push('version must be a non-empty string');
  }

  if (config.settings && typeof config.settings !== 'object') {
    errors.push('settings must be an object');
  }

  if (config.metadata && typeof config.metadata !== 'object') {
    errors.push('metadata must be an object');
  }

  return {
    valid: errors.length === 0,
    errors
  };
};

export const validateUserCommand = (command: any): { valid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (!command.name || typeof command.name !== 'string' || !command.name.trim()) {
    errors.push('name must be a non-empty string');
  }

  if (!command.response || typeof command.response !== 'string' || !command.response.trim()) {
    errors.push('response must be a non-empty string');
  }

  if (command.cooldown !== undefined && (typeof command.cooldown !== 'number' || command.cooldown < 0)) {
    errors.push('cooldown must be a non-negative number');
  }

  if (command.permissions && (!Array.isArray(command.permissions) || 
    !command.permissions.every(p => typeof p === 'string'))) {
    errors.push('permissions must be an array of strings');
  }

  return {
    valid: errors.length === 0,
    errors
  };
};
