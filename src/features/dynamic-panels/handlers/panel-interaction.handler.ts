import { Injectable, Logger } from '@nestjs/common';
import { ActionRow<PERSON>uilder, ButtonBuilder, ButtonInteraction, ButtonStyle, EmbedBuilder, ModalBuilder, ModalSubmitInteraction, TextInputBuilder, TextInputStyle } from 'discord.js';
import { ButtonContext, Context } from 'necord';
import { BYOKService } from '../../../core/agents/services/byok.service';
import { PanelConfigService } from '../services/panel-config.service';
import { PanelOrchestratorService } from '../services/panel-orchestrator.service';

@Injectable()
export class PanelInteractionHandler {
  private readonly logger = new Logger(PanelInteractionHandler.name);

  constructor(
    private readonly panelConfig: PanelConfigService,
    private readonly panelOrchestrator: PanelOrchestratorService,
    private readonly byokService: BYOKService
  ) {}

  async handlePanelButtonInteraction(
    @Context() [interaction]: ButtonContext
  ): Promise<void> {
    try {
      // Handle BYOK-specific interactions
      if (interaction.customId.startsWith('byok_')) {
        await this.handleBYOKInteraction(interaction);
        return;
      }

      // Handle panel interactions
      if (!interaction.customId.startsWith('panel:')) return;

      const [, panelId, action, targetUserId] = interaction.customId.split(':');

      // Verify user authorization
      if (interaction.user.id !== targetUserId) {
        await interaction.reply({
          content: '❌ This panel is not for you.',
          ephemeral: true
        });
        return;
      }

      if (!panelId || !action) {
        await interaction.reply({
          content: '❌ Invalid panel interaction.',
          ephemeral: true
        });
        return;
      }

      const config = this.panelConfig.getPanelConfig(panelId);
      if (!config) {
        await interaction.reply({
          content: '❌ Panel configuration not found.',
          ephemeral: true
        });
        return;
      }

      await this.routePanelAction(interaction, panelId, action, config);

    } catch (error) {
      this.logger.error('Error handling panel button interaction:', error);
      if (!interaction.replied) {
        await interaction.reply({
          content: '❌ An error occurred while processing your request.',
          ephemeral: true
        });
      }
    }
  }

  private async handleBYOKInteraction(interaction: ButtonInteraction): Promise<void> {
    try {
      const [action, targetUserId] = interaction.customId.split(':');

      // Verify user authorization
      if (interaction.user.id !== targetUserId) {
        await interaction.reply({
          content: '❌ This configuration is not for you.',
          ephemeral: true
        });
        return;
      }

      switch (action) {
        case 'byok_modal':
          const modal = new ModalBuilder()
            .setCustomId(`byok_setup_modal:${interaction.user.id}`)
            .setTitle('Configure Your API Keys');

          const openaiInput = new TextInputBuilder()
            .setCustomId('openai_key')
            .setLabel('OpenAI API Key')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('sk-...')
            .setRequired(false);

          const anthropicInput = new TextInputBuilder()
            .setCustomId('anthropic_key')
            .setLabel('Anthropic API Key')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('sk-ant-...')
            .setRequired(false);

          const googleInput = new TextInputBuilder()
            .setCustomId('google_key')
            .setLabel('Google API Key')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('Enter your Google API key')
            .setRequired(false);

          const exaInput = new TextInputBuilder()
            .setCustomId('exa_key')
            .setLabel('Exa API Key (Web Search)')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('Enter your Exa API key for web search')
            .setRequired(false);

          modal.addComponents(
            new ActionRowBuilder<TextInputBuilder>().addComponents(openaiInput),
            new ActionRowBuilder<TextInputBuilder>().addComponents(anthropicInput),
            new ActionRowBuilder<TextInputBuilder>().addComponents(googleInput),
            new ActionRowBuilder<TextInputBuilder>().addComponents(exaInput)
          );

          await interaction.showModal(modal);
          break;
        case 'byok_status':
          await this.showBYOKStatus(interaction);
          break;
        default:
          await interaction.reply({
            content: '❌ Unknown BYOK action.',
            ephemeral: true
          });
      }
    } catch (error) {
      this.logger.error(`Failed to handle BYOK interaction: ${(error as Error).message}`, error);
      
      if (!interaction.replied && !interaction.deferred) {
        await interaction.reply({
          content: '❌ An error occurred while processing your BYOK request.',
          ephemeral: true
        });
      }
    }
  }

  async handleModalSubmit(
    @Context() [interaction]: [ModalSubmitInteraction]
  ): Promise<void> {
    try {
      if (!interaction.customId.startsWith('byok_setup_modal:')) return;

      const [, targetUserId] = interaction.customId.split(':');

      // Verify user authorization
      if (interaction.user.id !== targetUserId) {
        await interaction.reply({
          content: '❌ This configuration is not for you.',
          ephemeral: true
        });
        return;
      }

      await this.handleBYOKModalSubmit(interaction);

    } catch (error) {
      this.logger.error(`Failed to handle modal submit: ${(error as Error).message}`, error);
      
      if (!interaction.replied && !interaction.deferred) {
        await interaction.reply({
          content: '❌ An error occurred while saving your API keys.',
          ephemeral: true
        });
      }
    }
  }

  private async handleBYOKModalSubmit(interaction: ModalSubmitInteraction): Promise<void> {
    const openaiKey = interaction.fields.getTextInputValue('openai_key');
    const anthropicKey = interaction.fields.getTextInputValue('anthropic_key');
    const googleKey = interaction.fields.getTextInputValue('google_key');
    const exaKey = interaction.fields.getTextInputValue('exa_key');

    const memberId = interaction.user.id;
    const savedProviders = [];

    try {
      // Save each provided key
      if (openaiKey && openaiKey.trim()) {
        await this.byokService.storeUserKey(memberId, 'openai', openaiKey.trim());
        savedProviders.push('OpenAI');
      }

      if (anthropicKey && anthropicKey.trim()) {
        await this.byokService.storeUserKey(memberId, 'anthropic', anthropicKey.trim());
        savedProviders.push('Anthropic');
      }

      if (googleKey && googleKey.trim()) {
        await this.byokService.storeUserKey(memberId, 'google', googleKey.trim());
        savedProviders.push('Google');
      }

      if (exaKey && exaKey.trim()) {
        await this.byokService.storeUserKey(memberId, 'exa', exaKey.trim());
        savedProviders.push('Exa (Web Search)');
      }

      if (savedProviders.length === 0) {
        await interaction.reply({
          content: 'ℹ️ No API keys were provided. Your configuration remains unchanged.',
          ephemeral: true
        });
        return;
      }

      const embed = new EmbedBuilder()
        .setTitle('✅ BYOK Configuration Updated')
        .setDescription(`Successfully configured ${savedProviders.join(', ')} API keys`)
        .setColor(0x10B981)
        .addFields([
          { name: '🛡️ Security', value: 'Keys encrypted and isolated per member', inline: true },
          { name: '🔓 Access', value: 'Available for all AI agents', inline: true }
        ])
        .setFooter({ text: 'Your keys are now active for enhanced AI capabilities' });

      await interaction.reply({ embeds: [embed], ephemeral: true });

    } catch (error) {
      this.logger.error(`Failed to save BYOK keys for ${memberId}:`, error);
      await interaction.reply({
        content: `❌ Failed to save API keys: ${(error as Error).message}`,
        ephemeral: true
      });
    }
  }

  private async routePanelAction(
    interaction: ButtonInteraction,
    panelId: string,
    action: string,
    config: any
  ): Promise<void> {
    const [category, specificAction] = action.split(':');

    if (!specificAction) {
      await interaction.reply({
        content: '❌ Invalid action format.',
        ephemeral: true
      });
      return;
    }

    switch (category) {
      case 'ai':
        await this.handleAIAction(interaction, specificAction, config);
        break;

      case 'wealth':
        await this.handleWealthAction(interaction, specificAction, config);
        break;

      case 'dev':
        await this.handleDevAction(interaction, specificAction, config);
        break;

      case 'growth':
        await this.handleGrowthAction(interaction, specificAction, config);
        break;

      case 'enterprise':
        await this.handleEnterpriseAction(interaction, specificAction, config);
        break;

      case 'business':
        await this.handleBusinessAction(interaction, specificAction, config);
        break;

      case 'ai_agent':
        await this.handleAIAgentAction(interaction, specificAction, config);
        break;

      default:
        await interaction.reply({
          content: `❌ Unknown action category: ${category}`,
          ephemeral: true
        });
    }
  }

  private async handleAIAction(
    interaction: ButtonInteraction,
    action: string,
    config: any
  ): Promise<void> {
    switch (action) {
      case 'start_session':
        await this.startAISession(interaction);
        break;
      
      case 'view_progress':
        await this.showAIProgress(interaction);
        break;
      
      case 'quick_question':
        await this.showQuickQuestionModal(interaction);
        break;

      default:
        await interaction.reply({
          content: `❌ Unknown AI action: ${action}`,
          ephemeral: true
        });
    }
  }

  private async handleWealthAction(
    interaction: ButtonInteraction,
    action: string,
    config: any
  ): Promise<void> {
    switch (action) {
      case 'portfolio_analysis':
        await this.showPortfolioAnalysis(interaction);
        break;
      
      case 'create_plan':
        await this.showWealthPlanModal(interaction);
        break;
      
      case 'risk_assessment':
        await this.showRiskAssessment(interaction);
        break;
      
      case 'market_insights':
        await this.showMarketInsights(interaction);
        break;

      default:
        await interaction.reply({
          content: `❌ Unknown wealth action: ${action}`,
          ephemeral: true
        });
    }
  }

  private async handleDevAction(
    interaction: ButtonInteraction,
    action: string,
    config: any
  ): Promise<void> {
    switch (action) {
      case 'create_request':
        await this.showDevRequestModal(interaction);
        break;
      
      case 'browse_developers':
        await this.showDeveloperList(interaction);
        break;
      
      case 'my_requests':
        await this.showUserRequests(interaction);
        break;
      
      case 'payment_setup':
        await this.showPaymentSetup(interaction);
        break;

      default:
        await interaction.reply({
          content: `❌ Unknown dev action: ${action}`,
          ephemeral: true
        });
    }
  }

  private async handleGrowthAction(
    interaction: ButtonInteraction,
    action: string,
    config: any
  ): Promise<void> {
    switch (action) {
      case 'set_goals':
        await this.showGoalSetupModal(interaction);
        break;
      
      case 'track_habits':
        await this.showHabitTracker(interaction);
        break;
      
      case 'view_insights':
        await this.showGrowthInsights(interaction);
        break;
      
      case 'mindfulness':
        await this.showMindfulnessOptions(interaction);
        break;

      default:
        await interaction.reply({
          content: `❌ Unknown growth action: ${action}`,
          ephemeral: true
        });
    }
  }

  private async handleEnterpriseAction(
    interaction: ButtonInteraction,
    action: string,
    config: any
  ): Promise<void> {
    switch (action) {
      case 'support_ticket':
        await this.showSupportTicketModal(interaction);
        break;
      
      case 'custom_integration':
        await this.showCustomIntegrationForm(interaction);
        break;
      
      case 'analytics':
        await this.showEnterpriseAnalytics(interaction);
        break;
      
      case 'white_label':
        await this.showWhiteLabelOptions(interaction);
        break;

      default:
        await interaction.reply({
          content: `❌ Unknown enterprise action: ${action}`,
          ephemeral: true
        });
    }
  }

  // AI Action Implementations
  private async startAISession(interaction: ButtonInteraction): Promise<void> {
    const embed = new EmbedBuilder()
      .setTitle('🤖 AI Session Started')
      .setDescription('Your personalized AI learning session is now active!')
      .setColor(0x3B82F6)
      .addFields([
        { name: '🎯 Focus Area', value: 'Machine Learning Basics', inline: true },
        { name: '⏱️ Session Duration', value: '30 minutes', inline: true },
        { name: '📊 Difficulty', value: 'Beginner', inline: true }
      ])
      .setFooter({ text: 'Ask me anything about AI and machine learning!' });

    await interaction.reply({ embeds: [embed], ephemeral: true });
  }

  private async showAIProgress(interaction: ButtonInteraction): Promise<void> {
    const embed = new EmbedBuilder()
      .setTitle('📊 Your AI Mastery Progress')
      .setDescription('Track your learning journey and achievements')
      .setColor(0x10B981)
      .addFields([
        { name: '🎯 Current Level', value: 'Intermediate (Level 3)', inline: true },
        { name: '📈 Progress', value: '67% Complete', inline: true },
        { name: '⭐ Points Earned', value: '1,250 XP', inline: true },
        { name: '🏆 Achievements', value: '• First AI Model\n• Data Analysis Pro\n• Python Expert', inline: false },
        { name: '📚 Completed Modules', value: '• Introduction to AI\n• Machine Learning Basics\n• Neural Networks 101', inline: false }
      ]);

    await interaction.reply({ embeds: [embed], ephemeral: true });
  }

  private async showQuickQuestionModal(interaction: ButtonInteraction): Promise<void> {
    const modal = new ModalBuilder()
      .setCustomId(`ai_question_${interaction.user.id}`)
      .setTitle('Quick AI Question');

    const questionInput = new TextInputBuilder()
      .setCustomId('question')
      .setLabel('What would you like to know?')
      .setStyle(TextInputStyle.Paragraph)
      .setPlaceholder('Ask anything about AI, machine learning, or programming...')
      .setRequired(true)
      .setMaxLength(1000);

    const actionRow = new ActionRowBuilder<TextInputBuilder>().addComponents(questionInput);
    modal.addComponents(actionRow);

    await interaction.showModal(modal);
  }

  // Wealth Action Implementations
  private async showPortfolioAnalysis(interaction: ButtonInteraction): Promise<void> {
    const embed = new EmbedBuilder()
      .setTitle('💼 Portfolio Analysis')
      .setDescription('AI-powered analysis of your investment portfolio')
      .setColor(0x10B981)
      .addFields([
        { name: '📊 Total Value', value: '$0 (Connect your accounts)', inline: true },
        { name: '📈 Performance', value: 'N/A', inline: true },
        { name: '⚖️ Risk Level', value: 'Not Assessed', inline: true },
        { name: '🎯 Recommended Actions', value: '• Connect your investment accounts\n• Complete risk assessment\n• Set investment goals', inline: false }
      ])
      .setFooter({ text: 'Connect your accounts for personalized analysis' });

    await interaction.reply({ embeds: [embed], ephemeral: true });
  }

  private async showWealthPlanModal(interaction: ButtonInteraction): Promise<void> {
    const modal = new ModalBuilder()
      .setCustomId(`wealth_plan_${interaction.user.id}`)
      .setTitle('Create Your Wealth Plan');

    const goalInput = new TextInputBuilder()
      .setCustomId('financial_goal')
      .setLabel('Financial Goal')
      .setStyle(TextInputStyle.Short)
      .setPlaceholder('e.g., Save $100,000 for retirement')
      .setRequired(true);

    const timeframeInput = new TextInputBuilder()
      .setCustomId('timeframe')
      .setLabel('Timeframe')
      .setStyle(TextInputStyle.Short)
      .setPlaceholder('e.g., 5 years')
      .setRequired(true);

    const incomeInput = new TextInputBuilder()
      .setCustomId('monthly_income')
      .setLabel('Monthly Income')
      .setStyle(TextInputStyle.Short)
      .setPlaceholder('e.g., $5000')
      .setRequired(false);

    const actionRow1 = new ActionRowBuilder<TextInputBuilder>().addComponents(goalInput);
    const actionRow2 = new ActionRowBuilder<TextInputBuilder>().addComponents(timeframeInput);
    const actionRow3 = new ActionRowBuilder<TextInputBuilder>().addComponents(incomeInput);

    modal.addComponents(actionRow1, actionRow2, actionRow3);

    await interaction.showModal(modal);
  }

  // Dev Action Implementations
  private async showDevRequestModal(interaction: ButtonInteraction): Promise<void> {
    const modal = new ModalBuilder()
      .setCustomId(`dev_request_${interaction.user.id}`)
      .setTitle('Create Development Request');

    const descriptionInput = new TextInputBuilder()
      .setCustomId('description')
      .setLabel('Project Description')
      .setStyle(TextInputStyle.Paragraph)
      .setPlaceholder('Describe your project in detail...')
      .setRequired(true)
      .setMaxLength(2000);

    const budgetInput = new TextInputBuilder()
      .setCustomId('budget')
      .setLabel('Budget Range')
      .setStyle(TextInputStyle.Short)
      .setPlaceholder('e.g., $500 - $2000')
      .setRequired(false);

    const timelineInput = new TextInputBuilder()
      .setCustomId('timeline')
      .setLabel('Timeline')
      .setStyle(TextInputStyle.Short)
      .setPlaceholder('e.g., 2 weeks')
      .setRequired(false);

    const skillsInput = new TextInputBuilder()
      .setCustomId('skills')
      .setLabel('Required Skills')
      .setStyle(TextInputStyle.Short)
      .setPlaceholder('e.g., React, Node.js, MongoDB')
      .setRequired(false);

    modal.addComponents(
      new ActionRowBuilder<TextInputBuilder>().addComponents(descriptionInput),
      new ActionRowBuilder<TextInputBuilder>().addComponents(budgetInput),
      new ActionRowBuilder<TextInputBuilder>().addComponents(timelineInput),
      new ActionRowBuilder<TextInputBuilder>().addComponents(skillsInput)
    );

    await interaction.showModal(modal);
  }

  // Additional action implementations would go here...
  // For brevity, I'll add placeholder methods

  private async showRiskAssessment(interaction: ButtonInteraction): Promise<void> {
    await interaction.reply({
      content: '⚖️ **Risk Assessment Tool**\n\nThis feature helps you evaluate your investment risk tolerance. Coming soon!',
      ephemeral: true
    });
  }

  private async showMarketInsights(interaction: ButtonInteraction): Promise<void> {
    await interaction.reply({
      content: '🔍 **Market Insights**\n\nReal-time market analysis and trends. Coming soon!',
      ephemeral: true
    });
  }

  private async showDeveloperList(interaction: ButtonInteraction): Promise<void> {
    await interaction.reply({
      content: '👨‍💻 **Browse Developers**\n\nFind skilled developers for your projects. Coming soon!',
      ephemeral: true
    });
  }

  private async showUserRequests(interaction: ButtonInteraction): Promise<void> {
    await interaction.reply({
      content: '📋 **Your Requests**\n\nView and manage your development requests. Coming soon!',
      ephemeral: true
    });
  }

  private async showPaymentSetup(interaction: ButtonInteraction): Promise<void> {
    await interaction.reply({
      content: '💳 **Payment Setup**\n\nSecure payment configuration for your projects. Coming soon!',
      ephemeral: true
    });
  }

  private async showGoalSetupModal(interaction: ButtonInteraction): Promise<void> {
    await interaction.reply({
      content: '🎯 **Goal Setting**\n\nSet and track your personal development goals. Coming soon!',
      ephemeral: true
    });
  }

  private async showHabitTracker(interaction: ButtonInteraction): Promise<void> {
    await interaction.reply({
      content: '✅ **Habit Tracker**\n\nTrack your daily habits and build consistency. Coming soon!',
      ephemeral: true
    });
  }

  private async showGrowthInsights(interaction: ButtonInteraction): Promise<void> {
    await interaction.reply({
      content: '📈 **Growth Insights**\n\nAnalyze your personal development patterns. Coming soon!',
      ephemeral: true
    });
  }

  private async showMindfulnessOptions(interaction: ButtonInteraction): Promise<void> {
    await interaction.reply({
      content: '🧘‍♂️ **Mindfulness**\n\nAccess guided meditation and mindfulness exercises. Coming soon!',
      ephemeral: true
    });
  }

  private async showSupportTicketModal(interaction: ButtonInteraction): Promise<void> {
    await interaction.reply({
      content: '🚨 **Priority Support**\n\nEnterprise support ticket system. Coming soon!',
      ephemeral: true
    });
  }

  private async showCustomIntegrationForm(interaction: ButtonInteraction): Promise<void> {
    await interaction.reply({
      content: '⚙️ **Custom Integration**\n\nRequest custom integrations and features. Coming soon!',
      ephemeral: true
    });
  }

  private async showEnterpriseAnalytics(interaction: ButtonInteraction): Promise<void> {
    await interaction.reply({
      content: '📊 **Enterprise Analytics**\n\nAdvanced analytics dashboard. Coming soon!',
      ephemeral: true
    });
  }

  private async showWhiteLabelOptions(interaction: ButtonInteraction): Promise<void> {
    await interaction.reply({
      content: '🏷️ **White Label Setup**\n\nCustomize the platform with your branding. Coming soon!',
      ephemeral: true
    });
  }

  // Business Action Implementations
  private async handleBusinessAction(
    interaction: ButtonInteraction,
    action: string,
    config: any
  ): Promise<void> {
    switch (action) {
      case 'talks':
        await this.showBusinessTalks(interaction);
        break;
      
      case 'war_room':
        await this.showWarRoomEntry(interaction);
        break;
      
      case 'clippers':
        await this.showClippersSection(interaction);
        break;
      
      case 'networking':
        await this.showNetworkingConnect(interaction);
        break;

      default:
        await interaction.reply({
          content: `❌ Unknown business action: ${action}`,
          ephemeral: true
        });
    }
  }

  private async showBusinessTalks(interaction: ButtonInteraction): Promise<void> {
    const embed = new EmbedBuilder()
      .setTitle('💼 Business Talks Hub')
      .setDescription('Join strategic business discussions and learn from industry experts')
      .setColor(0x8B5CF6)
      .addFields([
        { name: '🎯 Active Topics', value: '• Scaling Strategies\n• Market Analysis\n• Investment Opportunities', inline: true },
        { name: '👥 Participants', value: '12 active members', inline: true },
        { name: '📊 Success Rate', value: '89% implementation', inline: true }
      ])
      .setFooter({ text: 'Share your business insights and learn from others' });

    await interaction.reply({ embeds: [embed], ephemeral: true });
  }

  private async showWarRoomEntry(interaction: ButtonInteraction): Promise<void> {
    const embed = new EmbedBuilder()
      .setTitle('⚔️ War Room Access')
      .setDescription('Enter intensive strategy sessions for serious entrepreneurs')
      .setColor(0xEF4444)
      .addFields([
        { name: '🔥 Current Sessions', value: '• Q4 Revenue Push\n• Market Domination\n• Crisis Management', inline: false },
        { name: '⚠️ Warning', value: 'High-intensity environment. Bring your A-game.', inline: false }
      ])
      .setFooter({ text: 'Only for serious business builders' });

    await interaction.reply({ embeds: [embed], ephemeral: true });
  }

  private async showClippersSection(interaction: ButtonInteraction): Promise<void> {
    const embed = new EmbedBuilder()
      .setTitle('✂️ Clippers Hub')
      .setDescription('Content creation tools and viral marketing strategies')
      .setColor(0x06B6D4)
      .addFields([
        { name: '🎬 Tools Available', value: '• Video editing guides\n• Viral hooks library\n• Trend analysis', inline: true },
        { name: '📈 Success Metrics', value: '2.4M+ views generated', inline: true },
        { name: '🔥 Trending', value: '#EntrepreneurLife', inline: true }
      ])
      .setFooter({ text: 'Turn your content into cash flow' });

    await interaction.reply({ embeds: [embed], ephemeral: true });
  }

  private async showNetworkingConnect(interaction: ButtonInteraction): Promise<void> {
    const embed = new EmbedBuilder()
      .setTitle('🌐 Networking Connect')
      .setDescription('Connect with like-minded entrepreneurs and business partners')
      .setColor(0x10B981)
      .addFields([
        { name: '👥 Network Size', value: '500+ entrepreneurs', inline: true },
        { name: '🤝 Active Partnerships', value: '47 this month', inline: true },
        { name: '💰 Deals Closed', value: '$2.1M+ combined', inline: true },
        { name: '🎯 Connect Based On', value: '• Industry\n• Location\n• Investment level\n• Skills needed', inline: false }
      ])
      .setFooter({ text: 'Your network is your net worth' });

    await interaction.reply({ embeds: [embed], ephemeral: true });
  }

  // AI Agent Action Implementations
  private async handleAIAgentAction(
    interaction: ButtonInteraction,
    action: string,
    config: any
  ): Promise<void> {
    switch (action) {
      case 'personal':
        await this.showPersonalAgent(interaction);
        break;
      
      case 'memory':
        await this.showAgentMemory(interaction);
        break;
      
      case 'byok_setup':
        await this.showBYOKSetup(interaction);
        break;
      
      case 'evolution':
        await this.showAgentEvolution(interaction);
        break;

      default:
        await interaction.reply({
          content: `❌ Unknown AI agent action: ${action}`,
          ephemeral: true
        });
    }
  }

  private async showPersonalAgent(interaction: ButtonInteraction): Promise<void> {
    const embed = new EmbedBuilder()
      .setTitle('🤖 Personal AI Agent')
      .setDescription('Your dedicated AI assistant with complete data isolation')
      .setColor(0xFF6B9D)
      .addFields([
        { name: '🧠 Agent Status', value: 'Active & Learning', inline: true },
        { name: '📚 Knowledge Base', value: 'Personalized for you', inline: true },
        { name: '🔒 Privacy', value: '100% Isolated', inline: true },
        { name: '💬 Interaction Stats', value: '• 247 conversations\n• 89% satisfaction\n• 15 goals achieved', inline: false }
      ])
      .setFooter({ text: 'Your AI agent learns exclusively from your interactions' });

    await interaction.reply({ embeds: [embed], ephemeral: true });
  }

  private async showAgentMemory(interaction: ButtonInteraction): Promise<void> {
    const embed = new EmbedBuilder()
      .setTitle('🧠 Agent Memory System')
      .setDescription('Track your AI agent\'s learning and memory development')
      .setColor(0x3B82F6)
      .addFields([
        { name: '📊 Memory Stats', value: '• 1,247 concepts learned\n• 89 personal preferences\n• 23 recurring patterns', inline: false },
        { name: '🎯 Recent Learning', value: '• Your communication style\n• Project preferences\n• Success patterns', inline: false },
        { name: '🔄 Memory Updates', value: 'Live learning enabled', inline: true }
      ])
      .setFooter({ text: 'Your agent\'s memory is private and grows with every interaction' });

    await interaction.reply({ embeds: [embed], ephemeral: true });
  }

  private async showBYOKSetup(interaction: ButtonInteraction): Promise<void> {
    const embed = new EmbedBuilder()
      .setTitle('🔑 BYOK (Bring Your Own Key) Setup')
      .setDescription('Configure your personal API keys for enhanced AI capabilities')
      .setColor(0xF59E0B)
      .addFields([
        { name: '🔐 Supported Providers', value: '• OpenAI GPT-4\n• Anthropic Claude\n• Google PaLM\n• Custom endpoints', inline: false },
        { name: '✅ Benefits', value: '• Higher rate limits\n• Priority access\n• Custom model selection\n• Cost control', inline: false },
        { name: '🛡️ Security', value: 'Keys encrypted and isolated per member', inline: true }
      ])
      .setFooter({ text: 'Your keys, your control, your privacy' });

    const actionRow = new ActionRowBuilder<ButtonBuilder>()
      .addComponents(
        new ButtonBuilder()
          .setCustomId(`byok_modal:${interaction.user.id}`)
          .setLabel('Configure API Keys')
          .setStyle(ButtonStyle.Primary)
          .setEmoji('🔑'),
        new ButtonBuilder()
          .setCustomId(`byok_status:${interaction.user.id}`)
          .setLabel('View Status')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('📊')
      );

    await interaction.reply({ 
      embeds: [embed], 
      components: [actionRow],
      ephemeral: true 
    });
  }

  private async showAgentEvolution(interaction: ButtonInteraction): Promise<void> {
    const embed = new EmbedBuilder()
      .setTitle('📈 Agent Evolution Tracker')
      .setDescription('Monitor your AI agent\'s development and capabilities growth')
      .setColor(0x10B981)
      .addFields([
        { name: '🏆 Evolution Stage', value: 'Advanced (Level 4)', inline: true },
        { name: '⭐ Capabilities', value: '15 unlocked', inline: true },
        { name: '🎯 Next Milestone', value: '89% complete', inline: true },
        { name: '📊 Growth Areas', value: '• Code understanding: 92%\n• Business strategy: 85%\n• Personal coaching: 94%', inline: false },
        { name: '🔓 Recent Unlocks', value: '• Advanced reasoning\n• Multi-context memory\n• Predictive insights', inline: false }
      ])
      .setFooter({ text: 'Your agent evolves based on your unique interaction patterns' });

    await interaction.reply({ embeds: [embed], ephemeral: true });
  }

  private async showBYOKModal(interaction: ButtonInteraction): Promise<void> {
    const modal = new ModalBuilder()
      .setCustomId(`byok_setup_modal:${interaction.user.id}`)
      .setTitle('🔑 BYOK - Configure Your API Keys');

    const openaiInput = new TextInputBuilder()
      .setCustomId('openai_key')
      .setLabel('OpenAI API Key (sk-...)')
      .setStyle(TextInputStyle.Short)
      .setPlaceholder('sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx')
      .setRequired(false)
      .setMinLength(20);

    const anthropicInput = new TextInputBuilder()
      .setCustomId('anthropic_key')
      .setLabel('Anthropic API Key (sk-ant-...)')
      .setStyle(TextInputStyle.Short)
      .setPlaceholder('sk-ant-api03-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx')
      .setRequired(false)
      .setMinLength(20);

    const googleInput = new TextInputBuilder()
      .setCustomId('google_key')
      .setLabel('Google AI API Key')
      .setStyle(TextInputStyle.Short)
      .setPlaceholder('AIxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx')
      .setRequired(false)
      .setMinLength(20);

    const actionRow1 = new ActionRowBuilder<TextInputBuilder>().addComponents(openaiInput);
    const actionRow2 = new ActionRowBuilder<TextInputBuilder>().addComponents(anthropicInput);
    const actionRow3 = new ActionRowBuilder<TextInputBuilder>().addComponents(googleInput);

    modal.addComponents(actionRow1, actionRow2, actionRow3);

    await interaction.showModal(modal);
  }

  private async showBYOKStatus(interaction: ButtonInteraction): Promise<void> {
    const memberId = interaction.user.id;
    
    try {
      const userKeys = await this.byokService.getUserKeys(memberId);
      const availableModels = await this.byokService.getAvailableModels(memberId);

      const openaiStatus = userKeys.keys.openai?.isValid ? '✅ Configured' : '❌ Not configured';
      const anthropicStatus = userKeys.keys.anthropic?.isValid ? '✅ Configured' : '❌ Not configured';
      const googleStatus = userKeys.keys.google?.isValid ? '✅ Configured' : '❌ Not configured';
      const exaStatus = userKeys.keys.exa?.isValid ? '✅ Configured' : '❌ Not configured';

      const embed = new EmbedBuilder()
        .setTitle('🔑 BYOK Status')
        .setDescription('Your current API key configuration')
        .setColor(0x3B82F6)
        .addFields([
          { name: '🤖 OpenAI', value: openaiStatus, inline: true },
          { name: '🎯 Anthropic', value: anthropicStatus, inline: true },
          { name: '🌐 Google', value: googleStatus, inline: true },
          { name: '🔍 Exa (Web Search)', value: exaStatus, inline: true }
        ]);

      if (availableModels.length > 0) {
        const modelList = availableModels.slice(0, 5).map((m: { name: any; }) => `• ${m.name}`).join('\n');
        if (availableModels.length > 5) {
          embed.addFields([
            { name: '🚀 Available Models', value: `${modelList}\n...and ${availableModels.length - 5} more`, inline: false }
          ]);
        } else {
          embed.addFields([
            { name: '🚀 Available Models', value: modelList, inline: false }
          ]);
        }
      }

      const dailyUsage = userKeys.usage.daily;
      embed.addFields([
        { name: '📊 Usage Stats', value: `Daily: ${dailyUsage.tokens} tokens`, inline: true },
        { name: '💰 Cost Tracking', value: `$${dailyUsage.cost.toFixed(2)} today`, inline: true }
      ])
      .setFooter({ text: 'Use "Configure API Keys" to update your providers' });

      const actionRow = new ActionRowBuilder<ButtonBuilder>()
        .addComponents(
          new ButtonBuilder()
            .setCustomId(`byok_modal:${interaction.user.id}`)
            .setLabel('Update Keys')
            .setStyle(ButtonStyle.Primary)
            .setEmoji('🔄')
        );

      await interaction.reply({ 
        embeds: [embed], 
        components: [actionRow],
        ephemeral: true 
      });

    } catch (error) {
      this.logger.error(`Failed to get BYOK status for ${memberId}:`, error);
      await interaction.reply({
        content: '❌ Failed to retrieve your BYOK status.',
        ephemeral: true
      });
    }
  }
}
