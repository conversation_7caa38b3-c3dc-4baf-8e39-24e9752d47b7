import { DatabaseModule } from '@/core/database';
import { RedisDatabaseModule } from '@/core/database/redis-database.module';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ChannelFilterService } from './channel-filter.service';
import { MembershipService } from './membership.service';
import { WhopService } from './whop.service';

@Module({
  imports: [ConfigModule, DatabaseModule, RedisDatabaseModule],
  providers: [
    WhopService,
    MembershipService,
    ChannelFilterService
  ],
  exports: [
    MembershipService,
    ChannelFilterService
  ]
})
export class ServicesModule {}