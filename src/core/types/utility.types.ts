/**
 * Core utility types for the EnergeX Discord bot
 * Provides type-safe utility functions and common patterns
 */


/**
 * Generic utility function types
 */
export type Predicate<T> = (item: T) => boolean;
export type Mapper<T, U> = (item: T) => U;
export type Reducer<T, U> = (acc: U, item: T, index: number) => U;

/**
 * Validation result types
 */
export interface ValidationResult<T = any> {
  valid: boolean;
  data?: T;
  errors: ValidationError[];
  warnings?: ValidationWarning[];
}

export interface ValidationError {
  field?: string;
  message: string;
  code: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

export interface ValidationWarning {
  field?: string;
  message: string;
  code: string;
}

/**
 * Data transformation types
 */
export interface TransformConfig<T, U> {
  source: T;
  target?: Partial<U>;
  rules?: TransformRule<T, U>[];
  options?: TransformOptions;
}

export interface TransformRule<T, U> {
  sourceField: keyof T;
  targetField: keyof U;
  transformer?: (value: T[keyof T]) => U[keyof U];
  condition?: (source: T) => boolean;
}

export interface TransformOptions {
  strict?: boolean;
  ignoreUndefined?: boolean;
  ignoreFunctions?: boolean;
  maxDepth?: number;
}

/**
 * Format utility types
 */
export type FormatType = 
  | 'currency'
  | 'percentage'
  | 'date'
  | 'time'
  | 'datetime'
  | 'duration'
  | 'bytes'
  | 'number';

export interface FormatOptions {
  locale?: string;
  currency?: string;
  precision?: number;
  timezone?: string;
  style?: 'short' | 'long' | 'narrow';
}

/**
 * Discord-specific utility types
 */
export interface DiscordUserInfo {
  id: string;
  tag: string;
  username: string;
  discriminator: string;
  avatarUrl: string;
  createdAt: Date;
  joinedAt?: Date;
  roles?: string[];
  permissions?: PermissionString[];
  isBot: boolean;
  isSystem: boolean;
}

export interface DiscordGuildInfo {
  id: string;
  name: string;
  memberCount: number;
  createdAt: Date;
  ownerId: string;
  features: string[];
  boostLevel: number;
  boostCount: number;
  description?: string;
  icon?: string;
  banner?: string;
}

export interface DiscordChannelInfo {
  id: string;
  name: string;
  type: number;
  position?: number;
  topic?: string;
  nsfw?: boolean;
  parentId?: string;
  permissions?: PermissionString[];
}

export interface DiscordRoleInfo {
  id: string;
  name: string;
  color: string;
  position: number;
  permissions: PermissionString[];
  mentionable: boolean;
  hoisted: boolean;
  managed: boolean;
}

/**
 * Permission utility types
 */
export interface PermissionCheck {
  userId: string;
  guildId: string;
  requiredPermissions: PermissionString[];
  channelId?: string;
}

export interface PermissionResult {
  hasPermission: boolean;
  missingPermissions: PermissionString[];
  grantedPermissions: PermissionString[];
  isOwner: boolean;
  isAdmin: boolean;
}

/**
 * Cache utility types
 */
export interface CacheEntry<T = any> {
  key: string;
  value: T;
  ttl?: number;
  createdAt: Date;
  expiresAt?: Date;
  tags?: string[];
}

export interface CacheOptions {
  ttl?: number;
  tags?: string[];
  serialize?: boolean;
  compress?: boolean;
}

export interface CacheStats {
  totalKeys: number;
  totalMemory: string;
  hitRate: number;
  missRate: number;
  keysByType: Record<string, number>;
  keysByPattern: Record<string, number>;
}

/**
 * Rate limiting types
 */
export interface RateLimitConfig {
  windowMs: number;
  maxRequests: number;
  keyGenerator?: (context: any) => string;
  skipIf?: (context: any) => boolean;
  onLimitReached?: (context: any) => Promise<void>;
}

export interface RateLimitStatus {
  remaining: number;
  reset: Date;
  limit: number;
  used: number;
}

/**
 * Utility service interfaces
 */
export interface IValidationService {
  validate<T>(data: unknown, schema: ValidationSchema<T>): Promise<ValidationResult<T>>;
  validateSync<T>(data: unknown, schema: ValidationSchema<T>): ValidationResult<T>;
  createSchema<T>(definition: SchemaDefinition<T>): ValidationSchema<T>;
  addCustomValidator(name: string, validator: CustomValidator): void;
}

export interface IFormatService {
  format(value: any, type: FormatType, options?: FormatOptions): string;
  formatCurrency(amount: number, currency?: string, locale?: string): string;
  formatDate(date: Date, style?: 'short' | 'medium' | 'long' | 'full', locale?: string): string;
  formatDuration(milliseconds: number, style?: 'short' | 'long'): string;
  formatBytes(bytes: number, decimals?: number): string;
  formatPercentage(value: number, decimals?: number, locale?: string): string;
}

export interface ITransformService {
  transform<T, U>(config: TransformConfig<T, U>): Promise<U>;
  transformSync<T, U>(config: TransformConfig<T, U>): U;
  mapKeys<T>(obj: Record<string, T>, mapper: (key: string) => string): Record<string, T>;
  mapValues<T, U>(obj: Record<string, T>, mapper: (value: T, key: string) => U): Record<string, U>;
  deepClone<T>(obj: T): T;
  merge<T>(target: T, ...sources: Partial<T>[]): T;
}

export interface IPermissionService {
  checkPermissions(check: PermissionCheck): Promise<PermissionResult>;
  hasPermission(userId: string, guildId: string, permission: PermissionString): Promise<boolean>;
  hasAnyPermission(userId: string, guildId: string, permissions: PermissionString[]): Promise<boolean>;
  hasAllPermissions(userId: string, guildId: string, permissions: PermissionString[]): Promise<boolean>;
  getUserPermissions(userId: string, guildId: string, channelId?: string): Promise<PermissionString[]>;
}

/**
 * Validation schema types
 */
export interface ValidationSchema<T = any> {
  type: 'object' | 'array' | 'string' | 'number' | 'boolean' | 'date';
  properties?: Record<keyof T, ValidationRule>;
  items?: ValidationRule;
  required?: (keyof T)[];
  optional?: (keyof T)[];
  custom?: CustomValidator[];
}

export interface ValidationRule {
  type: 'string' | 'number' | 'boolean' | 'date' | 'array' | 'object' | 'any';
  required?: boolean;
  min?: number;
  max?: number;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  enum?: any[];
  custom?: CustomValidator[];
  transform?: (value: any) => any;
}

export type CustomValidator = (value: any, context?: any) => ValidationError | null;

export type SchemaDefinition<T> = {
  [K in keyof T]: ValidationRule;
};

/**
 * Error handling types
 */
export interface UtilityError extends Error {
  code: string;
  context?: Record<string, any>;
  severity: 'low' | 'medium' | 'high' | 'critical';
  recoverable: boolean;
}

export class ValidationError extends Error implements UtilityError {
  public readonly code: string;
  public readonly context?: Record<string, any>;
  public readonly severity: 'low' | 'medium' | 'high' | 'critical';
  public readonly recoverable: boolean;

  constructor(
    message: string,
    code: string = 'VALIDATION_ERROR',
    context?: Record<string, any>,
    severity: 'low' | 'medium' | 'high' | 'critical' = 'medium'
  ) {
    super(message);
    this.name = 'ValidationError';
    this.code = code;
    this.context = context;
    this.severity = severity;
    this.recoverable = severity !== 'critical';
  }
}

export class TransformError extends Error implements UtilityError {
  public readonly code: string;
  public readonly context?: Record<string, any>;
  public readonly severity: 'low' | 'medium' | 'high' | 'critical';
  public readonly recoverable: boolean;

  constructor(
    message: string,
    code: string = 'TRANSFORM_ERROR',
    context?: Record<string, any>,
    severity: 'low' | 'medium' | 'high' | 'critical' = 'medium'
  ) {
    super(message);
    this.name = 'TransformError';
    this.code = code;
    this.context = context;
    this.severity = severity;
    this.recoverable = severity !== 'critical';
  }
}

/**
 * Performance monitoring types
 */
export interface PerformanceMetric {
  name: string;
  duration: number;
  startTime: number;
  endTime: number;
  metadata?: Record<string, any>;
}

export interface PerformanceTracker {
  start(name: string, metadata?: Record<string, any>): void;
  end(name: string): PerformanceMetric;
  measure<T>(name: string, fn: () => T, metadata?: Record<string, any>): T;
  measureAsync<T>(name: string, fn: () => Promise<T>, metadata?: Record<string, any>): Promise<T>;
}

/**
 * Utility configuration types
 */
export interface UtilityConfig {
  cache: {
    defaultTtl: number;
    maxMemory: string;
    compressionEnabled: boolean;
  };
  validation: {
    strictMode: boolean;
    throwOnError: boolean;
    customValidators: Record<string, CustomValidator>;
  };
  formatting: {
    defaultLocale: string;
    defaultCurrency: string;
    defaultTimezone: string;
  };
  performance: {
    trackingEnabled: boolean;
    slowOperationThreshold: number;
    metricsRetention: number;
  };
  rateLimit: {
    defaultWindow: number;
    defaultLimit: number;
    globalLimit: number;
  };
}

/**
 * Service health types
 */
export interface ServiceHealth {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: Date;
  uptime: number;
  version: string;
  dependencies: HealthCheck[];
  metrics: HealthMetric[];
}

export interface HealthCheck {
  name: string;
  status: 'up' | 'down' | 'unknown';
  responseTime?: number;
  error?: string;
  metadata?: Record<string, any>;
}

export interface HealthMetric {
  name: string;
  value: number;
  unit: string;
  threshold?: number;
  status: 'normal' | 'warning' | 'critical';
}

/**
 * Export all types
 */
export type * from './object-utils.types';

