import { DatabaseModule } from '@/core/database';
import { RedisDatabaseModule } from '@/core/database/redis-database.module';
import { Module } from '@nestjs/common';
import { EncryptionService } from './encryption.service';
import { SessionService } from './session.service';
import { UserService } from './user.service';

@Module({
  imports: [DatabaseModule, RedisDatabaseModule],
  providers: [EncryptionService, SessionService, UserService],
  exports: [EncryptionService, SessionService, UserService],
})
export class SecurityModule {}