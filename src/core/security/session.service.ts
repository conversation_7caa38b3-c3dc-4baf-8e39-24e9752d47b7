import { Session } from '@/core/database';
import { SessionRepository } from '@/core/database/repositories/session.repository';
import { Injectable, Logger } from '@nestjs/common';
import { Request } from 'express';
import { EncryptionService } from './encryption.service';

@Injectable()
export class SessionService {
  private readonly logger = new Logger(SessionService.name);

  constructor(
    private readonly sessionRepository: SessionRepository,
    private readonly encryptionService: EncryptionService,
  ) {}

  async createSession(
    userId: string,
    req: Request,
    expiresIn: number = 24 * 60 * 60 * 1000, // 24 hours
  ): Promise<Session> {
    try {
      const sessionId = this.encryptionService.generateSessionId();
      const expiresAt = new Date(Date.now() + expiresIn);
      const ipAddress = req.ip || req.connection?.remoteAddress || 'unknown';
      const userAgent = req.get('User-Agent') || 'unknown';
      const deviceFingerprint = this.encryptionService.generateDeviceFingerprint(
        userAgent,
        ipAddress,
      );

      const newSession = {
        sessionId,
        userId,
        expiresAt,
        ipAddress,
        userAgent,
        deviceFingerprint,
        lastAccessedAt: new Date(),
        isRevoked: false,
      };

      return await this.sessionRepository.createSession({
        sessionId,
        userId,
        ipAddress,
        userAgent,
        deviceFingerprint,
        expiryHours: expiresIn / (60 * 60 * 1000) // Convert milliseconds to hours
      });
    } catch (error) {
      this.logger.error('Failed to create session:', error);
      throw new Error('Session creation failed');
    }
  }

  async validateSession(sessionId: string, req: Request): Promise<(Session & { user?: any }) | null> {
    try {
      const session = await this.sessionRepository.findBySessionId(sessionId);

      if (!session) {
        return null;
      }

      // Check if session is expired
      if (new Date(session.expiresAt) < new Date()) {
        await this.revokeSession(sessionId);
        return null;
      }

      // Check if session is revoked
      if (session.isRevoked) {
        return null;
      }

      // Update last accessed time
      await this.sessionRepository.update(session.id, {
        lastAccessedAt: new Date()
      });

      return session;
    } catch (error) {
      this.logger.error('Failed to validate session:', error);
      return null;
    }
  }

  async revokeSession(sessionId: string): Promise<void> {
    try {
      await this.sessionRepository.revokeSession(sessionId);
    } catch (error) {
      this.logger.error('Failed to revoke session:', error);
    }
  }

  async cleanupExpiredSessions(): Promise<number> {
    try {
      return await this.sessionRepository.cleanupExpiredSessions();
    } catch (error) {
      this.logger.error('Failed to cleanup expired sessions:', error);
      return 0;
    }
  }
}