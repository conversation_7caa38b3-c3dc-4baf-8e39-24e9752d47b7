import { Injectable } from '@nestjs/common';
import { Redis } from 'ioredis';
import { BaseRedisRepositoryImpl } from './base-redis.repository';
import { RepositoryOptions } from '../types/index';
import { 
  Organization, 
  CreateOrganization, 
  UpdateOrganization, 
  OrganizationKeys,
  OrganizationTier,
  OrganizationStatus 
} from '../entities/organization.entity';

/**
 * Organization repository with Redis implementation
 * Handles organization CRUD operations and business logic
 */
@Injectable()
export class OrganizationRepository extends BaseRedisRepositoryImpl<Organization> {
  constructor(redis: Redis) {
    const options: RepositoryOptions = {
      enableCaching: true,
      defaultTTL: 3600, // 1 hour
      enableSoftDelete: true,
      enableVersioning: false,
      enableAudit: true,
      compressionEnabled: false,
      serializationStrategy: 'json',
      indexFields: ['slug', 'tier', 'status', 'ownerId', 'isActive'],
      searchFields: ['name', 'slug'],
    };

    super(redis, 'organization', OrganizationKeys, options);
  }

  /**
   * Find organization by slug
   */
  async findBySlug(slug: string): Promise<Organization | null> {
    try {
      const organizations = await this.findByField('slug', slug);
      return organizations.length > 0 ? organizations[0] || null : null;
    } catch (error) {
      this.logger.error(`Failed to find organization by slug: ${slug}`, error);
      return null;
    }
  }

  /**
   * Find organizations by owner
   */
  async findByOwner(ownerId: string): Promise<Organization[]> {
    try {
      const result = await this.findByField('ownerId', ownerId);
      return [...result];
    } catch (error) {
      this.logger.error(`Failed to find organizations by owner: ${ownerId}`, error);
      return [];
    }
  }

  /**
   * Find organizations by tier
   */
  async findByTier(tier: OrganizationTier): Promise<Organization[]> {
    try {
      const result = await this.findByField('tier', tier);
      return [...result];
    } catch (error) {
      this.logger.error(`Failed to find organizations by tier: ${tier}`, error);
      return [];
    }
  }

  /**
   * Find organizations by status
   */
  async findByStatus(status: OrganizationStatus): Promise<Organization[]> {
    try {
      const result = await this.findByField('status', status);
      return [...result];
    } catch (error) {
      this.logger.error(`Failed to find organizations by status: ${status}`, error);
      return [];
    }
  }

  /**
   * Find active organizations
   */
  async findActiveOrganizations(): Promise<Organization[]> {
    try {
      const result = await this.findByField('isActive', true);
      return [...result];
    } catch (error) {
      this.logger.error('Failed to find active organizations', error);
      return [];
    }
  }

  /**
   * Get organization statistics
   */
  async getStats(): Promise<{
    total: number;
    byTier: Record<OrganizationTier, number>;
    byStatus: Record<OrganizationStatus, number>;
    active: number;
  }> {
    try {
      const allOrgs = await this.findAll();
      
      const stats = {
        total: allOrgs.length,
        byTier: {} as Record<OrganizationTier, number>,
        byStatus: {} as Record<OrganizationStatus, number>,
        active: 0,
      };

      // Initialize counters
      const tiers: OrganizationTier[] = ['free', 'starter', 'professional', 'enterprise', 'custom'];
      const statuses: OrganizationStatus[] = ['active', 'suspended', 'trial', 'cancelled'];
      
      tiers.forEach(tier => stats.byTier[tier] = 0);
      statuses.forEach(status => stats.byStatus[status] = 0);

      // Count organizations
      allOrgs.forEach(org => {
        stats.byTier[org.tier]++;
        stats.byStatus[org.status]++;
        if (org.isActive) stats.active++;
      });

      return stats;
    } catch (error) {
      this.logger.error('Failed to get organization stats', error);
      return {
        total: 0,
        byTier: {} as Record<OrganizationTier, number>,
        byStatus: {} as Record<OrganizationStatus, number>,
        active: 0,
      };
    }
  }

  /**
   * Check if slug is available
   */
  async isSlugAvailable(slug: string, excludeId?: string): Promise<boolean> {
    try {
      const existing = await this.findBySlug(slug);
      if (!existing) return true;
      if (excludeId && existing.id === excludeId) return true;
      return false;
    } catch (error) {
      this.logger.error(`Failed to check slug availability: ${slug}`, error);
      return false;
    }
  }

  /**
   * Generate unique slug from name
   */
  async generateSlug(name: string): Promise<string> {
    let baseSlug = name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '');

    let slug = baseSlug;
    let counter = 1;

    while (!(await this.isSlugAvailable(slug))) {
      slug = `${baseSlug}-${counter}`;
      counter++;
    }

    return slug;
  }

  /**
   * Update organization settings
   */
  async updateSettings(id: string, settings: Partial<Organization['config']>): Promise<Organization | null> {
    try {
      const org = await this.findById(id);
      if (!org) return null;

      const updatedSettings = {
        ...org.config,
        ...settings
      };

      return this.update(id, { config: updatedSettings } as any);
    } catch (error) {
      this.logger.error(`Failed to update organization settings: ${id}`, error);
      return null;
    }
  }

  /**
   * Update organization limits
   */
  async updateLimits(id: string, limits: Partial<Organization['limits']>): Promise<Organization | null> {
    try {
      const org = await this.findById(id);
      if (!org) return null;

      const updatedLimits = {
        ...org.limits,
        ...limits
      };

      return this.update(id, { limits: updatedLimits } as any);
    } catch (error) {
      this.logger.error(`Failed to update organization limits: ${id}`, error);
      return null;
    }
  }

  /**
   * Update organization billing
   */
  async updateBilling(id: string, billing: Partial<Organization['billing']>): Promise<Organization | null> {
    try {
      const org = await this.findById(id);
      if (!org) return null;

      const updatedBilling = {
        ...org.billing,
        ...billing
      };

      return this.update(id, { billing: updatedBilling } as any);
    } catch (error) {
      this.logger.error(`Failed to update organization billing: ${id}`, error);
      return null;
    }
  }

  /**
   * Paginated search with filters
   */
  async findWithPagination(options: {
    page?: number;
    limit?: number;
    tier?: OrganizationTier;
    status?: OrganizationStatus;
    search?: string;
  } = {}): Promise<{
    organizations: Organization[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    try {
      const page = options.page || 1;
      const limit = options.limit || 20;
      const offset = (page - 1) * limit;

      let allOrgs = await this.findAll();

      // Apply filters
      if (options.tier) {
        allOrgs = allOrgs.filter(org => org.tier === options.tier);
      }
      if (options.status) {
        allOrgs = allOrgs.filter(org => org.status === options.status);
      }
      if (options.search) {
        const searchLower = options.search.toLowerCase();
        allOrgs = allOrgs.filter(org => 
          org.name.toLowerCase().includes(searchLower) ||
          org.slug.toLowerCase().includes(searchLower)
        );
      }

      const total = allOrgs.length;
      const totalPages = Math.ceil(total / limit);
      
      // Sort by creation date (newest first)
      const sortedOrgs = [...allOrgs].sort((a, b) => 
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      );

      // Apply pagination
      const organizations = sortedOrgs.slice(offset, offset + limit);

      return {
        organizations,
        total,
        page,
        limit,
        totalPages,
      };
    } catch (error) {
      this.logger.error('Failed to find organizations with pagination', error);
      return {
        organizations: [],
        total: 0,
        page: 1,
        limit: 20,
        totalPages: 0,
      };
    }
  }
}
