/**
 * Redis-specific types for type-safe operations
 */

/**
 * Redis configuration interface that matches ioredis RedisOptions
 * Based on ioredis v5.6.1 RedisOptions interface
 */
export interface RedisConfig {
  host?: string;
  port?: number;
  password?: string;
  username?: string;
  db?: number;
  url?: string;
  
  // Connection options
  connectTimeout?: number;
  commandTimeout?: number;
  socketTimeout?: number;
  lazyConnect?: boolean;
  keepAlive?: number;
  noDelay?: boolean;
  connectionName?: string;
  family?: number;
  
  // Retry and resilience options
  retryStrategy?: (times: number) => number | void | null;
  maxRetriesPerRequest?: number | null;
  reconnectOnError?: ((err: Error) => boolean | 1 | 2) | null;
  
  // Queue and command options
  enableOfflineQueue?: boolean;
  enableReadyCheck?: boolean;
  maxLoadingRetryTime?: number;
  
  // Pub/Sub options
  autoResubscribe?: boolean;
  autoResendUnfulfilledCommands?: boolean;
  
  // Pipeline options
  enableAutoPipelining?: boolean;
  autoPipeliningIgnoredCommands?: string[];
  
  // Other options
  readOnly?: boolean;
  stringNumbers?: boolean;
  monitor?: boolean;
  offlineQueue?: boolean;
  commandQueue?: boolean;
}

/**
 * Base Redis entity with timestamps - strict typing
 */
export interface BaseRedisEntity {
  readonly id: string;
  readonly createdAt: Date;
  readonly updatedAt: Date;
  deletedAt?: Date | null;
}

/**
 * Redis key patterns for consistent naming
 */
export type RedisKeyPattern = 
  | `user:${string}`
  | `session:${string}`
  | `guild:${string}`
  | `agent:${string}`
  | `chat:${string}`
  | `panel:${string}`
  | `support:${string}`
  | `community:${string}`
  | `config:${string}`;

/**
 * Redis operation result types with strict constraints
 */
export interface RedisOperationResult<T = unknown> {
  readonly success: boolean;
  readonly data?: T;
  readonly error?: string;
  readonly timestamp?: Date;
  readonly operation?: string;
}

/**
 * Generic Redis repository operations with strict typing
 */
export interface RedisRepository<T extends BaseRedisEntity> {
  findById(id: string): Promise<T | null>;
  findByPattern(pattern: string): Promise<ReadonlyArray<T>>;
  findAll(): Promise<ReadonlyArray<T>>;
  create(data: Omit<T, 'id' | 'createdAt' | 'updatedAt'>): Promise<T>;
  update(id: string, data: Partial<Omit<T, 'id' | 'createdAt'>>): Promise<T | null>;
  delete(id: string): Promise<boolean>;
  softDelete(id: string): Promise<boolean>;
  exists(id: string): Promise<boolean>;
  count(): Promise<number>;
}

/**
 * Redis search options with proper typing
 */
export interface RedisSearchOptions {
  readonly limit?: number;
  readonly offset?: number;
  readonly sortBy?: string;
  readonly sortOrder?: 'ASC' | 'DESC';
  readonly filters?: Readonly<Record<string, unknown>>;
}

/**
 * Redis transaction context
 */
export interface RedisTransactionContext {
  multi(): RedisTransaction;
}

/**
 * Redis transaction operations
 */
export interface RedisTransaction {
  pipeline?: any; // Pipeline object for transaction operations
  set?(key: string, value: string | object): RedisTransaction;
  get?(key: string): RedisTransaction;
  del?(key: string): RedisTransaction;
  exists?(key: string): RedisTransaction;
  expire?(key: string, seconds: number): RedisTransaction;
  exec(): Promise<ReadonlyArray<unknown>>;
}

/**
 * Redis cache options
 */
export interface RedisCacheOptions {
  ttl?: number; // Time to live in seconds
  compress?: boolean;
  serialize?: boolean;
}

/**
 * Redis pub/sub types
 */
export interface RedisPubSubMessage<T = any> {
  channel: string;
  pattern?: string;
  data: T;
  timestamp: Date;
}

export type RedisEventListener<T = any> = (message: RedisPubSubMessage<T>) => void;

/**
 * Redis collection types for different data structures with strict typing
 */
export type RedisValue = string | number | boolean | Record<string, unknown> | null | undefined;
export type RedisHash = Readonly<Record<string, RedisValue>>;
export type RedisList = ReadonlyArray<RedisValue>;
export type RedisSet = ReadonlySet<RedisValue>;
export type RedisSortedSet = ReadonlyMap<RedisValue, number>;

/**
 * Redis error types
 */
export class RedisError extends Error {
  constructor(
    message: string,
    public readonly code?: string,
    public readonly operation?: string
  ) {
    super(message);
    this.name = 'RedisError';
  }
}

export class RedisConnectionError extends RedisError {
  constructor(message: string) {
    super(message, 'CONNECTION_ERROR');
    this.name = 'RedisConnectionError';
  }
}

export class RedisTimeoutError extends RedisError {
  constructor(operation: string) {
    super(`Operation ${operation} timed out`, 'TIMEOUT_ERROR', operation);
    this.name = 'RedisTimeoutError';
  }
}

export class RedisSerializationError extends RedisError {
  constructor(message: string, operation?: string) {
    super(message, 'SERIALIZATION_ERROR', operation);
    this.name = 'RedisSerializationError';
  }
}

/**
 * Redis configuration interface
 */
export interface RedisConfig {
  host?: string;
  port?: number;
  password?: string;
  db?: number;
  url?: string;
  keyPrefix?: string;
  maxRetriesPerRequest?: number;
  retryDelayOnFailover?: number;
  enableReadyCheck?: boolean;
  lazyConnect?: boolean;
  keepAlive?: number;
  family?: number;
  connectTimeout?: number;
  commandTimeout?: number;
}

/**
 * Redis client interface
 */
export interface RedisClient {
  connect(): Promise<void>;
  disconnect(): Promise<void>;
  isConnected: boolean; // Property, not method
  getHealth(): Promise<RedisHealthCheck>;
  getMetrics(): Promise<RedisPerformanceMetrics>;
}

/**
 * Redis health check interface
 */
export interface RedisHealthCheck {
  status: 'healthy' | 'unhealthy' | 'degraded';
  latency: number;
  memoryUsage: number;
  memory?: {
    used: string;
    peak: string;
    fragmentation: number;
  };
  connectedClients: number;
  connections?: {
    active: number;
    total: number;
  };
  uptime: number;
  version?: string;
  lastCheck: Date;
}

/**
 * Redis performance metrics
 */
export interface RedisPerformanceMetrics {
  operationsPerSecond: number;
  averageLatency: number;
  memoryUsage: number;
  hitRate: number;
  missRate: number;
  evictedKeys: number;
  expiredKeys: number;
  operations?: any;
  performance?: any;
  memory?: any;
  connections?: any;
}

/**
 * Redis lock interface
 */
export interface RedisLock {
  key: string;
  value: string;
  ttl: number;
  acquired: boolean;
  acquiredAt?: Date;
  expiresAt?: Date;
  release(): Promise<boolean>;
  extend(ttl: number): Promise<boolean>;
}

/**
 * Redis lock manager interface
 */
export interface RedisLockManager {
  acquire(key: string, ttl: number, options?: RedisLockRetryOptions): Promise<RedisLock | null>;
  release(lock: RedisLock): Promise<boolean>;
  extend(lock: RedisLock, ttl: number): Promise<boolean>;
  isLocked(key: string): Promise<boolean>;
}

/**
 * Redis lock retry options
 */
export interface RedisLockRetryOptions {
  retries?: number;
  delay?: number;
  factor?: number;
  maxDelay?: number;
}

/**
 * Redis pub/sub manager interface
 */
export interface RedisPubSubManager {
  subscribe(channel: string, listener: RedisEventListener): Promise<void>;
  unsubscribe(channel: string, listener?: RedisEventListener): Promise<void>;
  publish(channel: string, message: any): Promise<number>;
  psubscribe(pattern: string, listener: RedisEventListener): Promise<void>;
  punsubscribe(pattern: string, listener?: RedisEventListener): Promise<void>;
}

/**
 * Redis session storage interface
 */
export interface RedisSessionStorage {
  get(sessionId: string): Promise<any>;
  set(sessionId: string, data: any, ttl?: number): Promise<boolean>;
  delete(sessionId: string): Promise<boolean>;
  exists(sessionId: string): Promise<boolean>;
  touch(sessionId: string, ttl: number): Promise<boolean>;
}

/**
 * Redis migration types
 */
export interface RedisMigration {
  version: string;
  description: string;
  up: (redis: any) => Promise<void>;
  down: (redis: any) => Promise<void>;
}

export interface MigrationStatus {
  version: string;
  appliedAt: Date;
  success: boolean;
  error?: string;
}