import { Injectable, Logger } from '@nestjs/common';
import { NewUser, User } from '../../entities/user.entity';
import { BaseRedisRepository, RedisEntityConfig } from '../base-redis.repository';

export interface RedisUser extends Omit<User, 'id'> {
  id?: string;
  discordId: string;
}

@Injectable()
export class UserRedisRepository extends BaseRedisRepository<RedisUser> {
  protected config: RedisEntityConfig = {
    keyPrefix: 'user',
    indexes: ['username', 'email', 'isActive'],
    relationships: {
      // No direct relationships in base user entity
    },
    // ttl: undefined, // Users persist indefinitely - removed for exactOptionalPropertyTypes compliance
  };

  protected logger = new Logger(UserRedisRepository.name);

  // User-specific operations
  async createUser(userData: NewUser): Promise<RedisUser> {
    const id = userData.discordId;
    const now = new Date();
    const user = await this.create(id, {
      ...userData,
      experience: userData.experience || 0,
      balance: userData.balance || 0,
      isActive: userData.isActive ?? true,
      lastActivityAt: userData.lastActivityAt || now,
      createdAt: now,
      updatedAt: now,
    } as any);

    // Create username index for fast lookups
    await this.redis.hset('user:username_index', userData.username.toLowerCase(), id);

    // Initialize user's achievement set
    if (!await this.redis.exists(this.key(id, 'achievements'))) {
      await this.redis.sadd(this.key(id, 'achievements'), 'member'); // Default achievement
    }

    // Initialize user's guilds set
    await this.redis.sadd(this.key(id, 'guilds'), ''); // Empty set initially
    await this.redis.srem(this.key(id, 'guilds'), ''); // Remove empty string

    // Add to experience leaderboard if they have experience
    if (user.experience > 0) {
      await this.redis.zadd('leaderboard:global:experience', user.experience, id);
    }

    this.logger.log(`Created user: ${userData.username} (${id})`);
    return user;
  }

  async findByDiscordId(discordId: string): Promise<RedisUser | null> {
    return this.findById(discordId);
  }

  async findByUsername(username: string): Promise<RedisUser | null> {
    const discordId = await this.redis.hget('user:username_index', username.toLowerCase());
    if (!discordId) return null;
    return this.findById(discordId);
  }

  async findByEmail(email: string): Promise<RedisUser | null> {
    const users = await this.findByIndex('email', email);
    return users[0] || null;
  }

  async updateLastActivity(discordId: string): Promise<void> {
    await this.redis.hset(this.key(discordId), {
      lastActivityAt: new Date(),
      updatedAt: new Date(),
    });
  }

  async addExperience(discordId: string, amount: number): Promise<{ newTotal: number; levelUp: boolean }> {
    const multi = this.redis.multi();
    const userKey = this.key(discordId);
    
    // Get current experience
    const currentExp = await this.redis.hget(userKey, 'experience');
    const oldExp = parseInt(currentExp || '0');
    const newExp = oldExp + amount;
    
    // Calculate levels (1000 XP per level)
    const oldLevel = Math.floor(oldExp / 1000);
    const newLevel = Math.floor(newExp / 1000);
    const levelUp = newLevel > oldLevel;

    // Update user data
    multi.hset(userKey, {
      experience: newExp.toString(),
      updatedAt: new Date(),
    });

    // Update global leaderboard
    multi.zadd('leaderboard:global:experience', newExp, discordId);

    // If level up, update level-based leaderboard
    if (levelUp) {
      multi.zadd('leaderboard:global:level', newLevel, discordId);
    }

    await multi.exec();

    this.logger.debug(`Added ${amount} XP to ${discordId} (${oldExp} -> ${newExp})`);
    return { newTotal: newExp, levelUp };
  }

  async updateBalance(discordId: string, amount: number): Promise<number> {
    const newBalance = await this.redis.hincrby(this.key(discordId), 'balance', amount);
    await this.redis.hset(this.key(discordId), 'updatedAt', new Date().toISOString());

    this.logger.debug(`Updated balance for ${discordId}: ${amount > 0 ? '+' : ''}${amount}`);
    return newBalance;
  }

  async getBalance(discordId: string): Promise<number> {
    const balance = await this.redis.hget(this.key(discordId), 'balance');
    return parseInt(balance || '0');
  }

  // Achievement system
  async getUserAchievements(discordId: string): Promise<string[]> {
    return this.redis.smembers(this.key(discordId, 'achievements'));
  }

  async addAchievement(discordId: string, achievement: string): Promise<boolean> {
    const result = await this.redis.sadd(this.key(discordId, 'achievements'), achievement);
    
    if (result === 1) {
      // Log achievement in user's activity feed
      await this.addUserActivity(discordId, {
        type: 'achievement',
        achievement,
        timestamp: Date.now(),
      });
      
      this.logger.log(`User ${discordId} earned achievement: ${achievement}`);
    }
    
    return result === 1;
  }

  async hasAchievement(discordId: string, achievement: string): Promise<boolean> {
    return (await this.redis.sismember(this.key(discordId, 'achievements'), achievement)) === 1;
  }

  // User activity feed
  async addUserActivity(discordId: string, activity: any): Promise<void> {
    const activityData = {
      ...activity,
      id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: activity.timestamp || Date.now(),
    };

    await this.redis.lpush(
      this.key(discordId, 'activity'), 
      JSON.stringify(activityData)
    );
    
    // Keep only last 100 activities
    await this.redis.ltrim(this.key(discordId, 'activity'), 0, 99);
  }

  async getUserActivity(discordId: string, limit: number = 20): Promise<any[]> {
    const activities = await this.redis.lrange(this.key(discordId, 'activity'), 0, limit - 1);
    return activities.map((activity: any) => JSON.parse(activity));
  }

  // Guild membership
  async addUserToGuild(discordId: string, guildId: string): Promise<void> {
    const multi = this.redis.multi();
    
    // Add guild to user's guild set
    multi.sadd(this.key(discordId, 'guilds'), guildId);
    
    // Add user to guild's member set (handled by guild repository)
    // This is just for indexing purposes
    multi.sadd(`guild:${guildId}:members`, discordId);
    
    await multi.exec();
    
    await this.addUserActivity(discordId, {
      type: 'guild_joined',
      guildId,
    });
  }

  async removeUserFromGuild(discordId: string, guildId: string): Promise<void> {
    const multi = this.redis.multi();
    
    multi.srem(this.key(discordId, 'guilds'), guildId);
    multi.srem(`guild:${guildId}:members`, discordId);
    
    await multi.exec();
    
    await this.addUserActivity(discordId, {
      type: 'guild_left',
      guildId,
    });
  }

  async getUserGuilds(discordId: string): Promise<string[]> {
    return this.redis.smembers(this.key(discordId, 'guilds'));
  }

  // Leaderboard operations
  async getGlobalLeaderboard(type: 'experience' | 'level' = 'experience', limit: number = 10): Promise<Array<{ discordId: string; score: number; rank: number }>> {
    const results = await this.redis.zrevrange(
      `leaderboard:global:${type}`,
      0,
      limit - 1,
      'WITHSCORES'
    );

    const leaderboard: Array<{ discordId: string; score: number; rank: number }> = [];
    for (let i = 0; i < results.length; i += 2) {
      const discordId = results[i];
      const scoreStr = results[i + 1];
      if (discordId && scoreStr) {
        leaderboard.push({
          discordId,
          score: parseInt(scoreStr),
          rank: Math.floor(i / 2) + 1,
        });
      }
    }

    return leaderboard;
  }

  async getUserRank(discordId: string, type: 'experience' | 'level' = 'experience'): Promise<number | null> {
    const rank = await this.redis.zrevrank(`leaderboard:global:${type}`, discordId);
    return rank !== null ? rank + 1 : null; // Redis ranks are 0-based
  }

  async getUserScore(discordId: string, type: 'experience' | 'level' = 'experience'): Promise<number> {
    const score = await this.redis.zscore(`leaderboard:global:${type}`, discordId);
    return score ? parseInt(score) : 0;
  }

  // Search and filtering
  async searchUsers(query: string, limit: number = 10): Promise<RedisUser[]> {
    const searchTerm = query.toLowerCase();
    
    // Search by username (using username index)
    const usernames = await this.redis.hkeys('user:username_index');
    const matchingUsernames = usernames
      .filter((username: any) => username.includes(searchTerm))
      .slice(0, limit);
    
    const userIds = await this.redis.hmget('user:username_index', ...matchingUsernames);
    const validUserIds = userIds.filter((id: any) => id !== null) as string[];
    
    return this.findMany(validUserIds);
  }

  async getActiveUsers(since: Date, limit: number = 100): Promise<RedisUser[]> {
    const allUsers = await this.findAll();
    
    return allUsers
      .filter((user: any) => user.lastActivityAt && new Date(user.lastActivityAt) > since)
      .sort((a, b) => new Date(b.lastActivityAt!).getTime() - new Date(a.lastActivityAt!).getTime())
      .slice(0, limit);
  }

  // User statistics
  async getUserStats(discordId: string): Promise<{
    experience: number;
    level: number;
    balance: number;
    achievements: number;
    guilds: number;
    rank: number | null;
    joinedAt: Date | null;
    lastActivity: Date | null;
  }> {
    const user = await this.findById(discordId);
    if (!user) {
      throw new Error('User not found');
    }

    const [achievements, guilds, rank] = await Promise.all([
      this.getUserAchievements(discordId),
      this.getUserGuilds(discordId),
      this.getUserRank(discordId, 'experience'),
    ]);

    const experience = user.experience || 0;
    const level = Math.floor(experience / 1000);

    return {
      experience,
      level,
      balance: user.balance || 0,
      achievements: achievements.length,
      guilds: guilds.length,
      rank,
      joinedAt: user.createdAt ? new Date(user.createdAt) : null,
      lastActivity: user.lastActivityAt ? new Date(user.lastActivityAt) : null,
    };
  }

  // Bulk operations
  async updateMultipleUsers(updates: Array<{ discordId: string; data: Partial<RedisUser> }>): Promise<void> {
    const multi = this.redis.multi();
    
    for (const { discordId, data } of updates) {
      const serializedData = this.serialize({
        ...data,
        updatedAt: new Date(),
      });
      
      multi.hset(this.key(discordId), serializedData);
    }
    
    await multi.exec();
    this.logger.debug(`Bulk updated ${updates.length} users`);
  }

  // Clean up inactive users (soft delete)
  async markInactive(discordId: string): Promise<void> {
    await this.update(discordId, {
      isActive: false,
      updatedAt: new Date(),
    });
    
    // Remove from active indexes but keep data
    await this.redis.zrem('leaderboard:global:experience', discordId);
    await this.redis.zrem('leaderboard:global:level', discordId);
    
    this.logger.log(`Marked user ${discordId} as inactive`);
  }

  async reactivateUser(discordId: string): Promise<void> {
    const user = await this.findById(discordId);
    if (!user) return;

    await this.update(discordId, {
      isActive: true,
      lastActivityAt: new Date(),
      updatedAt: new Date(),
    });

    // Re-add to leaderboards
    if (user.experience && user.experience > 0) {
      await this.redis.zadd('leaderboard:global:experience', user.experience, discordId);
      const level = Math.floor(user.experience / 1000);
      if (level > 0) {
        await this.redis.zadd('leaderboard:global:level', level, discordId);
      }
    }

    this.logger.log(`Reactivated user ${discordId}`);
  }
}
