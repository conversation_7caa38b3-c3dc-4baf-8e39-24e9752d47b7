import { Injectable, Logger, OnApplicationShutdown, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { RedisService } from './redis.service';
import { GuildRepository } from './repositories/guild.repository';
import { RepositoryFactory } from './repositories/repository.factory';
import { SessionRepository } from './repositories/session.repository';
import { UserRepository } from './repositories/user.repository';
import { RedisHealthCheck, RedisMetrics } from './types/redis-base.types';
import { RedisDataUtil, RedisMigrationUtil } from './utils/redis-migration.util';

/**
 * Main Redis Database Service
 * Provides centralized access to all Redis operations and repositories
 */
@Injectable()
export class RedisDatabaseService implements OnModuleInit, OnApplicationShutdown {
  private readonly logger = new Logger(RedisDatabaseService.name);
  private isInitialized = false;

  constructor(
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
    private readonly repositoryFactory: RepositoryFactory,
    private readonly userRepository: UserRepository,
    private readonly sessionRepository: SessionRepository,
    private readonly guildRepository: GuildRepository,
    private readonly migrationUtil: RedisMigrationUtil,
    private readonly dataUtil: RedisDataUtil
  ) {}

  async onModuleInit(): Promise<void> {
    try {
      await this.initialize();
      this.logger.log('Redis database service initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize Redis database service:', error);
      throw error;
    }
  }

  async onApplicationShutdown(): Promise<void> {
    try {
      await this.shutdown();
      this.logger.log('Redis database service shut down successfully');
    } catch (error) {
      this.logger.error('Error during Redis database service shutdown:', error);
    }
  }

  /**
   * Initialize the Redis database service
   */
  private async initialize(): Promise<void> {
    // Test Redis connection
    const client = this.redisService.getClient();
    await client.ping();
    
    // Run startup tasks
    await this.performStartupTasks();
    
    this.isInitialized = true;
  }

  /**
   * Shutdown the Redis database service
   */
  private async shutdown(): Promise<void> {
    // Perform cleanup tasks
    await this.performShutdownTasks();
    
    // Close Redis connections
    await this.redisService.onModuleDestroy();
    
    this.isInitialized = false;
  }

  /**
   * Perform startup tasks
   */
  private async performStartupTasks(): Promise<void> {
    // Clean up expired sessions
    const expiredSessionsCount = await this.sessionRepository.cleanupExpiredSessions();
    if (expiredSessionsCount > 0) {
      this.logger.log(`Cleaned up ${expiredSessionsCount} expired sessions`);
    }

    // Perform data cleanup
    const cleanupResult = await this.dataUtil.cleanup();
    if (cleanupResult.deletedKeys > 0) {
      this.logger.log(`Cleaned up ${cleanupResult.deletedKeys} expired keys`);
    }
  }

  /**
   * Perform shutdown tasks
   */
  private async performShutdownTasks(): Promise<void> {
    // Any cleanup operations before shutdown
    this.logger.log('Performing shutdown tasks...');
  }

  // Repository Access Methods

  /**
   * Get User repository
   */
  get users(): UserRepository {
    return this.userRepository;
  }

  /**
   * Get Session repository
   */
  get sessions(): SessionRepository {
    return this.sessionRepository;
  }

  /**
   * Get Guild repository
   */
  get guilds(): GuildRepository {
    return this.guildRepository;
  }

  /**
   * Get repository factory for creating custom repositories
   */
  get repositories(): RepositoryFactory {
    return this.repositoryFactory;
  }

  // Utility Methods

  /**
   * Get migration utility
   */
  get migration(): RedisMigrationUtil {
    return this.migrationUtil;
  }

  /**
   * Get data utility
   */
  get data(): RedisDataUtil {
    return this.dataUtil;
  }

  /**
   * Get Redis service for direct access
   */
  get redis(): RedisService {
    return this.redisService;
  }

  // Generic convenience wrappers expected by some feature services
  async findById<T = any>(entityType: string, id: string): Promise<T | null> {
    return this.redisService.findById<T>(entityType, id);
  }

  async findMany<T = any>(
    entityType: string,
    options: { limit?: number; offset?: number; orderBy?: { field: string; direction: 'ASC' | 'DESC' } } = {}
  ): Promise<{ data: T[] }> {
    const data = await this.redisService.find<T>(entityType, {
      limit: options.limit,
      offset: options.offset,
      orderBy: options.orderBy as any,
    } as any);
    return { data };
  }

  async findByIndex<T = any>(
    entityType: string,
    field: string,
    value: unknown,
    options: { limit?: number; sortOrder?: 'asc' | 'desc' } = {}
  ): Promise<T[]> {
    const results = await this.redisService.find<T>(entityType, {
      where: { [field]: value } as any,
      limit: options.limit,
    } as any);

    if (options.sortOrder) {
      const order = options.sortOrder.toLowerCase() === 'desc' ? -1 : 1;
      return [...results].sort((a: any, b: any) => {
        const at = new Date(a?.createdAt ?? 0).getTime();
        const bt = new Date(b?.createdAt ?? 0).getTime();
        return (at - bt) * order;
      });
    }
    return results;
  }

  async search<T = any>(
    entityType: string,
    query: string,
    fields: string[] = [],
    options: { limit?: number } = {}
  ): Promise<T[]> {
    const all = await this.redisService.find<T>(entityType, { limit: options.limit } as any);
    if (!query) return all;
    const q = query.toLowerCase();
    return all.filter((e: any) => {
      const keys = fields.length > 0 ? fields : Object.keys(e ?? {});
      return keys.some((k) => typeof e?.[k] === 'string' && (e[k] as string).toLowerCase().includes(q));
    });
  }

  async create<T = any>(entityType: string, data: any, _opts?: any): Promise<T> {
    return this.redisService.create<T>(entityType, data);
  }

  async update<T = any>(entityType: string, id: string, data: any, _opts?: any): Promise<T | null> {
    return this.redisService.update<T>(entityType, id, data);
  }

  async increment(entityType: string, id: string, field: string, amount: number): Promise<number> {
    const client = this.redisService.getClient();
    const key = `${entityType}:${id}`;
    return client.hincrby(key, field, amount);
  }

  // Health and Monitoring

  /**
   * Perform health check
   */
  async healthCheck(): Promise<RedisHealthCheck> {
    try {
      const client = this.redisService.getClient();
      const start = Date.now();
      
      // Test basic operations
      await client.ping();
      const latency = Date.now() - start;

      // Get Redis info
      const info = await client.info();
      const memory = await client.info('memory');
      const stats = await client.info('stats');

      // Parse info strings
      const parseInfo = (infoStr: string) => {
        const result: { [key: string]: string } = {};
        infoStr.split('\r\n').forEach(line => {
          const [key, value] = line.split(':');
          if (key && value) {
            result[key] = value;
          }
        });
        return result;
      };

      const memoryInfo = parseInfo(memory);
      const statsInfo = parseInfo(stats);

      return {
        status: latency < 100 ? 'healthy' : latency < 500 ? 'degraded' : 'unhealthy',
        latency,
        memory: {
          used: memoryInfo.used_memory_human || '0',
          peak: memoryInfo.used_memory_peak_human || '0',
          fragmentation: parseFloat(memoryInfo.mem_fragmentation_ratio || '0') || 0
        },
        connections: {
          active: parseInt(statsInfo.connected_clients || '0') || 0,
          total: parseInt(statsInfo.total_connections_received || '0') || 0
        },
        uptime: parseInt(parseInfo(info).uptime_in_seconds || '0') || 0,
        version: parseInfo(info).redis_version || 'unknown'
      };
    } catch (error) {
      this.logger.error('Health check failed:', error);
      return {
        status: 'unhealthy'
      };
    }
  }

  /**
   * Get database metrics
   */
  async getMetrics(): Promise<RedisMetrics> {
    try {
      const stats = await this.dataUtil.getStats();
      const client = this.redisService.getClient();
      const info = await client.info('stats');

      const parseInfo = (infoStr: string) => {
        const result: { [key: string]: string } = {};
        infoStr.split('\r\n').forEach(line => {
          const [key, value] = line.split(':');
          if (key && value) {
            result[key] = value;
          }
        });
        return result;
      };

      const statsInfo = parseInfo(info);

      return {
        operations: {
          reads: parseInt(statsInfo.total_reads_processed || '0') || 0,
          writes: parseInt(statsInfo.total_writes_processed || '0') || 0,
          deletes: parseInt(statsInfo.expired_keys || '0') || 0
        },
        performance: {
          averageLatency: 0, // Would need to track this separately
          slowQueries: 0, // Would need to track this separately
          errorRate: 0 // Would need to track this separately
        },
        storage: {
          totalKeys: stats.totalKeys,
          memoryUsage: stats.memoryUsage,
          hitRate: parseFloat(statsInfo.keyspace_hits || '0') /
                  (parseFloat(statsInfo.keyspace_hits || '0') + parseFloat(statsInfo.keyspace_misses || '0')) * 100 || 0
        },
        connections: {
          active: parseInt(statsInfo.connected_clients || '0') || 0,
          peak: parseInt(statsInfo.connected_clients || '0') || 0, // Redis doesn't track peak directly
          refused: parseInt(statsInfo.rejected_connections || '0') || 0
        }
      };
    } catch (error) {
      this.logger.error('Failed to get metrics:', error);
      throw error;
    }
  }

  /**
   * Validate data integrity
   */
  async validateIntegrity(): Promise<{
    valid: boolean;
    issues: Array<{
      key: string;
      issue: string;
      severity: 'low' | 'medium' | 'high';
    }>;
  }> {
    return this.dataUtil.validateIntegrity();
  }

  /**
   * Backup database
   */
  async backup(): Promise<{ [key: string]: any }> {
    return this.dataUtil.backup();
  }

  /**
   * Restore database
   */
  async restore(backup: { [key: string]: any }, clear = false): Promise<void> {
    return this.dataUtil.restore(backup, clear);
  }

  /**
   * Clear all data (dangerous operation)
   */
  async clearAll(): Promise<void> {
    const client = this.redisService.getClient();
    await client.flushall();
    this.logger.warn('All Redis data has been cleared');
  }

  // Transaction and Batch Operations

  /**
   * Execute operations in a transaction
   */
  async transaction<T>(operations: (redis: RedisService) => Promise<T>): Promise<T> {
    return operations(this.redisService);
  }

  /**
   * Check if service is initialized
   */
  get initialized(): boolean {
    return this.isInitialized;
  }

  /**
   * Get connection status
   */
  async getConnectionStatus(): Promise<{
    connected: boolean;
    host: string;
    port: number;
    database: number;
  }> {
    try {
      const client = this.redisService.getClient();
      await client.ping();
      
      return {
        connected: true,
        host: client.options.host || 'unknown',
        port: client.options.port || 0,
        database: client.options.db || 0
      };
    } catch (error) {
      return {
        connected: false,
        host: 'unknown',
        port: 0,
        database: 0
      };
    }
  }

  /**
   * Execute custom Redis command
   */
  async executeCommand(command: string, ...args: any[]): Promise<any> {
    const client = this.redisService.getClient();
    return client.call(command, ...args);
  }

  // Guild convenience methods
  
  /**
   * Get guild (missing method causing build errors)
   */
  async getGuild(guildId: string) {
    try {
      return await this.guildRepository.findByDiscordId(guildId) || await this.guildRepository.findById(guildId);
    } catch (error) {
      this.logger.error(`Failed to get guild ${guildId}:`, error);
      return null;
    }
  }

  /**
   * Update guild settings
   */
  async updateGuildSettings(id: string | number, settings: any) {
    return this.guildRepository.updateSettings(String(id), settings);
  }

  /**
   * Update guild features
   */
  async updateGuildFeatures(id: string | number, features: any) {
    return this.guildRepository.updateFeatures(String(id), features);
  }

  // ==========================================
  // GUILD OPERATIONS
  // ==========================================

  /**
   * Find guild by Discord ID
   */
  async findGuildByDiscordId(discordId: string) {
    try {
      return await this.guildRepository.findByDiscordId(discordId);
    } catch (error) {
      this.logger.error(`Failed to find guild by Discord ID ${discordId}:`, error);
      return null;
    }
  }

  /**
   * Update guild by ID
   */
  async updateGuild(guildId: string, data: any) {
    try {
      return await this.guildRepository.update(guildId, data);
    } catch (error) {
      this.logger.error(`Failed to update guild ${guildId}:`, error);
      return null;
    }
  }

  /**
   * Find guild by ID
   */
  async findGuildById(id: string) {
    try {
      return await this.guildRepository.findById(id);
    } catch (error) {
      this.logger.error(`Failed to find guild by ID ${id}:`, error);
      return null;
    }
  }

  /**
   * Create guild
   */
  async createGuild(guildData: any) {
    try {
      return await this.guildRepository.create(guildData);
    } catch (error) {
      this.logger.error('Failed to create guild:', error);
      throw error;
    }
  }

  // ==========================================
  // USER OPERATIONS
  // ==========================================

  /**
   * Find user by Discord ID
   */
  async findUserByDiscordId(discordId: string) {
    try {
      return await this.userRepository.findByDiscordId(discordId);
    } catch (error) {
      this.logger.error(`Failed to find user by Discord ID ${discordId}:`, error);
      return null;
    }
  }

  /**
   * Update user by ID
   */
  async updateUser(userId: string, data: any) {
    try {
      return await this.userRepository.updateUser(userId, data);
    } catch (error) {
      this.logger.error(`Failed to update user ${userId}:`, error);
      return null;
    }
  }

  /**
   * Create user
   */
  async createUser(userData: any) {
    try {
      return await this.userRepository.createUser(userData);
    } catch (error) {
      this.logger.error('Failed to create user:', error);
      throw error;
    }
  }

  // ==========================================
  // SESSION OPERATIONS
  // ==========================================

  /**
   * Create session
   */
  async createSession(sessionData: any) {
    try {
      return await this.sessionRepository.createSession(sessionData);
    } catch (error) {
      this.logger.error('Failed to create session:', error);
      throw error;
    }
  }

  /**
   * Update session by ID
   */
  async updateSession(sessionId: string, data: any) {
    try {
      const session = await this.sessionRepository.findBySessionId(sessionId);
      if (!session) return null;
      return await this.sessionRepository.update(session.id, data);
    } catch (error) {
      this.logger.error(`Failed to update session ${sessionId}:`, error);
      return null;
    }
  }

  /**
   * Delete session by session ID
   */
  async deleteSession(sessionId: string) {
    try {
      const session = await this.sessionRepository.findBySessionId(sessionId);
      if (!session) return false;
      return await this.sessionRepository.delete(session.id);
    } catch (error) {
      this.logger.error(`Failed to delete session ${sessionId}:`, error);
      return false;
    }
  }

  // ==========================================
  // AGENT OPERATIONS
  // ==========================================

  /**
   * Find agent interactions by user ID
   */
  async findAgentInteractions(userId: string) {
    try {
      // Get all agents for this user first
      const agents = await this.repositoryFactory.createRepository('agent').findByField('userId', userId);
      const interactions = [];
      
      // Get interactions for each agent
      for (const agent of agents) {
        const agentInteractions = await this.repositoryFactory.createRepository('agent').getInteractions(agent.id);
        interactions.push(...agentInteractions);
      }
      
      return interactions;
    } catch (error) {
      this.logger.error(`Failed to find agent interactions for user ${userId}:`, error);
      return [];
    }
  }

  /**
   * Create agent interaction
   */
  async createAgentInteraction(data: any) {
    try {
      const agentRepo = this.repositoryFactory.createRepository('agent');
      return await agentRepo.trackInteraction(data);
    } catch (error) {
      this.logger.error('Failed to create agent interaction:', error);
      throw error;
    }
  }

  /**
   * Update agent memory
   */
  async updateAgentMemory(userId: string, memoryData: any) {
    try {
      // Find agents for this user
      const agents = await this.repositoryFactory.createRepository('agent').findByField('userId', userId);
      const results = [];
      
      for (const agent of agents) {
        await this.repositoryFactory.createRepository('agent').storeMemory({
          agentId: agent.id,
          userId,
          ...memoryData
        });
        results.push(agent.id);
      }
      
      return results;
    } catch (error) {
      this.logger.error(`Failed to update agent memory for user ${userId}:`, error);
      return [];
    }
  }

  // ==========================================
  // ADDITIONAL UTILITY METHODS
  // ==========================================

  /**
   * Find entity by field value (generic)
   */
  async findByField(entityType: string, field: string, value: any) {
    const repository = this.repositoryFactory.createRepository(entityType);
    return repository.findByField(field, value);
  }

  /**
   * Create entity (generic)
   */
  async createEntity(entityType: string, data: any) {
    const repository = this.repositoryFactory.createRepository(entityType);
    return repository.create(data);
  }

  /**
   * Update entity (generic)
   */
  async updateEntity(entityType: string, id: string, data: any) {
    const repository = this.repositoryFactory.createRepository(entityType);
    return repository.update(id, data);
  }

  /**
   * Delete entity (generic)
   */
  async deleteEntity(entityType: string, id: string, hard = false) {
    const repository = this.repositoryFactory.createRepository(entityType);
    return repository.delete(id, hard);
  }

  /**
   * Search entities (generic)
   */
  async searchEntities(entityType: string, query: string, options?: any) {
    const repository = this.repositoryFactory.createRepository(entityType);
    return repository.search(query, options);
  }

  /**
   * Count entities (generic)
   */
  async countEntities(entityType: string, conditions?: any) {
    const repository = this.repositoryFactory.createRepository(entityType);
    return repository.count(conditions);
  }

  /**
   * Batch operations (generic)
   */
  async batchCreate(entityType: string, entities: any[]) {
    const repository = this.repositoryFactory.createRepository(entityType);
    const results = [];
    for (const entity of entities) {
      const result = await repository.create(entity);
      results.push(result);
    }
    return results;
  }

  /**
   * Batch update (generic)
   */
  async batchUpdate(entityType: string, updates: Array<{ id: string; data: any }>) {
    const repository = this.repositoryFactory.createRepository(entityType);
    const results = [];
    for (const update of updates) {
      const result = await repository.update(update.id, update.data);
      results.push(result);
    }
    return results;
  }

  /**
   * Batch delete (generic)
   */
  async batchDelete(entityType: string, ids: string[], hard = false) {
    const repository = this.repositoryFactory.createRepository(entityType);
    const results = [];
    for (const id of ids) {
      const result = await repository.delete(id, hard);
      results.push(result);
    }
    return results;
  }

  // ==========================================
  // ADDITIONAL CONVENIENCE METHODS
  // ==========================================

  /**
   * Find user by ID
   */
  async findUserById(userId: string) {
    try {
      return await this.userRepository.findById(userId);
    } catch (error) {
      this.logger.error(`Failed to find user by ID ${userId}:`, error);
      return null;
    }
  }

  /**
   * Find session by session ID
   */
  async findSessionBySessionId(sessionId: string) {
    try {
      return await this.sessionRepository.findBySessionId(sessionId);
    } catch (error) {
      this.logger.error(`Failed to find session by session ID ${sessionId}:`, error);
      return null;
    }
  }

  /**
   * Find sessions by user ID
   */
  async findSessionsByUserId(userId: string) {
    try {
      return await this.sessionRepository.findByUserId(userId);
    } catch (error) {
      this.logger.error(`Failed to find sessions by user ID ${userId}:`, error);
      return [];
    }
  }

  /**
   * Revoke session
   */
  async revokeSession(sessionId: string) {
    try {
      return await this.sessionRepository.revokeSession(sessionId);
    } catch (error) {
      this.logger.error(`Failed to revoke session ${sessionId}:`, error);
      return null;
    }
  }

  /**
   * Update user activity
   */
  async updateUserActivity(userId: string) {
    try {
      return await this.userRepository.updateLastActivity(userId);
    } catch (error) {
      this.logger.error(`Failed to update user activity for ${userId}:`, error);
      return null;
    }
  }

  /**
   * Update guild activity
   */
  async updateGuildActivity(guildId: string) {
    try {
      return await this.guildRepository.updateActivity(guildId);
    } catch (error) {
      this.logger.error(`Failed to update guild activity for ${guildId}:`, error);
      return null;
    }
  }

  /**
   * Find active sessions
   */
  async findActiveSessions() {
    try {
      return await this.sessionRepository.findActiveSessions();
    } catch (error) {
      this.logger.error('Failed to find active sessions:', error);
      return [];
    }
  }

  /**
   * Find active users
   */
  async findActiveUsers() {
    try {
      return await this.userRepository.findActiveUsers();
    } catch (error) {
      this.logger.error('Failed to find active users:', error);
      return [];
    }
  }

  /**
   * Find active guilds
   */
  async findActiveGuilds() {
    try {
      return await this.guildRepository.findActiveGuilds();
    } catch (error) {
      this.logger.error('Failed to find active guilds:', error);
      return [];
    }
  }

  /**
   * User and guild operations combined
   */
  async findOrCreateUser(userData: any) {
    try {
      let user = await this.findUserByDiscordId(userData.discordId);
      if (!user) {
        user = await this.createUser(userData);
      }
      return user;
    } catch (error) {
      this.logger.error('Failed to find or create user:', error);
      throw error;
    }
  }

  /**
   * Find or create guild
   */
  async findOrCreateGuild(guildData: any) {
    try {
      let guild = await this.findGuildByDiscordId(guildData.discordId);
      if (!guild) {
        guild = await this.createGuild(guildData);
      }
      return guild;
    } catch (error) {
      this.logger.error('Failed to find or create guild:', error);
      throw error;
    }
  }
}