import { Inject, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Redis } from 'ioredis';
import {
    RedisConnectionError,
    RedisError,
    RedisOperationResult
} from './types/redis.types';
import {
    HealthCheckRepository,
    MemoryUsage,
    RedisStats
} from './types/repository.types';

export const REDIS_CONNECTION = Symbol('REDIS_CONNECTION');

@Injectable()
export class DatabaseService implements HealthCheckRepository {
  private readonly logger = new Logger(DatabaseService.name);

  constructor(
    @Inject(REDIS_CONNECTION) private redis: Redis,
    private configService: ConfigService,
  ) {}

  /**
   * Get Redis connection instance
   */
  getRedis(): Redis {
    return this.redis;
  }

  /**
   * Execute Redis command safely with error handling
   */
  async executeCommand<T = any>(
    command: string,
    ...args: (string | number | Buffer)[]
  ): Promise<RedisOperationResult<T>> {
    try {
      const result = await this.redis.call(command, ...args);
      return {
        success: true,
        data: result as T,
      };
    } catch (error) {
      const redisError = this.createRedisError(error, `${command} ${args.join(' ')}`);
      this.logger.error(`Redis command failed: ${command}`, redisError);
      return {
        success: false,
        error: (error as Error).message,
      };
    }
  }

  /**
   * Set key-value pair with optional TTL
   */
  async set(key: string, value: string, ttlSeconds?: number): Promise<RedisOperationResult<string>> {
    try {
      let result: string;
      if (ttlSeconds) {
        result = await this.redis.setex(key, ttlSeconds, value);
      } else {
        result = await this.redis.set(key, value);
      }
      return {
        success: true,
        data: result,
      };
    } catch (error) {
      const redisError = this.createRedisError(error, `SET ${key}`);
      this.logger.error(`Failed to set key: ${key}`, redisError);
      return {
        success: false,
        error: (error as Error).message,
      };
    }
  }

  /**
   * Get value by key
   */
  async get(key: string): Promise<RedisOperationResult<string | null>> {
    try {
      const result = await this.redis.get(key);
      return {
        success: true,
        data: result,
      };
    } catch (error) {
      const redisError = this.createRedisError(error, `GET ${key}`);
      this.logger.error(`Failed to get key: ${key}`, redisError);
      return {
        success: false,
        error: (error as Error).message,
      };
    }
  }

  /**
   * Delete key(s)
   */
  async del(...keys: string[]): Promise<RedisOperationResult<number>> {
    try {
      const result = await this.redis.del(...keys);
      return {
        success: true,
        data: result,
      };
    } catch (error) {
      const redisError = this.createRedisError(error, `DEL ${keys.join(' ')}`);
      this.logger.error(`Failed to delete keys: ${keys.join(', ')}`, redisError);
      return {
        success: false,
        error: (error as Error).message,
      };
    }
  }

  /**
   * Check if key exists
   */
  async exists(...keys: string[]): Promise<RedisOperationResult<number>> {
    try {
      const result = await this.redis.exists(...keys);
      return {
        success: true,
        data: result,
      };
    } catch (error) {
      const redisError = this.createRedisError(error, `EXISTS ${keys.join(' ')}`);
      this.logger.error(`Failed to check existence of keys: ${keys.join(', ')}`, redisError);
      return {
        success: false,
        error: (error as Error).message,
      };
    }
  }

  /**
   * Set hash field
   */
  async hset(key: string, field: string, value: string): Promise<RedisOperationResult<number>> {
    try {
      const result = await this.redis.hset(key, field, value);
      return {
        success: true,
        data: result,
      };
    } catch (error) {
      const redisError = this.createRedisError(error, `HSET ${key} ${field}`);
      this.logger.error(`Failed to set hash field: ${key}.${field}`, redisError);
      return {
        success: false,
        error: (error as Error).message,
      };
    }
  }

  /**
   * Get hash field
   */
  async hget(key: string, field: string): Promise<RedisOperationResult<string | null>> {
    try {
      const result = await this.redis.hget(key, field);
      return {
        success: true,
        data: result,
      };
    } catch (error) {
      const redisError = this.createRedisError(error, `HGET ${key} ${field}`);
      this.logger.error(`Failed to get hash field: ${key}.${field}`, redisError);
      return {
        success: false,
        error: redisError.message,
      };
    }
  }

  /**
   * Get all hash fields
   */
  async hgetall(key: string): Promise<RedisOperationResult<Record<string, string>>> {
    try {
      const result = await this.redis.hgetall(key);
      return {
        success: true,
        data: result,
      };
    } catch (error) {
      const redisError = this.createRedisError(error, `HGETALL ${key}`);
      this.logger.error(`Failed to get all hash fields: ${key}`, redisError);
      return {
        success: false,
        error: redisError.message,
      };
    }
  }

  /**
   * Set multiple hash fields
   */
  async hmset(key: string, hash: Record<string, string>): Promise<RedisOperationResult<string>> {
    try {
      const result = await this.redis.hmset(key, hash);
      return {
        success: true,
        data: result,
      };
    } catch (error) {
      const redisError = this.createRedisError(error, `HMSET ${key}`);
      this.logger.error(`Failed to set multiple hash fields: ${key}`, redisError);
      return {
        success: false,
        error: redisError.message,
      };
    }
  }

  /**
   * Health check implementation
   */
  async checkConnection(): Promise<boolean> {
    try {
      const result = await this.redis.ping();
      return result === 'PONG';
    } catch (error) {
      this.logger.error('Redis health check failed', error);
      return false;
    }
  }

  /**
   * Get Redis statistics
   */
  async getStats(): Promise<RedisStats> {
    try {
      const info = await this.redis.info();
      const lines = info.split('\r\n');
      const stats: Record<string, string> = {};
      
      for (const line of lines) {
        if (line.includes(':')) {
          const [key, value] = line.split(':');
          if (key && value !== undefined) {
            stats[key] = value;
          }
        }
      }

      return {
        connectedClients: parseInt(stats.connected_clients || '0'),
        usedMemory: parseInt(stats.used_memory || '0'),
        totalCommandsProcessed: parseInt(stats.total_commands_processed || '0'),
        keyspaceHits: parseInt(stats.keyspace_hits || '0'),
        keyspaceMisses: parseInt(stats.keyspace_misses || '0'),
        uptime: parseInt(stats.uptime_in_seconds || '0'),
      };
    } catch (error) {
      this.logger.error('Failed to get Redis stats', error);
      throw new RedisError('Failed to get Redis statistics', 'STATS_ERROR');
    }
  }

  /**
   * Get memory usage information
   */
  async getMemoryUsage(): Promise<MemoryUsage> {
    try {
      const info = await this.redis.info('memory');
      const lines = info.split('\r\n');
      const stats: Record<string, string> = {};
      
      for (const line of lines) {
        if (line.includes(':')) {
          const [key, value] = line.split(':');
          if (key && value !== undefined) {
            stats[key] = value;
          }
        }
      }

      const used = parseInt(stats.used_memory || '0');
      const peak = parseInt(stats.used_memory_peak || '0');
      const limit = parseInt(stats.maxmemory || '0') || Infinity;
      
      return {
        used,
        peak,
        limit,
        percentage: limit === Infinity ? 0 : (used / limit) * 100,
      };
    } catch (error) {
      this.logger.error('Failed to get memory usage', error);
      throw new RedisError('Failed to get memory usage', 'MEMORY_ERROR');
    }
  }

  /**
   * Ping Redis server and return latency
   */
  async ping(): Promise<number> {
    try {
      const start = Date.now();
      await this.redis.ping();
      return Date.now() - start;
    } catch (error) {
      this.logger.error('Failed to ping Redis', error);
      throw new RedisConnectionError('Failed to ping Redis server');
    }
  }

  /**
   * Create Redis connection
   */
  async createRedisConnection(): Promise<Redis> {
    this.logger.log('Redis connection ready');
    return this.redis;
  }

  /**
   * Close Redis connection
   */
  async closeConnection(): Promise<void> {
    try {
      await this.redis.quit();
      this.logger.log('Redis connection closed');
    } catch (error) {
      this.logger.error('Failed to close Redis connection', error);
      throw error;
    }
  }

  /**
   * Create standardized Redis error
   */
  private createRedisError(error: any, operation: string): RedisError {
    if (error instanceof RedisError) {
      return error;
    }
    
    if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
      return new RedisConnectionError(`Connection failed during ${operation}: ${error.message}`);
    }
    
    return new RedisError(
      `Redis operation failed: ${error.message}`,
      error.code || 'UNKNOWN_ERROR',
      operation
    );
  }

  /**
   * Legacy compatibility method (deprecated)
   * @deprecated Use appropriate Redis methods instead
   */
  async healthCheck(): Promise<boolean> {
    this.logger.warn('healthCheck() is deprecated, use checkConnection() instead');
    return this.checkConnection();
  }

  /**
   * Legacy SQL query method for backward compatibility
   * @deprecated Use Redis operations instead
   */
  async query(sql: string, params?: any[]): Promise<any> {
    this.logger.warn('query() method is deprecated - this is a Redis-based service, not SQL');
    throw new Error('SQL queries not supported in Redis-based DatabaseService. Use Redis operations instead.');
  }
}
