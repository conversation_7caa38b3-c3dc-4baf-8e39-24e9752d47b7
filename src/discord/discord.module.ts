import { DatabaseModule } from '@/core/database';
import { RedisDatabaseModule } from '@/core/database/redis-database.module';
import { Module } from '@nestjs/common';
import { AgentsService } from '../agents/agents.service';
import { ChannelRoutingService } from '../agents/integration/channel-routing.service';
import { PersonalGrowthSupportService } from '../agents/integration/personal-growth-support.service';
import { IntakeSpecialist } from '../agents/types/intake-specialist';
import { PersonalGrowthCoach } from '../agents/types/personal-growth-coach';
import { ProgressTracker } from '../agents/types/progress-tracker';
import { SecurityModule } from '../core/security/security.module';
import { ServicesModule } from '../core/services/services.module';
import { DiscordCommandsService } from './commands/discord-commands.service';
import { DiscordService } from './discord.service';
import { DiscordEventsService } from './events/discord-events.service';
import { DiscordUtilsService } from './utils/discord-utils.service';

@Module({
  imports: [SecurityModule, DatabaseModule, RedisDatabaseModule, ServicesModule],
  providers: [
    DiscordService,
    // Only include Discord bot services if token is provided
    ...(process.env.DISCORD_TOKEN ? [
      DiscordEventsService,
      DiscordCommandsService,
      PersonalGrowthSupportService,
      ChannelRoutingService,
      AgentsService,
      PersonalGrowthCoach,
      IntakeSpecialist,
      ProgressTracker,
    ] : []),
    DiscordUtilsService,
  ],
  exports: [DiscordService, DiscordUtilsService],
})
export class DiscordModule {}