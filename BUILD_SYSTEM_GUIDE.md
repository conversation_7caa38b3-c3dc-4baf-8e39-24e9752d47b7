# 🏗️ Build System Guide - Discord Bot EnergeX

Comprehensive guide to the preflight build system with validation, optimization, and deployment preparation.

## 🚀 Quick Start

### Development Build
```bash
# Quick development build
pnpm run build

# Preflight development build with validation
pnpm run build:preflight:dev

# Development build pipeline
pnpm run build:pipeline --target=dev
```

### Production Build
```bash
# Production build with full validation
pnpm run build:preflight:prod

# Complete production pipeline with tests
pnpm run build:pipeline:prod

# Optimized production build
pnpm run build && pnpm run build:optimize
```

### Docker Build
```bash
# Docker-optimized build
pnpm run build:pipeline:docker

# Build Docker image with testing
pnpm run build:docker:test

# Complete Docker build pipeline
./scripts/docker-build.sh --tag=my-bot --test
```

## 📋 Build Commands Reference

### Core Build Commands
| Command | Description | Use Case |
|---------|-------------|----------|
| `pnpm run build` | Standard NestJS build | Basic development |
| `pnpm run build:clean` | Clean build (remove dist/) | Fresh build needed |
| `pnpm run build:force` | Force build (ignore errors) | Emergency builds |

### Preflight Build Commands
| Command | Description | Use Case |
|---------|-------------|----------|
| `pnpm run build:preflight` | Full preflight build (dev) | Development validation |
| `pnpm run build:preflight:prod` | Production preflight build | Pre-deployment |
| `pnpm run build:preflight:dev` | Development preflight build | Local development |

### Build Pipeline Commands
| Command | Description | Use Case |
|---------|-------------|----------|
| `pnpm run build:pipeline` | Complete build pipeline | Comprehensive build |
| `pnpm run build:pipeline:prod` | Production pipeline + tests | Production deployment |
| `pnpm run build:pipeline:docker` | Docker-optimized pipeline | Container deployment |

### Build Validation Commands
| Command | Description | Use Case |
|---------|-------------|----------|
| `pnpm run build:validate` | Validate build artifacts | Post-build verification |
| `pnpm run build:validate:strict` | Strict validation + report | Production validation |

### Build Optimization Commands
| Command | Description | Use Case |
|---------|-------------|----------|
| `pnpm run build:analyze` | Analyze build for optimization | Performance review |
| `pnpm run build:optimize` | Apply build optimizations | Size reduction |

### Docker Build Commands
| Command | Description | Use Case |
|---------|-------------|----------|
| `pnpm run build:docker` | Build Docker image | Container creation |
| `pnpm run build:docker:test` | Build + test Docker image | Container validation |

## 🔍 Build Validation Levels

### Level 1: Quick Validation
- ✅ File structure check
- ✅ Basic syntax validation
- ✅ Entry point verification
- **Duration**: ~10 seconds

### Level 2: Standard Validation
- ✅ All Level 1 checks
- ✅ TypeScript compilation
- ✅ ESLint validation
- ✅ Module resolution test
- **Duration**: ~30 seconds

### Level 3: Comprehensive Validation
- ✅ All Level 2 checks
- ✅ Build execution test
- ✅ Performance analysis
- ✅ Security scan
- ✅ Dependency audit
- **Duration**: ~60 seconds

### Level 4: Production Validation
- ✅ All Level 3 checks
- ✅ Optimization analysis
- ✅ Deployment readiness
- ✅ Container testing (if Docker)
- ✅ Integration tests
- **Duration**: ~120 seconds

## 🎯 Build Targets

### Development Target
```bash
# Optimized for fast iteration
pnpm run build:preflight:dev
```
- **Features**: Source maps, fast compilation, hot reload support
- **Validation**: Basic checks, TypeScript compilation
- **Output**: Unminified, with debugging info
- **Use Case**: Local development, debugging

### Production Target
```bash
# Optimized for deployment
pnpm run build:preflight:prod
```
- **Features**: Webpack bundling, minification, tree shaking
- **Validation**: Comprehensive checks, security scan
- **Output**: Minified, optimized, no source maps
- **Use Case**: Cloud deployment, production servers

### Docker Target
```bash
# Optimized for containers
pnpm run build:pipeline:docker
```
- **Features**: Multi-stage build, minimal footprint
- **Validation**: Container-specific checks, security scan
- **Output**: Container-ready, optimized layers
- **Use Case**: Docker deployment, Kubernetes

## 🔧 Build Configuration

### TypeScript Configuration
The build system uses `tsconfig.json` with these key settings:
- **Target**: ES2020 for modern Node.js compatibility
- **Module**: CommonJS for NestJS compatibility
- **Strict**: Enabled for type safety
- **Source Maps**: Environment-dependent
- **Path Mapping**: Configured for clean imports

### NestJS Configuration
Build behavior is controlled by `nest-cli.json`:
- **Webpack**: Enabled for production builds
- **Delete Output**: Clean builds
- **Entry File**: main.ts
- **Source Root**: src/

### Build Optimization
Controlled by `build.config.json`:
- **Bundle Analysis**: Size and dependency tracking
- **Performance Metrics**: Build time and output size
- **Security Validation**: Sensitive data detection
- **Platform Optimization**: Target-specific settings

## 📊 Build Metrics & Monitoring

### Tracked Metrics
- **Build Duration**: Total compilation time
- **Bundle Size**: Output size in MB
- **File Count**: Number of generated files
- **Dependency Size**: Large dependency detection
- **Error Count**: Compilation and validation errors

### Performance Thresholds
- **Build Time**: Warn >120s, Fail >300s
- **Bundle Size**: Warn >50MB, Fail >100MB
- **Large Files**: Warn >1MB per file
- **Memory Usage**: Monitor during build

### Reporting
- **JSON Reports**: Machine-readable build metrics
- **HTML Reports**: Human-readable analysis (with --analyze)
- **CI Integration**: GitHub Actions workflow
- **Monitoring**: Prometheus metrics (if enabled)

## 🐳 Docker Build Optimization

### Multi-Stage Build Process
1. **Dependencies Stage**: Install and cache dependencies
2. **Build Stage**: Compile TypeScript and bundle
3. **Runtime Stage**: Minimal production image

### Optimization Techniques
- **Layer Caching**: Efficient Docker layer utilization
- **Dependency Separation**: Separate dev and prod dependencies
- **File Permissions**: Proper security settings
- **Health Checks**: Built-in container health monitoring

### Security Features
- **Non-root User**: Runs as nestjs user (UID 1001)
- **Minimal Base**: Alpine Linux for smaller attack surface
- **No Secrets**: No build-time secrets in layers
- **Vulnerability Scanning**: Basic security validation

## 🔄 CI/CD Integration

### GitHub Actions Workflow
The `.github/workflows/preflight-build.yml` provides:
- **Multi-target builds**: dev, prod, docker
- **Parallel execution**: Faster pipeline completion
- **Artifact management**: Build outputs and reports
- **Security scanning**: Dependency and code analysis
- **Deployment preparation**: Ready-to-deploy packages

### Integration Examples

#### GitLab CI
```yaml
build:
  stage: build
  script:
    - npm install -g pnpm
    - pnpm install
    - pnpm run build:pipeline:prod
  artifacts:
    paths:
      - dist/
      - "*.json"
```

#### Jenkins
```groovy
pipeline {
  agent any
  stages {
    stage('Build') {
      steps {
        sh 'pnpm run build:pipeline:prod'
      }
    }
  }
}
```

## 🚨 Troubleshooting

### Common Build Issues

#### "TypeScript compilation failed"
```bash
# Check for errors
npx tsc --noEmit

# Auto-fix common issues
pnpm run lint:fix

# Force build with errors
pnpm run build:force
```

#### "Build artifacts missing"
```bash
# Clean and rebuild
pnpm run build:clean

# Check build output
ls -la dist/src/
```

#### "Docker build failed"
```bash
# Run Docker preflight
./scripts/docker-preflight.sh

# Build with verbose output
./scripts/docker-build.sh --no-cache --verbose
```

#### "Build size too large"
```bash
# Analyze build
pnpm run build:analyze

# Apply optimizations
pnpm run build:optimize

# Check dependencies
pnpm run build:pipeline --target=prod
```

### Performance Issues

#### Slow Build Times
- Enable incremental compilation
- Use build caching
- Optimize TypeScript configuration
- Review large dependencies

#### Large Bundle Size
- Enable tree shaking
- Remove unused dependencies
- Optimize imports
- Use dynamic imports for large modules

## 🎯 Best Practices

### Development
- Use `build:preflight:dev` for daily development
- Enable source maps for debugging
- Use watch mode for hot reload
- Run quick validation frequently

### Production
- Always use `build:preflight:prod` before deployment
- Enable all optimizations
- Remove source maps and debug code
- Validate security configuration

### Docker
- Use multi-stage builds
- Optimize layer caching
- Test images before deployment
- Monitor container resource usage

### CI/CD
- Run preflight builds on every commit
- Cache dependencies between builds
- Parallelize validation steps
- Generate and store build reports

## 📈 Performance Optimization

### Build Speed
- **Incremental Builds**: Only rebuild changed files
- **Dependency Caching**: Cache node_modules between builds
- **Parallel Processing**: Use multiple CPU cores
- **Skip Checks**: Use --skip flags for faster iteration

### Bundle Size
- **Tree Shaking**: Remove unused code
- **Code Splitting**: Split large bundles
- **Compression**: Gzip/Brotli compression
- **Asset Optimization**: Optimize static assets

### Runtime Performance
- **Dead Code Elimination**: Remove unreachable code
- **Minification**: Reduce file sizes
- **Module Bundling**: Optimize module loading
- **Lazy Loading**: Load modules on demand

## 🔐 Security Considerations

### Build Security
- **Dependency Scanning**: Check for vulnerabilities
- **Sensitive Data**: Prevent secrets in build output
- **Code Analysis**: Static security analysis
- **Container Security**: Secure Docker images

### Deployment Security
- **Environment Isolation**: Separate dev/prod builds
- **Access Control**: Secure build artifacts
- **Audit Trail**: Track build and deployment history
- **Compliance**: Meet security requirements

The build system provides comprehensive validation, optimization, and deployment preparation to ensure your Discord bot is reliable, secure, and performant across all environments.
